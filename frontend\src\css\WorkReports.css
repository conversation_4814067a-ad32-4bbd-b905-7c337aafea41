/* Reset c<PERSON> bản */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Variables */
:root {
  --primary-color: #2563eb; /* <PERSON><PERSON><PERSON> chủ đạo xanh dương */
  --primary-dark: #1e40af;
  --accent-color: #1e3a8a; /* <PERSON><PERSON><PERSON> nhấn mạnh */
  --light-bg: #f8fafc;
  --mid-bg: #e2e8f0;
  --white: #ffffff;
  --text-color: #1f2937; /* <PERSON><PERSON><PERSON> chữ chính */
  --secondary-text: #64748b; /* <PERSON><PERSON><PERSON> chữ phụ */
  --border-color: #d1d5db;
  --shadow-light: rgba(0, 0, 0, 0.08);
  --shadow-medium: rgba(0, 0, 0, 0.12);
  --success-color: #059669;
  --danger-color: #dc2626;
  --stt-bg: #6c757d;
}

/* Typography */
body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-color);
  background: var(--light-bg);
  line-height: 1.6;
}

/* STT Badge */
.stt-badge {
  display: inline-block;
  background-color: var(--stt-bg);
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 600;
}

/* STT Cell in Tables */
.stt-cell {
  text-align: center;
  font-weight: 600;
  background-color: rgba(108, 117, 125, 0.1);
}

/* Role Cell in Tables */
.role-cell {
  text-align: center;
  vertical-align: middle;
  min-width: 100px;
}

.role-badge {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.4rem 0.8rem;
  border-radius: 0.5rem;
}

/* Report Author Info */
.report-author {
  font-style: italic;
  margin-top: 0.25rem;
}

/* Total Count Display */
.total-count {
  background-color: #f3f4f6;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 1rem;
  color: #374151;
  border: 1px solid #d1d5db;
  display: inline-block;
  font-weight: 600;
}

.total-count span {
  font-size: 0.9rem;
  color: #6b7280;
  font-style: normal;
  margin-left: 8px;
}

.total-count strong {
  color: #1f2937;
  font-weight: 700;
}

/* Completion Percentage Styles */
.completion-percentage-select {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #cbd5e1;
  border-radius: 8px;
  padding: 8px 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.completion-percentage-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  background: white;
}

.completion-percentage-cell {
  text-align: center;
  vertical-align: middle;
  padding: 8px 12px;
  min-width: 120px;
}

.completion-percentage-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.completion-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.85rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  color: #475569;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.percentage-text {
  font-family: 'Inter', monospace;
  letter-spacing: 0.5px;
}

.progress-bar-container {
  width: 80px;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar-fill {
  height: 100%;
  border-radius: 3px;
  transition: all 0.4s ease;
  background: linear-gradient(90deg, currentColor 0%, currentColor 100%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.task-completion-field {
  margin-top: 12px;
}

.task-completion-field .form-label {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.task-completion-field .form-label::before {
  content: "📊";
  font-size: 0.9rem;
}

/* Responsive Design for Completion Percentage */
@media (max-width: 768px) {
  .completion-percentage-cell {
    min-width: 100px;
    padding: 6px 8px;
  }

  .completion-percentage-display {
    gap: 2px;
  }

  .completion-badge {
    min-width: 45px;
    padding: 3px 6px;
    font-size: 0.8rem;
  }

  .progress-bar-container {
    width: 60px;
    height: 5px;
  }
}

/* Enhanced styling for different completion levels */
.completion-percentage-display[data-completion="100"] .completion-badge {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-color: #86efac;
  color: #166534;
}

.completion-percentage-display[data-completion="90"] .completion-badge,
.completion-percentage-display[data-completion="80"] .completion-badge {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #6ee7b7;
  color: #047857;
}

.completion-percentage-display[data-completion="70"] .completion-badge,
.completion-percentage-display[data-completion="60"] .completion-badge {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #fcd34d;
  color: #92400e;
}

.completion-percentage-display[data-completion="50"] .completion-badge,
.completion-percentage-display[data-completion="40"] .completion-badge {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
  border-color: #fb923c;
  color: #c2410c;
}

.completion-percentage-display[data-completion="30"] .completion-badge,
.completion-percentage-display[data-completion="20"] .completion-badge,
.completion-percentage-display[data-completion="10"] .completion-badge {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  border-color: #f87171;
  color: #dc2626;
}

.completion-percentage-display[data-completion="0"] .completion-badge {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #cbd5e1;
  color: #64748b;
}

/* Modal Styles */
.modal-dialog {
  max-height: 90vh;
  margin: 1.75rem auto;
}

.modal-content {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-body {
  overflow-y: auto;
  flex-grow: 1;
  max-height: calc(90vh - 200px); /* Trừ đi chiều cao header và footer */
}

.modal-footer {
  background: var(--white);
  border-top: 1px solid var(--border-color);
  padding: 12px;
  border-radius: 0 0 8px 8px;
  margin-top: 20px; /* Khoảng cách với nội dung phía trên */
  position: relative; /* Đảm bảo nằm trong flow bình thường */
}

/* Đảm bảo footer luôn ở cuối modal-body */
.modal-body:has(.modal-footer) {
  display: flex;
  flex-direction: column;
}

.modal-body .modal-footer {
  margin-top: auto; /* Đẩy footer xuống cuối cùng */
  order: 999; /* Đảm bảo footer luôn ở cuối */
}

/* Để comments-section có đủ không gian */
.comments-section {
  margin-bottom: 20px; /* Khoảng cách với footer */
  flex-grow: 1; /* Cho phép mở rộng */
}

/* Action Buttons Modal */
.action-buttons-modal {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.action-buttons-modal .btn {
  margin: 0;
  flex-shrink: 0;
  white-space: nowrap;
}

/* Responsive modal for mobile */
@media (max-width: 767px) {
  .modal-dialog {
    margin: 1rem;
    max-width: calc(100% - 2rem);
    max-height: calc(100vh - 2rem);
  }
  
  .modal-body {
    max-height: calc(100vh - 180px);
    padding: 15px;
  }
  
  .action-buttons-modal {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .action-buttons-modal .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Container chính */
.work-reports-page {
  padding: 40px 20px;
  min-height: 100vh;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.page-header {
  margin-top: 55px;
  text-align: center;
  margin-bottom: 40px;
  background: var(--white);
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--shadow-light);
  border-left: 5px solid var(--primary-color);
}

.page-header h1 {
  font-size: 2.25rem;
  font-weight: 700;
  color: #000;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.page-header p {
  font-size: 1.1rem;
  color: var(--secondary-text);
  font-weight: 400;
}

/* Responsive page header */
@media (max-width: 767px) {
  .page-header {
    margin-top: 30px;
    padding: 20px 15px;
    margin-bottom: 25px;
  }
  
  .page-header h1 {
    font-size: 1.75rem;
    margin-bottom: 5px;
  }
  
  .page-header p {
    font-size: 0.95rem;
  }
}

/* Tabs Navigation */
.tabs-container .nav-tabs {
  border-bottom: 2px solid var(--border-color);
  margin-bottom: 30px;
  justify-content: center;
}

.tabs-container .nav-link {
  color: var(--secondary-text);
  font-weight: 600;
  padding: 10px 20px;
  border: none;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
}

.tabs-container .nav-link.active {
  color: var(--primary-color);
  background: var(--white);
  border-bottom: 3px solid var(--primary-color);
}

.tabs-container .nav-link:hover {
  color: var(--primary-dark);
}

/* Responsive tabs for mobile */
@media (max-width: 767px) {
  .tabs-container .nav-tabs {
    margin-bottom: 20px;
    flex-wrap: nowrap;
    overflow-x: auto;
    justify-content: flex-start;
    padding-bottom: 5px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
  }
  
  .tabs-container .nav-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }
  
  .tabs-container .nav-link {
    white-space: nowrap;
    padding: 8px 15px;
    font-size: 0.9rem;
  }
  
  .tabs-container .nav-link.active {
    border-bottom-width: 2px;
  }
}

/* Form nhập liệu */
.work-report-form {
  background: var(--white);
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 6px 15px var(--shadow-light);
  margin-bottom: 40px;
  border-left: 5px solid var(--primary-color);
  transition: all 0.3s ease;
}

.work-report-form:hover {
  box-shadow: 0 8px 20px var(--shadow-medium);
}

.work-report-form h5 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 25px;
  font-weight: 600;
  border-bottom: 2px solid var(--mid-bg);
  padding-bottom: 8px;
}

.work-report-form .form-group {
  margin-bottom: 20px;
}

.work-report-form .form-label {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 6px;
  display: block;
}

.work-report-form .form-control,
.work-report-form .form-select {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  font-size: 1rem;
  background: var(--white);
  transition: all 0.3s ease;
}

.work-report-form .form-control:focus,
.work-report-form .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 8px rgba(37, 99, 235, 0.2);
  outline: none;
}

.work-report-form .form-select[multiple] {
  height: 150px; /* Chiều cao cố định cho select nhiều lựa chọn */
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 25px;
}

.work-report-form .btn-primary,
.work-report-form .btn-submit {
  background: var(--primary-color);
  border: none;
  padding: 12px 25px;
  font-weight: 600;
  border-radius: 8px;
  color: var(--white);
  transition: all 0.3s ease;
  width: 200px;
  height: 50px;
  font-size: 18px;
  text-align: center;
}

.work-report-form .btn-primary:hover,
.work-report-form .btn-submit:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(37, 99, 235, 0.3);
}

.work-report-form .btn-danger {
  background: var(--danger-color);
  border: none;
  padding: 8px 15px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.work-report-form .btn-danger:hover {
  background: #b91c1c;
  transform: translateY(-2px);
}

/* Card công việc */
.work-report-form .card {
  border: 1px solid var(--border-color);
  border-radius: 10px;
  box-shadow: 0 2px 8px var(--shadow-light);
  background: var(--light-bg);
}

/* Compact form styles */
.compact-form .compact-tasks-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  margin-bottom: 20px;
}

.compact-form .compact-task-card {
  border: 1px solid var(--border-color);
  border-radius: 10px;
  box-shadow: 0 2px 8px var(--shadow-light);
  background: var(--white);
  transition: all 0.3s ease;
  margin: 0 0 20px 0;
  width: 100%;
}

.compact-form .compact-task-card:hover {
  box-shadow: 0 4px 12px var(--shadow-medium);
  transform: translateY(-2px);
}

.compact-form .compact-task-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid var(--border-color);
  padding: 10px 15px;
}

/* Horizontal layout for card body */
.compact-form .horizontal-card-layout {
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Main info section with dates and project */
.compact-form .task-main-info {
  display: flex;
  gap: 20px;
  width: 100%;
}

/* Dates container */
.compact-form .task-dates-container {
  display: flex;
  gap: 15px;
  flex: 3;
}

/* Project info container */
.compact-form .task-project-info {
  display: flex;
  gap: 15px;
  flex: 2;
}

/* Task details container */
.compact-form .task-details-container {
  display: flex;
  gap: 20px;
  width: 100%;
}

/* Content and progress section */
.compact-form .task-content-section {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* People section */
.compact-form .task-people-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Individual field styling */
.compact-form .task-field {
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Content field styling */
.compact-form .task-content-field,
.compact-form .task-progress-field {
  display: flex;
  flex-direction: column;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .compact-form .task-main-info,
  .compact-form .task-details-container {
    flex-direction: column;
    gap: 15px;
  }
  
  .compact-form .task-dates-container {
    flex-wrap: wrap;
  }
  
  .compact-form .task-field {
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .compact-form .task-dates-container,
  .compact-form .task-project-info {
    flex-direction: column;
  }
  
  /* Responsive styles for action buttons */
  .action-buttons .btn,
  .card-body .btn,
  .mb-3.card .btn,
  .card.mb-3 .btn {
    padding: 8px 12px;
    font-size: 0.95rem;
    min-width: 80px;
    margin-bottom: 5px;
  }
  
  .report-list .card .card-header,
  .mb-3.card .card-header,
  .card.mb-3 .card-header {
    flex-direction: column;
  }
  
  .report-list .card .card-header .action-buttons,
  .mb-3.card .card-actions,
  .card.mb-3 .card-actions,
  .mb-3.card .card-footer,
  .card.mb-3 .card-footer,
  .mb-3.card .action-buttons,
  .card.mb-3 .action-buttons {
    margin-top: 10px;
    justify-content: flex-start;
    width: 100%;
  }
  
  .expand-collapse-buttons .btn,
  .mb-3.card .expand-btn,
  .card.mb-3 .expand-btn,
  .mb-3.card .collapse-btn,
  .card.mb-3 .collapse-btn {
    min-width: 100px;
    font-size: 0.95rem;
    padding: 7px 12px;
  }
}

.compact-form .accordion-button {
  padding: 10px 15px;
  font-size: 0.9rem;
  background-color: #f8f9fa;
  color: var(--text-color);
  font-weight: 600;
}

.compact-form .accordion-button:not(.collapsed) {
  background-color: #e9ecef;
  color: var(--primary-color);
}

.compact-form .accordion-button:focus {
  box-shadow: none;
  border-color: var(--border-color);
}

.compact-form .accordion-body {
  padding: 15px;
  background-color: #f8f9fa;
}

/* Additional styles for compact form layout */
.compact-form .row {
  margin-bottom: 10px;
  width: 100%;
}

.compact-form .form-label {
  font-size: 0.85rem;
  margin-bottom: 4px;
  font-weight: 600;
  color: #444;
}

.compact-form .form-control, 
.compact-form .form-select {
  font-size: 0.95rem;
  padding: 6px 10px;
  height: auto;
  border-radius: 6px;
}

.compact-form .task-field .form-control,
.compact-form .task-field .form-select {
  min-height: 38px;
}

.compact-form textarea.form-control {
  min-height: 80px;
}

.compact-form .task-content-field textarea.form-control,
.compact-form .task-progress-field textarea.form-control {
  min-height: 120px;
}

/* Force horizontal layout */
.work-report-form.compact-form {
  display: block;
  width: 100%;
}

/* Form tìm kiếm - Standardized Filter Styles */
.employee-filter-section,
.manager-section .card,
.horizontal-filter-section {
  background: var(--white);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 6px 15px var(--shadow-light);
  margin-bottom: 30px;
  border-left: 5px solid var(--primary-color);
  transition: all 0.3s ease;
  width: 100%;
}

.employee-filter-section:hover,
.manager-section .card:hover,
.horizontal-filter-section:hover {
  box-shadow: 0 8px 20px var(--shadow-medium);
}

.employee-filter-section h2,
.manager-section h2,
.horizontal-filter-section h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
  border-bottom: 2px solid var(--mid-bg);
  padding-bottom: 8px;
}

/* Standardized form groups */
.employee-filter-section .form-group,
.manager-section .form-group,
.horizontal-filter-section .form-group {
  margin-bottom: 15px;
  min-width: 180px;
}

/* Standardized form labels */
.employee-filter-section .form-label,
.manager-section .form-label,
.horizontal-filter-section .form-label,
.horizontal-filter-group .form-label {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 6px;
  font-size: 0.9rem;
  display: block;
}

/* Standardized form controls */
.employee-filter-section .form-control,
.employee-filter-section .form-select,
.manager-section .form-control,
.manager-section .form-select,
.horizontal-filter-section .form-control,
.horizontal-filter-section .form-select,
.horizontal-filter-group .form-control,
.horizontal-filter-group .form-select {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  height: 38px;
  width: 100%;
}

.employee-filter-section .form-control:focus,
.employee-filter-section .form-select:focus,
.manager-section .form-control:focus,
.manager-section .form-select:focus,
.horizontal-filter-section .form-control:focus,
.horizontal-filter-section .form-select:focus,
.horizontal-filter-group .form-control:focus,
.horizontal-filter-group .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 8px rgba(37, 99, 235, 0.2);
}

/* Enhanced filter sections for mobile */
@media (max-width: 767px) {
  .employee-filter-section,
  .manager-section .card,
  .horizontal-filter-section {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 10px;
  }
  
  .employee-filter-section h2,
  .manager-section h2,
  .horizontal-filter-section h2 {
    font-size: 1.2rem;
    margin-bottom: 12px;
    padding-bottom: 6px;
  }
  
  .horizontal-filter-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .horizontal-filter-container .form-group {
    width: 100%;
  }
  
  .filter-button-container {
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
  }
  
  .filter-button-container .btn {
    flex: 1;
    margin: 0 5px;
    padding: 8px 10px;
    font-size: 0.9rem;
  }
  
  /* Collapsible filter sections for mobile */
  .filter-section-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background: none;
    border: none;
    text-align: left;
    padding: 0;
    margin-bottom: 10px;
    cursor: pointer;
  }
  
  .filter-section-toggle h2 {
    margin: 0;
    padding: 0;
    border: none;
  }
  
  .filter-section-toggle i {
    transition: transform 0.3s ease;
  }
  
  .filter-section-toggle.collapsed i {
    transform: rotate(180deg);
  }
  
  .filter-section-content {
    overflow: hidden;
    transition: max-height 0.3s ease;
  }
  
  .filter-section-content.collapsed {
    max-height: 0;
  }
}

/* Standardized horizontal filter layout */
.horizontal-filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: flex-end;
  width: 100%;
  margin-bottom: 15px;
}

/* Filter button container */
.filter-button-container {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
  width: 100%;
}

/* Apply and Reset buttons */
.filter-button-container .btn {
  min-width: 120px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Standardized filter groups */
.horizontal-filter-group {
  flex: 1;
  min-width: 180px;
  max-width: 250px;
}

/* Standardized filter buttons */
.horizontal-filter-buttons {
  display: flex;
  gap: 10px;
  margin-top: 24px;
  justify-content: flex-end;
}

/* Standardized filter action buttons */
.horizontal-filter-section .btn-primary,
.horizontal-filter-section .btn-secondary,
.horizontal-filter-section .btn-success,
.horizontal-filter-section .btn-outline-secondary,
.horizontal-filter-section .btn-outline-primary {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  font-size: 0.95rem;
  border-radius: 8px;
}

/* Export buttons container */
.export-buttons {
  display: flex;
  align-items: center;
}

/* Filter header with title and buttons */
.d-flex.justify-content-between.align-items-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 15px;
}

/* Standardized filter title */
.filter-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--primary-color);
}

/* Ensure consistent spacing in all filter sections */
.horizontal-filter-section {
  padding: 20px;
  margin-bottom: 30px;
}

/* Ensure consistent form structure */
.horizontal-filter-section form {
  width: 100%;
}

/* Ensure consistent spacing between filter sections */
.tab-content > .tab-pane {
  padding-top: 15px;
}

/* Standardized date filters */
.horizontal-date-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
  width: 100%;
  margin-bottom: 15px;
}

/* Date filter inputs */
.horizontal-date-filters .horizontal-filter-group {
  flex: 1;
  min-width: 180px;
  max-width: 250px;
}

/* Month/Year filter container */
.month-year-filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  width: 100%;
  margin-bottom: 15px;
}

/* Month/Year filter groups */
.month-year-filter-container .horizontal-filter-group {
  flex: 1;
  min-width: 180px;
  max-width: 250px;
}

/* Standardized header with title and buttons */
.d-flex.justify-content-between.align-items-center.mb-3 {
  margin-bottom: 15px !important;
  width: 100%;
}

/* Responsive adjustments for horizontal filters */
@media (max-width: 992px) {
  .horizontal-filter-group {
    min-width: 160px;
  }
}

@media (max-width: 768px) {
  .horizontal-filter-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .horizontal-filter-group {
    width: 100%;
    max-width: 100%;
  }
  
  .horizontal-filter-buttons {
    width: 100%;
    justify-content: flex-end;
  }
}

/* Danh sách báo cáo */
.report-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Simple and clean reports-header-controls */
.reports-header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
  background: #ffffff;
  padding: 15px 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-wrap: wrap;
  gap: 10px;
}

.reports-header-controls .total-count {
  font-size: 1rem;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.reports-header-controls .total-count strong {
  color: #1f2937;
  font-weight: 700;
}

.reports-header-controls .scroll-hint {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.reports-header-controls .expand-collapse-buttons {
  display: flex;
  gap: 8px;
}

.reports-header-controls .expand-collapse-buttons .btn {
  padding: 6px 12px;
  font-size: 0.9rem;
  border-radius: 6px;
}

/* Responsive cho reports-header-controls */
@media (max-width: 768px) {
  .reports-header-controls {
    flex-direction: column;
    gap: 12px;
    padding: 12px 15px;
  }
  
  .reports-header-controls .total-count {
    font-size: 0.9rem;
    padding: 6px 10px;
  }
  
  .reports-header-controls .expand-collapse-buttons {
    width: 100%;
    justify-content: center;
  }
  
  .reports-header-controls .expand-collapse-buttons .btn {
    flex: 1;
    max-width: 150px;
    font-size: 0.85rem;
    padding: 5px 8px;
  }
}

/* Card style mới cho tab Quản lý báo cáo - Enhanced for better visibility */
.report-list .card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.12);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid #e2e8f0;
  border-left: 6px solid var(--primary-color);
  display: flex;
  flex-direction: column;
  padding: 0;
  width: 100%;
  height: auto;
  margin-bottom: 25px;
  position: relative;
}

.report-list .card .card-header {
  padding: 20px 25px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #d1d5db;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

/* Enhanced action buttons container in card header */
.report-list .card .card-header .action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: flex-end;
  min-width: 200px;
}

.report-list .card .card-header > div:first-child {
  flex: 1;
}

.report-list .card .card-header .stt-badge {
  margin-bottom: 8px;
  display: inline-block;
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 700;
  box-shadow: 0 2px 6px rgba(107, 114, 128, 0.3);
  border: 1px solid #374151;
}

/* Enhanced user info display */
.report-list .card-header .user-info {
  background: rgba(37, 99, 235, 0.05);
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid var(--primary-color);
  margin-bottom: 8px;
}

.report-list .card-header .week-info {
  background: rgba(5, 150, 105, 0.05);
  padding: 6px 10px;
  border-radius: 6px;
  border-left: 3px solid #059669;
  font-weight: 600;
  color: #047857;
}

.report-list .card .card-body {
  padding: 25px 30px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 0 0 16px 16px;
}

/* Style cho các task trong card body - Enhanced visibility */
.report-list .card .card-body .task-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  padding: 18px 20px;
  margin-bottom: 15px;
  border: 2px solid #e2e8f0;
  border-left: 4px solid var(--primary-color);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

/* Enhanced buttons in task items */
.report-list .card .card-body .task-item .btn {
  margin-top: 8px;
  margin-right: 5px;
}

/* Enhanced action buttons container in mb-3 cards */
.mb-3.card .card-actions,
.card.mb-3 .card-actions,
.mb-3.card .card-footer,
.card.mb-3 .card-footer,
.mb-3.card .action-buttons,
.card.mb-3 .action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 10px 15px;
  justify-content: flex-end;
  align-items: center;
  margin-top: 5px;
}

/* Enhanced buttons in mb-3 cards */
.mb-3.card .btn,
.card.mb-3 .btn {
  padding: 8px 16px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.3s ease;
  margin: 0 5px 5px 0;
  min-width: 90px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.mb-3.card .btn i,
.card.mb-3 .btn i {
  margin-right: 5px;
  font-size: 1.1rem;
}

.mb-3.card .btn:hover,
.card.mb-3 .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mb-3.card .btn-outline-primary,
.card.mb-3 .btn-outline-primary {
  border: 2px solid #6b8cce;
  color: #3b5998;
  background-color: rgba(59, 89, 152, 0.05);
}

.mb-3.card .btn-outline-primary:hover,
.card.mb-3 .btn-outline-primary:hover {
  background: #4267b2;
  color: var(--white);
  border-color: #3b5998;
}

.mb-3.card .btn-outline-danger,
.card.mb-3 .btn-outline-danger {
  border: 2px solid #e57373;
  color: #c62828;
  background-color: rgba(198, 40, 40, 0.05);
}

.mb-3.card .btn-outline-danger:hover,
.card.mb-3 .btn-outline-danger:hover {
  background: #d32f2f;
  color: var(--white);
  border-color: #c62828;
}

.mb-3.card .btn-outline-success,
.card.mb-3 .btn-outline-success {
  border: 2px solid #81c784;
  color: #2e7d32;
  background-color: rgba(46, 125, 50, 0.05);
}

.mb-3.card .btn-outline-success:hover,
.card.mb-3 .btn-outline-success:hover {
  background: #388e3c;
  color: var(--white);
  border-color: #2e7d32;
}

/* Enhanced expand/collapse buttons in mb-3 cards with softer colors */
.mb-3.card .expand-btn,
.card.mb-3 .expand-btn,
.mb-3.card .collapse-btn,
.card.mb-3 .collapse-btn {
  background-color: #f5f8fc;
  border: 2px solid #a9c1e8;
  color: #4a6da7;
  border-radius: 6px;
  padding: 6px 14px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  margin: 0 5px 5px 0;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(74, 109, 167, 0.15);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.mb-3.card .expand-btn i,
.card.mb-3 .expand-btn i,
.mb-3.card .collapse-btn i,
.card.mb-3 .collapse-btn i {
  margin-right: 5px;
  font-size: 1.1rem;
}

.mb-3.card .expand-btn:hover,
.card.mb-3 .expand-btn:hover,
.mb-3.card .collapse-btn:hover,
.card.mb-3 .collapse-btn:hover {
  background-color: #e9eff8;
  border-color: #6b8cce;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(74, 109, 167, 0.2);
}

.report-list .card .card-body .task-item:last-child {
  margin-bottom: 0;
}

.report-list .card .card-body .task-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.report-list .card .card-body .task-date {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1.05rem;
  background: rgba(37, 99, 235, 0.1);
  padding: 4px 10px;
  border-radius: 6px;
}

.report-list .card .card-body .task-project {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3);
}

/* Style cho nội dung task khi mở rộng - Enhanced readability */
.report-list .card .card-body .task-item > div:last-child {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 18px 20px;
  border-radius: 10px;
  margin-top: 15px;
  border: 2px solid #e2e8f0;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.report-list .card .card-body .task-item > div:last-child p {
  margin-bottom: 12px;
  font-size: 1rem;
  line-height: 1.6;
  color: #1f2937;
}

.report-list .card .card-body .task-item > div:last-child strong {
  color: #1f2937;
  font-weight: 700;
  font-size: 1.05rem;
}

/* Style cho phần bình luận - Enhanced visibility */
.report-list .card .card-body .comments-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 20px;
  border-radius: 12px;
  margin-top: 20px;
  border: 2px solid #e2e8f0;
}

.report-list .card .card-body .comments-section h5 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: #1f2937;
  font-weight: 700;
  border-bottom: 2px solid #1f2937;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
}

.report-list .card .card-body .comments-section h5::before {
  content: "💬";
  margin-right: 8px;
  font-size: 1.1rem;
}

.report-list .card .card-body .comment {
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  padding: 15px !important;
  border-radius: 10px;
  margin-bottom: 12px;
  border: 1px solid #bbf7d0;
  box-shadow: 0 2px 6px rgba(5, 150, 105, 0.1);
}

.report-list .card .card-body p {
  margin: 0;
}
.report-list .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 32px rgba(37, 99, 235, 0.2);
  border-color: var(--primary-color);
}

/* Add hover effect for task items */
.report-list .card .card-body .task-item:hover {
  transform: translateX(5px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.15);
  border-left-color: #1e40af;
}

/* Enhanced animations and transitions */
.report-list .card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for multiple cards */
.report-list .card:nth-child(1) { animation-delay: 0.1s; }
.report-list .card:nth-child(2) { animation-delay: 0.2s; }
.report-list .card:nth-child(3) { animation-delay: 0.3s; }
.report-list .card:nth-child(4) { animation-delay: 0.4s; }
.report-list .card:nth-child(5) { animation-delay: 0.5s; }
.report-list .card:nth-child(6) { animation-delay: 0.6s; }

/* Enhanced focus states for accessibility */
.report-list .card:focus-within {
  outline: 3px solid rgba(37, 99, 235, 0.5);
  outline-offset: 2px;
}

/* Better text contrast for important information */
.report-list .card .card-body .task-item strong {
  color: #1f2937;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Enhanced readability for task content */
.report-list .card .card-body .task-item p {
  color: #374151;
  line-height: 1.7;
  font-size: 1rem;
  margin-bottom: 8px;
}

/* Enhanced style cho các thông tin trong card header */
.report-list .card-header strong {
  font-size: 1.2rem;
  color: #1f2937;
  font-weight: 700;
  margin-right: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.report-list .card-header small {
  display: block;
  margin-top: 6px;
  color: #64748b;
  line-height: 1.5;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Enhanced style cho badge trạng thái */
.report-list .card-header .badge {
  padding: 6px 12px;
  border-radius: 8px;
  font-weight: 700;
  font-size: 0.85rem;
  margin-left: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Enhanced status badges with better contrast */
.report-list .card-header .badge.bg-warning {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
  color: #1f2937 !important;
  border: 1px solid #d97706;
}

.report-list .card-header .badge.bg-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
  border: 1px solid #047857;
}

.report-list .card-header .badge.bg-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  color: white !important;
  border: 1px solid #b91c1c;
}

.report-list .card-header .badge.bg-info {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  color: white !important;
  border: 1px solid #1d4ed8;
}

/* Style cho các nút trong card */
.report-list .card-header .btn {
  padding: 5px 10px;
  font-size: 0.85rem;
  margin-bottom: 5px;
}

.card-header {
  padding: 15px 20px;
  background: var(--light-bg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header strong {
  color: #333;
  font-size: 1.15rem;
}

.card-header small {
  color: var(--secondary-text);
  font-size: 0.9rem;
}

.card-body {
  padding: 20px;
}

.card-body p {
  margin: 6px 0;
  font-size: 0.95rem;
  color: var(--text-color);
}

.card-body strong {
  color: var(--text-color);
  font-weight: 600;
}

.card-body .card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--light-bg);
  box-shadow: 0 2px 6px var(--shadow-light);
}

.status-ongoing {
  background: #dbeafe;
  color: #fff;
  padding: 4px 10px;
  border-radius: 6px;
  font-weight: 600;
}

.status-completed {
  background: #d1fae5;
  color: #fff;
  padding: 4px 10px;
  border-radius: 6px;
  font-weight: 600;
}

.action-buttons .btn,
.card-body .btn {
  padding: 8px 16px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.3s ease;
  margin: 0 3px;
  min-width: 90px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.action-buttons .btn:hover,
.card-body .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn.btn-danger.btn-sm{
  margin-top: 10px;
  float: right;
  font-size: 0.95rem;
  padding: 7px 14px;
  font-weight: 600;
}

/* Enhanced action buttons with softer colors */
.action-buttons .btn-outline-primary {
  border: 2px solid #6b8cce;
  color: #3b5998;
  background-color: rgba(59, 89, 152, 0.05);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-buttons .btn-outline-primary i {
  margin-right: 5px;
  font-size: 1.1rem;
}

.action-buttons .btn-outline-primary:hover {
  background: #4267b2;
  color: var(--white);
  border-color: #3b5998;
}

.action-buttons .btn-outline-danger {
  border: 2px solid #e57373;
  color: #c62828;
  background-color: rgba(198, 40, 40, 0.05);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-buttons .btn-outline-danger i {
  margin-right: 5px;
  font-size: 1.1rem;
}

.action-buttons .btn-outline-danger:hover {
  background: #d32f2f;
  color: var(--white);
  border-color: #c62828;
}

.card-body .btn-outline-success,
.action-buttons .btn-outline-success {
  border: 2px solid #81c784;
  color: #2e7d32;
  background-color: rgba(46, 125, 50, 0.05);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.card-body .btn-outline-success i,
.action-buttons .btn-outline-success i {
  margin-right: 5px;
  font-size: 1.1rem;
}

.card-body .btn-outline-success:hover,
.action-buttons .btn-outline-success:hover {
  background: #388e3c;
  color: var(--white);
  border-color: #2e7d32;
}

/* Phân trang */
.pagination {
  justify-content: center;
  margin-top: 30px;
}

.pagination .page-item .page-link {
  border-radius: 6px;
  margin: 0 5px;
  color: var(--primary-color);
  font-weight: 500;
  padding: 8px 14px;
  transition: all 0.3s ease;
}

.pagination .page-item.active .page-link {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.pagination .page-item .page-link:hover {
  background: var(--mid-bg);
  color: var(--primary-dark);
}

/* Modal xóa */
.modal-content {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  border-bottom: none;
  padding: 15px 20px;
}

.modal-title {
  font-weight: 600;
  font-size: 1.25rem;
  color: #fff;
}

.modal-body {
  padding: 20px;
  font-size: 1rem;
  color: var(--text-color);
}

.modal-footer {
  border-top: none;
  padding: 15px 20px;
  justify-content: flex-end;
  gap: 10px;
}
.manager-checkboxes {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 10px;
}
.manager-checkboxes .form-check {
  margin-bottom: 8px;
}
.modal-footer .btn {
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.modal-footer .btn-secondary {
  background: #6b7280;
  border: none;
}

.modal-footer .btn-secondary:hover {
  background: #4b5563;
}

.modal-footer .btn-danger {
  background: var(--danger-color);
  border: none;
}

.modal-footer .btn-danger:hover {
  background: #b91c1c;
}
/* WorkReports.css */
.status-pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important; /* Màu cam gradient cho "Chờ duyệt" */
  color: white !important;
  border: 1px solid #b45309 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}
.status-approved {
  background-color: #28a745 !important; /* Màu xanh cho "Đã duyệt" */
}
/* Task Card Styles */
.task-card {
  transition: all 0.3s ease;
  border-left: 3px solid var(--primary-color);
  margin-bottom: 15px;
}

.task-card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.task-card .card-body {
  display: flex;
  align-items: flex-start;
  padding: 20px;
}

.task-select {
  padding-top: 5px;
}

.task-content {
  flex-grow: 1;
  padding-right: 20px;
}

.task-info h5 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 10px;
}

.task-info .bg-light {
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.task-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 120px;
  padding-top: 10px;
}

.task-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.task-image {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.task-table-header {
  font-weight: 600;
  border-bottom: 2px solid var(--border-color);
  background-color: #f8f9fa;
  border-radius: 6px;
}

.cursor-pointer {
  cursor: pointer;
}

/* Styles for collapsed/expanded cards */
.card-header .btn-link {
  padding: 0;
  text-decoration: none;
  color: var(--primary-color);
  font-weight: 500;
}

.card-header .btn-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.card-header .btn-outline-info {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.card-header .btn-outline-info:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Task summary in header */
.task-summary {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  max-width: 80%;
  overflow: hidden;
}

.task-summary .badge {
  font-size: 0.75rem;
  padding: 4px 8px;
  margin-left: 8px;
}

.task-summary .text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.task-summary small.text-muted {
  font-size: 0.75rem;
  color: #6c757d;
  font-style: italic;
}

/* Expand/Collapse buttons */
.reports-header-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  width: 100%;
  text-align: center;
}

.reports-header-controls .total-count {
  margin-bottom: 10px;
  display: inline-block;
  text-align: center;
}

.expand-collapse-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 10px;
}

.expand-collapse-buttons .btn {
  font-size: 1rem;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  min-width: 120px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  background-color: #f5f8fc;
  border: 2px solid #a9c1e8;
  color: #4a6da7;
}

.expand-collapse-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background-color: #e9eff8;
  border-color: #6b8cce;
}

/* Checkbox styling */
.form-check-input {
  cursor: pointer;
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Status Badge Styles */
.status-pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important; /* Màu cam gradient cho "Chờ xác nhận" */
  color: white !important;
  border: 1px solid #b45309 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.status-accepted {
  background-color: #17a2b8 !important; /* Màu xanh dương cho "Đã nhận" */
}

.status-ongoing {
  background-color: #007bff !important; /* Màu xanh dương đậm cho "Tiếp tục" */
}

.status-paused {
  background-color: #6c757d !important; /* Màu xám cho "Tạm dừng" */
}

.status-completed {
  background-color: #28a745 !important; /* Màu xanh lá cho "Hoàn thành" */
}

/* Export Buttons Styles */
.export-buttons .dropdown-toggle {
  display: flex;
  align-items: center;
  background-color: #198754;
  border-color: #198754;
  padding: 8px 16px;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.export-buttons .dropdown-toggle:hover,
.export-buttons .dropdown-toggle:focus {
  background-color: #157347;
  border-color: #146c43;
  box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3);
}

.export-buttons .dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 180px;
}

.export-buttons .dropdown-item {
  padding: 10px 16px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.export-buttons .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #198754;
}

.export-buttons .dropdown-item i {
  margin-right: 8px;
  font-size: 1.1rem;
}

/* Suggestion Dropdown Styles */
.suggestion-dropdown {
  position: absolute;
  z-index: 1000;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 4px 8px var(--shadow-light);
}

.suggestion-dropdown .dropdown-header {
  padding: 8px 12px;
  font-weight: 600;
  background-color: var(--mid-bg);
}

.suggestion-dropdown .dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.3s ease;
  border: none;
  background: none;
  text-align: left;
  width: 100%;
}

.suggestion-dropdown .dropdown-item:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.task-table-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: auto;
  background-color: #f9fafb;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  max-width: none;
  max-height: 600px; /* Giới hạn chiều cao để tránh quá dài */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
  scroll-behavior: smooth;
  position: relative;
  z-index: 1;
  padding-bottom: 10px;
  text-align: center;
  -webkit-overflow-scrolling: touch; /* Improved scrolling on iOS */
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

/* Style the scrollbar */
.task-table-container::-webkit-scrollbar {
  height: 10px;
  width: 10px;
}

.task-table-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 5px;
}

.task-table-container::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 5px;
  border: 2px solid #f1f5f9;
}

.task-table-container::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* Hover effect for better visual feedback */
.task-table-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #cbd5e1;
}

/* Improved table styles for all screen sizes */
.task-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;
  background-color: white;
}

.task-table th {
  position: sticky;
  top: 0;
  background-color: #f1f5f9;
  z-index: 10;
  font-weight: 600;
  color: #1e40af;
  text-align: center;
  padding: 12px 10px;
  font-size: 0.9rem;
  border-bottom: 2px solid #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  white-space: nowrap;
}

.task-table td {
  padding: 10px;
  font-size: 0.95rem;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: middle;
}

/* Zebra striping for better readability */
.task-table tbody tr:nth-child(even) {
  background-color: #f8fafc;
}

/* Row hover effect */
.task-table tbody tr:hover {
  background-color: #f0f7ff;
  box-shadow: 0 2px 6px rgba(37, 99, 235, 0.1);
}

/* Styles for tablet screens */
@media (min-width: 769px) and (max-width: 1024px) {
  .task-table-container {
    max-height: 550px;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  }
  
  .task-table {
    min-width: 1000px;
  }
  
  .task-table th {
    padding: 12px 8px;
    font-size: 0.9rem;
  }
  
  .task-table td {
    padding: 10px 8px;
    font-size: 0.9rem;
  }
  
  /* Add a subtle indicator for scrollable content */
  .task-table-container::after {
    content: "← Scroll horizontally →";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(37, 99, 235, 0.05);
    color: #1e40af;
    padding: 5px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    border-top: 1px solid #e2e8f0;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .task-table-container:hover::after {
    opacity: 1;
  }
}

/* Scroll hint */
.scroll-hint {
  position: absolute;
  top: -30px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0.8;
  z-index: 10;
}

.task-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  margin-bottom: 1rem;
  table-layout: fixed; /* Ensures the table respects column widths */
  min-width: 1200px; /* Ensures the table is wide enough to scroll horizontally */
}

.task-table th,
.task-table td {
  padding: 8px 10px;
  border: 1px solid #ddd;
  vertical-align: top;
  font-size: 0.9rem;
  line-height: 1.4;
  text-align: center;
}

.task-table th {
  background-color: #f2f2f2;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
  font-size: 0.85rem;
  text-align: center;
  white-space: nowrap;
  border-bottom: 2px solid #ddd;
}

/* Column widths for children table */
.task-table th:nth-child(1) { width: 50px; } /* STT */
.task-table th:nth-child(2) { width: 150px; } /* TÊN CON */
.task-table th:nth-child(3) { width: 100px; } /* NGÀY SINH */
.task-table th:nth-child(4) { width: 70px; } /* TUỔI */
.task-table th:nth-child(5) { width: 150px; } /* TRÌNH ĐỘ HỌC VẤN */
.task-table th:nth-child(6) { width: 150px; } /* TÊN PHỤ HUYNH */
.task-table th:nth-child(7) { width: 120px; } /* SĐT PHỤ HUYNH */
.task-table th:nth-child(8) { width: 150px; } /* CÔNG TY */
.task-table th:nth-child(9) { width: 150px; } /* PHÒNG BAN */
.task-table th:nth-child(10) { width: 80px; } /* THAO TÁC */

.task-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.task-table tr:hover {
  background-color: #f0f0f0;
}

.task-table-container {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 2rem;
  position: relative;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.scroll-hint {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
  text-align: center;
  font-style: italic;
  background-color: #f8f9fa;
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.stt-cell {
  text-align: center;
  width: 40px;
  min-width: 40px;
  max-width: 40px;
}

.user-cell {
  width: 140px;
  min-width: 140px;
  max-width: 160px;
}

.project-cell {
  width: 100px;
  min-width: 100px;
  max-width: 120px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.content-cell {
  width: 200px;
  min-width: 180px;
  max-width: 250px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.time-cell {
  width: 80px;
  min-width: 80px;
  max-width: 100px;
  font-size: 0.9rem;
}

.progress-cell {
  width: 150px;
  min-width: 130px;
  max-width: 180px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.status-cell {
  width: 90px;
  min-width: 90px;
  max-width: 110px;
  text-align: center;
  font-weight: bold;
}

.image-cell {
  width: 120px;
  min-width: 120px;
  max-width: 150px;
}

.user-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.update-date {
  font-size: 0.8rem;
  color: #6c757d;
}

.preserve-whitespace {
  white-space: pre-line;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 80px;
  overflow-y: auto;
}

/* General styles for the manager assigned task card */
.manager-assigned-card .task-info h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151; /* var(--text-color) */
  margin-bottom: 10px;
}

.manager-assigned-card .task-info .bg-light {
  background-color: #f8f9fa !important;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.manager-assigned-card .task-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.manager-assigned-card .task-image {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.manager-assigned-card .task-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Desktop-only action buttons container */
.manager-assigned-card .task-actions-desktop {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 120px;
}

/* Hide mobile actions on desktop */
.manager-assigned-card .task-actions-mobile {
  display: none;
}


/* --- Responsive Styles for Mobile (max-width: 767.98px) --- */
@media (max-width: 767.98px) {
  .manager-assigned-card .card-body {
    flex-direction: column;
    align-items: flex-start;
  }

  .manager-assigned-card .task-select {
    margin-bottom: 1rem;
  }

  .manager-assigned-card .task-content {
    width: 100%;
  }

  /* Hide desktop actions on mobile */
  .manager-assigned-card .task-actions-desktop {
    display: none;
  }

  /* Show and style mobile actions */
  .manager-assigned-card .task-actions-mobile {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
  }

  .manager-assigned-card .task-actions-mobile .btn {
    width: 48%;
  }
}

@media (max-width: 768px) {
  .preserve-whitespace {
    max-height: none;
    overflow-y: visible;
    font-size: 0.85rem;
    line-height: 1.4;
  }
}

.horizontal-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.task-images {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.task-images img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

.status-badge {
  display: inline-block;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.status-pending {
  background-color: #f59e0b;
  background-image: linear-gradient(135deg, #f59e0b, #d97706);
  border: 1px solid #b45309;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.status-accepted {
  background-color: #17a2b8;
  background-image: linear-gradient(135deg, #17a2b8, #138496);
}

.status-ongoing {
  background-color: #007bff;
  background-image: linear-gradient(135deg, #007bff, #0056b3);
}

.status-paused {
  background-color: #ffc107;
  background-image: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
}

.status-completed {
  background-color: #28a745;
  background-image: linear-gradient(135deg, #28a745, #1e7e34);
}

.time-range {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.no-tasks {
  padding: 30px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

/* New styles for expandable content */
.expandable-content {
  position: relative;
  width: 100%;
}

.content-preview {
  position: relative;
  padding-right: 70px;
}

.content-full {
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.expand-btn,
.collapse-btn {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #0d6efd;
  border-radius: 3px;
  padding: 1px 4px;
  font-size: 0.7rem;
  cursor: pointer;
  margin-left: 3px;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}

.collapse-btn {
  position: static;
  display: block;
  margin-top: 5px;
  margin-left: auto;
}

.expand-btn:hover,
.collapse-btn:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
}

/* Make it more mobile-friendly */
@media (max-width: 768px) {
  .task-table-container {
    max-width: 100%;
    overflow-x: auto;
    max-height: 500px; /* Tăng chiều cao trên mobile để hiển thị nhiều hơn */
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    border: 1px solid #e2e8f0;
    background-color: #f8fafc;
  }
  
  /* Improved table styles for mobile */
  .task-table {
    min-width: 900px; /* Ensure table is wide enough to scroll horizontally */
    border-collapse: separate;
    border-spacing: 0;
  }
  
  .task-table th {
    position: sticky;
    top: 0;
    background-color: #f1f5f9;
    z-index: 10;
    font-weight: 600;
    color: #1e40af;
    text-align: center;
    padding: 10px 8px;
    font-size: 0.85rem;
    border-bottom: 2px solid #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  .task-table td {
    padding: 8px;
    font-size: 0.85rem;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
  }
  
  /* Row hover effect */
  .task-table tbody tr:hover {
    background-color: #f0f7ff;
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.1);
    transform: translateY(-1px);
  }
  
  /* Cell width adjustments for mobile */
  .stt-cell {
    width: 40px;
    min-width: 40px;
    max-width: 40px;
    font-weight: 600;
    background-color: #f8fafc;
  }
  
  .user-cell {
    width: 130px;
    min-width: 130px;
    max-width: 150px;
  }
  
  .project-cell {
    width: 90px;
    min-width: 90px;
    max-width: 110px;
    font-weight: 500;
  }
  
  .content-cell {
    width: 160px;
    min-width: 160px;
    max-width: 200px;
  }
  
  .time-cell {
    width: 75px;
    min-width: 75px;
    max-width: 85px;
    font-size: 0.8rem;
    text-align: center;
  }
  
  .progress-cell {
    width: 130px;
    min-width: 130px;
    max-width: 160px;
  }
  
  .status-cell {
    width: 85px;
    min-width: 85px;
    max-width: 95px;
    text-align: center;
  }
  
  .image-cell {
    width: 110px;
    min-width: 110px;
    max-width: 130px;
  }
  
  /* Improved text display */
  .preserve-whitespace {
    max-height: 70px;
    font-size: 0.85rem;
    line-height: 1.4;
    overflow-y: auto;
    padding: 4px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 4px;
  }
  
  /* Better expand/collapse buttons */
  .expand-btn,
  .collapse-btn {
    font-size: 0.7rem;
    padding: 2px 4px;
    background-color: #f0f7ff;
    border: 1px solid #bfdbfe;
    color: #2563eb;
    border-radius: 4px;
  }
  
  /* Visual indicator for scrollable table */
  .task-table-container::after {
    content: "← Vuốt để xem thêm →";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(37, 99, 235, 0.1);
    color: #1e40af;
    padding: 5px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    border-top: 1px solid #bfdbfe;
    animation: fadeInOut 2s infinite;
  }
  
  @keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
  }
    .my-reports-card .task-card-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 12px 15px;
    padding-top: 20px; /* Tạo khoảng cách cho task-card-number */
    background: linear-gradient(to right, #f0f7ff, #f8fafc);
    border-bottom: 1px solid #e2e8f0;
    gap: 10px;
    position: relative; /* Cần thiết để định vị task-card-number */
  }
  
  .my-reports-card .task-card-number {
    position: absolute; /* Đặt vị trí tuyệt đối */
    top: 10px;
    right: 10px;
    z-index: 10;
    background: linear-gradient(135deg, #2563eb, #1e40af);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.85rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }
  
  .my-reports-card .task-card-title {
    font-weight: 600;
    color: #1e40af;
    font-size: 1rem;
    word-break: break-word;
    text-align: center; /* Căn giữa tiêu đề */
    width: 100%; /* Chiếm toàn bộ chiều rộng */
  }
  
  .my-reports-card .task-card-title .user-name {
    display: block;
    margin-bottom: 5px;
    text-align: center;
  }
  
  .my-reports-card .task-card-title .update-date {
    display: block;
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: normal;
    text-align: center;
  }
  
  .my-reports-card .task-card-header .status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0 auto; /* Căn giữa theo chiều ngang */
    display: inline-block; /* Đảm bảo badge hiển thị đúng */
  }
  
  /* Card body responsiveness */
  .my-reports-card .card-body {
    padding: 15px;
  }
  
  /* Task info rows */
  .my-reports-card .task-card-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
    border-bottom: 1px solid #f1f5f9;
    padding-bottom: 12px;
    text-align: center;
  }
  
  .my-reports-card .task-card-row:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
  }
  
  .my-reports-card .task-card-label {
    width: 100%;
    font-weight: 600;
    color: #4b5563;
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .my-reports-card .task-card-value {
    width: 100%;
    color: #1f2937;
    font-size: 0.95rem;
    text-align: center;
  }
  
  /* Card footer styles */
  .my-reports-card .task-card-footer {
    padding: 12px 15px;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
  }
  
  .my-reports-card .task-card-action {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(37, 99, 235, 0.3);
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
  
  .my-reports-card .task-card-action i {
    margin-right: 5px;
  }
  
  .my-reports-card .task-card-action:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);
  }

  .task-table-container {
    overflow: visible;
    background-color: transparent;
    box-shadow: none;
    border: none;
    max-height: none;
    padding: 0;
  }
  
  .task-table-container::after {
    display: none; /* Hide the scroll hint */
  }
  
  /* Hide the original table */
  .task-table-container .task-table {
    display: none;
  }
  
  /* Create a new container for the card layout */
  .task-table-container::before {
    content: "Danh sách công việc";
    display: block;
    margin-bottom: 15px;
    font-weight: 600;
    color: #1e40af;
    text-align: center;
    font-size: 1.1rem;
    background-color: #f0f7ff;
    padding: 10px;
    border-radius: 8px;
    border-left: 4px solid #2563eb;
  }
  
  /* Add card layout for tasks */
  .task-table-container .task-cards {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    padding: 10px 5px;
  }
  
  .task-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border-left: 4px solid #2563eb;
    transition: all 0.3s ease;
    margin-bottom: 5px;
    position: relative;
    z-index: 1;
  }
  
  .task-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }
  
    .task-card-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 12px 15px;
      padding-top: 20px; /* Thêm padding-top để tránh chồng lấn với task-card-number */
      background: linear-gradient(to right, #f0f7ff, #f8fafc);
      border-bottom: 1px solid #e2e8f0;
      gap: 10px;
      position: relative; /* Thêm vị trí tương đối để định vị task-card-number */
    }
    
    .task-card-number {
      position: absolute; /* Đặt vị trí tuyệt đối */
      top: 10px;
      right: 10px;
      z-index: 10;
      /* Các thuộc tính hiện tại của task-card-number giữ nguyên */
    }
    
    .task-card-title {
      font-weight: 600;
      color: #1e40af;
      font-size: 1rem;
      word-break: break-word;
      text-align: center; /* Đảm bảo tiêu đề được căn giữa */
      width: 100%; /* Chiếm toàn bộ chiều rộng */
    }
    
    .task-card-title .user-name {
      display: block;
      margin-bottom: 5px;
      text-align: center;
    }
    
    .task-card-title .update-date {
      display: block;
      font-size: 0.8rem;
      color: #6b7280;
      font-weight: normal;
      text-align: center;
    }
    
    .task-card-header .status-badge {
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin: 0 auto; /* Căn giữa theo chiều ngang */
      display: inline-block; /* Đảm bảo badge hiển thị đúng */
    }
  
  
  .task-card-status.status-pending {
    background: linear-gradient(135deg, #f59e0b, #d97706);
  }
  
  .task-card-status.status-accepted {
    background: linear-gradient(135deg, #17a2b8, #138496);
  }
  
  .task-card-status.status-ongoing {
    background: linear-gradient(135deg, #007bff, #0056b3);
  }
  
  .task-card-status.status-paused {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
  }
  
  .task-card-status.status-completed {
    background: linear-gradient(135deg, #28a745, #1e7e34);
  }
  
  .task-card-body {
    padding: 15px;
  }
  
  .task-card-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
    border-bottom: 1px solid #f1f5f9;
    padding-bottom: 12px;
  }
  
  .task-card-row:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
  }
  
  .task-card-label {
    width: 100%;
    font-weight: 600;
    color: #4b5563;
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
  }
  
  .task-card-label i {
    margin-right: 5px;
    color: #2563eb;
  }
  
  .task-card-value {
    width: 100%;
    color: #1f2937;
    font-size: 0.95rem;
    background-color: #f8fafc;
    padding: 8px 10px;
    border-radius: 6px;
    border-left: 3px solid #e2e8f0;
    line-height: 1.4;
    word-break: break-word;
    min-height: 20px;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  .task-card-value.content,
  .task-card-value.progress {
    white-space: pre-line;
    max-height: 150px;
    overflow-y: auto;
    padding: 12px;
    background-color: #f8fafc;
    border-radius: 6px;
    border-left: 3px solid #2563eb;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* Fix for expandable content in cards */
  .task-card-value .expandable-content {
    display: block !important;
  }
  
  .task-card-value .content-preview,
  .task-card-value .content-full {
    display: block !important;
  }
  
  .task-card-value .expand-btn,
  .task-card-value .collapse-btn {
    display: none !important;
  }
  
  .task-card-value.collaborator .name-item {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
  }
  
  .task-card-footer {
    padding: 12px 15px;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .task-card-date {
    font-size: 0.85rem;
    color: #6b7280;
  }
  
  .task-card-action {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(37, 99, 235, 0.3);
    display: flex !important;
    align-items: center;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 5;
  }
  
  .task-card-action i {
    margin-right: 5px;
  }
  
  .task-card-action:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);
  }
  
  /* Badge for task number */
  .task-card-number {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #2563eb, #1e40af);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.85rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 10;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* Expand/collapse buttons for mobile */
  .task-card .expand-btn,
  .task-card .collapse-btn {
    background-color: #f0f7ff;
    border: 1px solid #bfdbfe;
    color: #2563eb;
    border-radius: 20px;
    padding: 5px 10px;
    font-size: 0.8rem;
    margin-top: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .task-card .expand-btn i,
  .task-card .collapse-btn i {
    margin-right: 5px;
    font-size: 0.8rem;
  }
  
  .task-card .expand-btn:hover,
  .task-card .collapse-btn:hover {
    background-color: #dbeafe;
    transform: translateY(-1px);
  }
  
  /* Time display in cards */
  .task-card-value.time {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
  
  .task-card-value.time div {
    padding: 5px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    font-size: 0.85rem;
  }
  
  /* Empty state for mobile */
  .task-table-container .no-tasks {
    background-color: #f8fafc;
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    color: #6b7280;
    font-size: 1rem;
    border: 1px dashed #cbd5e1;
    margin: 20px 0;
  }
  
  .task-table-container .no-tasks i {
    font-size: 2.5rem;
    color: #cbd5e1;
    margin-bottom: 15px;
    display: block;
  }
}

/* Special styles for very small screens */
@media (max-width: 480px) {
  .task-card-header {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  .task-card-status {
    align-self: center;
  }
  
  .task-card-footer {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .task-card-action {
    width: 100%;
    justify-content: center;
  }
}

/* Enhanced tablet view (between mobile and desktop) */
@media (min-width: 768px) and (max-width: 991px) {
  .task-table-container {
    max-height: 600px;
    border-radius: 10px;
    padding: 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    border-left: 4px solid var(--primary-color);
  }
  
  .task-table {
    min-width: 900px;
  }
  
  .task-table th {
    font-size: 0.85rem;
    padding: 10px 8px;
    white-space: nowrap;
  }
  
  .task-table td {
    font-size: 0.9rem;
    padding: 8px;
  }
  
  /* Improved scroll indicator for tablets */
  .task-table-container::after {
    content: "← Kéo ngang để xem thêm →";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to right, rgba(37, 99, 235, 0.1), rgba(37, 99, 235, 0.05), rgba(37, 99, 235, 0.1));
    color: #1e40af;
    padding: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    border-top: 1px solid #e2e8f0;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: pulse 2s infinite;
  }
  
  .task-table-container:hover::after {
    opacity: 1;
  }
  
  @keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
  }
  
  /* Compact column widths for tablet */
  .task-table .stt-cell {
    width: 40px;
  }
  
  .task-table .user-cell {
    width: 140px;
  }
  
  .task-table .status-cell {
    width: 100px;
  }
  
  .task-table .project-cell {
    width: 150px;
  }
  
  .task-table .time-cell {
    width: 140px;
  }
  
  .task-table .collaborator-cell {
    width: 150px;
  }
  
  /* Improved expand/collapse buttons for tablet */
  .task-table .expand-btn,
  .task-table .collapse-btn {
    padding: 4px 8px;
    font-size: 0.75rem;
    border-radius: 4px;
  }
}

/* Improved loading state for all screen sizes */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.loading-text {
  font-size: 1rem;
  color: var(--text-color);
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
  
  /* Responsive cho action buttons trong modal */
  .action-buttons-modal {
    flex-direction: column;
    gap: 6px;
    width: 100%;
  }
  
  .action-buttons-modal .btn {
    font-size: 0.8rem;
    padding: 6px 12px;
    width: 100%;
    justify-content: center;
  }
  
  .task-modal .modal-footer {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }


/* Styles for the task modal */
.task-modal .modal-dialog {
  max-width: 90%;
  margin: 1.75rem auto;
}

/* Responsive task modal for different screen sizes */
@media (max-width: 991px) {
  .task-modal .modal-dialog {
    max-width: 95%;
    margin: 1rem auto;
  }
  
  .task-modal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
  
  .task-modal .modal-header {
    padding: 15px 20px;
    background: linear-gradient(to right, #f0f7ff, #f8fafc);
    border-bottom: 1px solid #e2e8f0;
    border-radius: 12px 12px 0 0;
  }
  
  .task-modal .modal-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #fff;
  }
  
  .task-modal .modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .task-modal .modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e2e8f0;
    background-color: #f8fafc;
    border-radius: 0 0 12px 12px;
  }
}

@media (max-width: 767px) {
  .task-modal .modal-dialog {
    max-width: 100%;
    margin: 0.5rem;
  }
  
  .task-modal .modal-header {
    padding: 12px 15px;
  }
  
  .task-modal .modal-title {
    font-size: 1.1rem;
  }
  
  .task-modal .modal-body {
    padding: 15px;
    max-height: 65vh;
  }
  
  .task-modal .modal-footer {
    padding: 12px 15px;
    flex-direction: column;
    align-items: stretch;
  }
  
  .task-modal .modal-footer .btn {
    margin: 5px 0;
  }
  
  /* Improved form layout in modal for mobile */
  .task-modal .form-group {
    margin-bottom: 15px;
  }
  
  .task-modal .form-label {
    font-size: 0.9rem;
    margin-bottom: 5px;
  }
  
  .task-modal .form-control {
    font-size: 0.95rem;
    padding: 8px 12px;
  }
  
  /* Task details in modal for mobile */
  .task-modal .task-details-section {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
    border-left: 3px solid var(--primary-color);
  }
  
  .task-modal .task-detail-row {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .task-modal .task-detail-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
  
  .task-modal .task-detail-label {
    font-weight: 600;
    color: #4b5563;
    font-size: 0.85rem;
    margin-bottom: 5px;
  }
  
  .task-modal .task-detail-value {
    color: #1f2937;
    font-size: 0.95rem;
    background-color: white;
    padding: 8px 10px;
    border-radius: 6px;
    border-left: 2px solid #e2e8f0;
  }
}

/* Base task modal styles */
.task-modal .modal-dialog {
  max-width: 90%;
  width: 800px;
}

.task-modal .modal-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: none;
}

.task-modal .modal-footer {
  border-top: 1px solid #e2e8f0;
  padding: 15px 20px;
  background-color: #f8fafc;
}

.action-buttons-modal {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons-modal .btn {
  font-size: 0.9rem;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons-modal .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-buttons-modal .btn-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.action-buttons-modal .btn-warning:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  color: white;
}

.action-buttons-modal .btn-info {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  color: white;
}

.action-buttons-modal .btn-info:hover {
  background: linear-gradient(135deg, #0284c7, #0369a1);
  color: white;
}

.action-buttons-modal .btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.action-buttons-modal .btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
}

.action-buttons-modal .btn-success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.action-buttons-modal .btn-success:hover {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
}

.action-buttons-modal .btn-primary {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  color: white;
}

.action-buttons-modal .btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  color: white;
}

.task-modal .modal-footer .btn-secondary {
  background: linear-gradient(135deg, #64748b, #475569);
  border: none;
  padding: 8px 20px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.task-modal .modal-footer .btn-secondary:hover {
  background: linear-gradient(135deg, #475569, #334155);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.task-modal .modal-header {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  color: white;
  padding: 15px 20px;
  border-bottom: none;
}

.task-modal .modal-title {
  font-weight: 600;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
}

.task-modal .modal-title::before {
  content: '\f022';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  margin-right: 10px;
}

.task-modal .modal-body {
  padding: 25px;
  max-height: 80vh;
  overflow-y: auto;
}

.task-details h5 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-top: 20px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
  color: #1e40af;
}

.task-details h5:first-child {
  margin-top: 0;
}

.task-details p {
  margin-bottom: 8px;
}

.task-details strong {
  color: #4b5563;
  font-weight: 600;
}

.task-details .bg-light {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  padding: 15px !important;
  margin-bottom: 20px;
}

.task-details .bg-light:hover {
  background-color: #f1f5f9 !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Style for task images in modal */
.task-details .d-flex.flex-wrap img {
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-right: 10px;
  margin-bottom: 10px;
  border: 2px solid white;
}

.task-details .d-flex.flex-wrap img:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  z-index: 1;
  position: relative;
}

.task-details .row {
  margin-bottom: 15px;
}

.task-details {
  margin-bottom: 20px;
}

.task-details h5 {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.task-details p {
  margin-bottom: 8px;
}

/* Any additional styles you already have */

/* Responsive */
@media (max-width: 1024px) {
  .report-list {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .work-reports-page {
    padding: 20px 15px;
  }

  .page-header h1 {
    font-size: 1.75rem;
  }

  .page-header p {
    font-size: 1rem;
  }

  .work-report-form,
  .employee-filter-section,
  .manager-section .card {
    padding: 20px;
  }

  .report-list {
    grid-template-columns: 1fr;
  }

  .button-group {
    flex-direction: column;
    gap: 10px;
  }

  .work-report-form .btn-primary,
  .work-report-form .btn-submit {
    width: 100%;
  }
  .btn-submit {
    padding: 10px 20px; /* Giảm padding trên mobile */
    font-size: 16px; /* Giảm cỡ chữ */
    width: 150px; /* Giảm chiều rộng */
    height: 40px; /* Giảm chiều cao */
  }
}


@media (max-width: 480px) {
  .page-header h1 {
    font-size: 1.5rem;
  }

  .work-report-form .form-label,
  .employee-filter-section .form-label,
  .manager-section .form-label {
    font-size: 0.9rem;
  }

  .work-report-form .form-control,
  .work-report-form .form-select,
  .employee-filter-section .form-control,
  .employee-filter-section .form-select,
  .manager-section .form-control,
  .manager-section .form-select {
    font-size: 0.9rem;
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* Enhance the clickable rows in the task table */
.task-table tbody tr {
  transition: all 0.2s ease;
}

.task-table tbody tr:hover {
  background-color: #e6f7ff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.task-table tbody tr:active {
  transform: translateY(0);
  background-color: #d6f0ff !important;
}

/* Add a hint icon to indicate the row is clickable */
.task-table tbody tr td:first-child::after {
  content: '👁';
  font-size: 0.85rem;
  margin-left: 5px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.task-table tbody tr:hover td:first-child::after {
  opacity: 1;
}

/* Style for expand/collapse buttons with softer colors */
.expand-btn,
.collapse-btn {
  background-color: #f5f8fc;
  border: 2px solid #a9c1e8;
  color: #4a6da7;
  border-radius: 6px;
  padding: 5px 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  margin-left: 8px;
  transition: all 0.2s;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(74, 109, 167, 0.15);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.expand-btn i,
.collapse-btn i {
  margin-right: 5px;
  font-size: 1rem;
}

.expand-btn:hover,
.collapse-btn:hover {
  background-color: #e9eff8;
  border-color: #6b8cce;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(74, 109, 167, 0.2);
}

/* Make the modal look better */
.task-modal .modal-body {
  max-height: 80vh;
  overflow-y: auto;
  font-size: 1.05rem; /* Increased font size throughout modal */
}

.task-details .bg-light {
  border-left: 3px solid #1890ff;
  transition: all 0.3s;
}

.task-details .bg-light:hover {
  background-color: #f0f7ff !important;
}

/* Increase width for the individual and collaborator columns */
.individual-cell, 
.collaborator-cell {
  width: 180px;
  min-width: 180px;
  max-width: 250px;
  padding: 12px 15px;
}

/* Ensure text in these cells displays nicely */
.individual-cell .preserve-whitespace,
.collaborator-cell .preserve-whitespace {
  white-space: normal;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
  line-height: 1.4;
  display: block;
  height: auto;
  padding: 5px;
}

/* Add a light background to help separate names when there are multiple */
.individual-cell .preserve-whitespace,
.collaborator-cell .preserve-whitespace {
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #dee2e6;
  transition: all 0.2s;
}

.individual-cell .preserve-whitespace:hover,
.collaborator-cell .preserve-whitespace:hover {
  background-color: #f0f0f0;
  border-left-color: #6c757d;
}

/* Ensure table headers for these columns match the width */
.task-table th:nth-child(7),  /* "Cá nhân phối hợp" column */
.task-table th:nth-child(8) { /* "Tiến độ" column */
  width: 180px;
  min-width: 180px;
  max-width: 250px;
  background-color: #e9ecef;
}

/* Responsive adjustments for these columns */
@media (max-width: 768px) {
  .task-table th:nth-child(7),
  .task-table th:nth-child(8) {
    width: 150px;
    min-width: 150px;
  }
}



/* Action cell styles removed - now using modal actions */

/* Approval status styling */
.approval-status {
  margin-top: 5px;
}

.approval-status .badge {
  font-size: 0.75rem;
}

/* Enhanced user cell for my-reports */
.user-cell .user-name {
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 3px;
}

.user-cell .update-date {
  font-size: 0.85rem;
  color: var(--secondary-text);
  margin-bottom: 3px;
}

/* No tasks message styling */
.no-tasks {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-text);
  font-size: 1.1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

/* Responsive adjustments for my-reports table */
@media (max-width: 768px) {
  .user-cell .user-name {
    font-size: 0.9rem;
  }
  
  .user-cell .update-date {
    font-size: 0.8rem;
  }
  
  .task-table th,
  .task-table td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }
}

/* Style for name items */
.name-item {
  padding: 3px 5px;
  margin-right: 3px;
  margin-bottom: 3px;
  font-size: 0.9rem;
  border-left: 2px solid #6c757d;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  background-color: rgba(248, 249, 250, 0.5);
  border-radius: 3px;
}

.name-item:hover {
  background-color: #e2e6ea;
  transform: translateX(2px);
}

/* Style for manual collaborator name items */
.name-item.manual-collaborator {
  border-left: 2px solid #ffc107;
  background-color: #fff3cd;
  color: #856404;
}

.name-item.manual-collaborator:hover {
  background-color: #ffeaa7;
}

/* Collaborator selection styles */
.collaborator-selection {
  width: 100%;
}

.selected-collaborators {
  margin-bottom: 10px;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: center;
}

.selected-label {
  font-weight: 600;
  color: #6c757d;
  margin-right: 8px;
}

/* Manual collaborator input styles */
.manual-collaborator-input {
  border-top: 1px solid #e9ecef;
  padding-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.manual-collaborator-input .form-control {
  border-radius: 6px;
  border: 1px solid #ffc107;
  background-color: #fffbf0;
  flex: 1;
  min-width: 150px;
}

.manual-collaborator-input .form-control:focus {
  border-color: #ffb300;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.manual-collaborator-input .btn-outline-warning {
  border-color: #ffc107;
  color: #856404;
  white-space: normal;
  text-align: center;
  word-wrap: break-word;
  height: auto;
  min-height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  font-size: 0.9rem;
  max-width: 200px;
}

.manual-collaborator-input .btn-outline-warning:hover {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #000;
}

/* Responsive adjustments for manual collaborator input */
@media (max-width: 768px) {
  .manual-collaborator-input {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .manual-collaborator-input .form-control {
    width: 100%;
    margin-bottom: 0;
  }
  
  .manual-collaborator-input .btn-outline-warning {
    width: 100%;
    max-width: 100%;
    margin-top: 0;
  }
}

/* Allow name items to flow naturally */
.individual-cell .preserve-whitespace,
.collaborator-cell .preserve-whitespace {
  background-color: transparent;
  border-left: none;
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
  padding: 3px;
}

/* Improve visibility of individual and collaborator cells */
.individual-cell, 
.collaborator-cell {
  background-color: rgba(248, 249, 250, 0.3);
}

/* Add a subtle highlight on hover */
.individual-cell:hover, 
.collaborator-cell:hover {
  background-color: rgba(248, 249, 250, 0.8);
}

/* Style for name items in modal */
.name-item-modal {
  display: inline-block;
  padding: 4px 8px;
  margin: 3px;
  background-color: #e6f7ff;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  font-size: 0.95rem;
  transition: all 0.2s;
}

.name-item-modal:hover {
  background-color: #bae7ff;
  transform: translateX(2px);
}

/* Make the general information section stand out */
.task-details h5 {
  border-bottom: 2px solid #1890ff;
  padding-bottom: 12px;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
  color: #0958d9;
}

/* Highlight the general information section */
.task-details p strong {
  font-weight: 600;
  color: #222;
  font-size: 1.1rem;
}

/* Style the first section differently to make it stand out */
.task-details h5:first-of-type {
  background-color: #f0f7ff;
  padding: 10px 15px;
  border-radius: 6px;
  border-left: 5px solid #1890ff;
  border-bottom: none;
}

/* Make the general info user details more prominent */
.task-details p:nth-of-type(1), 
.task-details p:nth-of-type(2) {
  font-weight: 500;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 5px;
  margin-bottom: 12px;
  border-left: 3px solid #6c757d;
}

/* Status badge styling in modal */
.task-modal .status-badge {
  font-size: 0.95rem;
  padding: 6px 12px;
  border-radius: 6px;
  display: inline-block;
  margin-left: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.task-modal .status-section {
  display: flex;
  align-items: center;
  margin: 15px 0;
  padding: 10px 15px;
  background-color: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #2563eb;
}

/* Status colors in modal */
.task-modal .status-pending {
  background-color: #f59e0b;
  background-image: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: 1px solid #b45309;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.task-modal .status-accepted {
  background-color: #17a2b8;
  background-image: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.task-modal .status-ongoing {
  background-color: #007bff;
  background-image: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.task-modal .status-paused {
  background-color: #ffc107;
  background-image: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
}

.task-modal .status-completed {
  background-color: #28a745;
  background-image: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

/* Collaborator Search Dropdown Styles */
.collaborator-selection .dropdown-menu {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 0;
  overflow: hidden;
}

.collaborator-selection .dropdown-menu .form-control {
  border: none;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 0;
  font-size: 0.9rem;
  padding: 8px 12px;
  background-color: #f8fafc;
}

.collaborator-selection .dropdown-menu .form-control:focus {
  box-shadow: none;
  border-color: #2563eb;
  background-color: white;
}

.collaborator-selection .dropdown-item {
  padding: 10px 15px;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s ease;
}

.collaborator-selection .dropdown-item:last-child {
  border-bottom: none;
}

.collaborator-selection .dropdown-item:hover {
  background-color: #f1f5f9;
  transform: translateX(2px);
}

.collaborator-selection .dropdown-item:active {
  background-color: #e2e8f0;
}

.collaborator-selection .dropdown-item .fw-bold {
  color: #1f2937;
  font-size: 0.95rem;
}

.collaborator-selection .dropdown-item .text-muted {
  color: #6b7280;
  font-size: 0.85rem;
}

.collaborator-selection .selected-collaborators .badge {
  font-size: 0.85rem;
  padding: 6px 10px;
  border-radius: 6px;
  background: linear-gradient(135deg, #2563eb, #1e40af);
  border: none;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
  color: white !important; /* Force text color to be white */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
  word-break: break-word;
  position: relative;
  padding-right: 25px; /* Space for the close button */
  text-align: center;
}

.collaborator-selection .selected-collaborators .btn-close {
  font-size: 0.6em;
  opacity: 0.8;
  transition: opacity 0.2s ease;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.collaborator-selection .selected-collaborators .btn-close:hover {
  opacity: 1;
}

.btn-outline-warning.btn-sm {
  max-width: 120px;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: break-word;
  text-align: center;
  padding: 8px 10px;
  font-size: 0.9rem;
  box-sizing: border-box;
}

.manual-collaborator-input .btn-outline-warning.btn-sm {
  max-width: 120px;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: break-word;
  text-align: center;
  padding: 8px 10px;
  font-size: 0.9rem;
  box-sizing: border-box;
}

.collaborator-selection .dropdown-toggle {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  white-space: normal;
  text-align: left;
  word-wrap: break-word;
  overflow: hidden;
}

/* Responsive adjustments for collaborator selection dropdown */
@media (max-width: 768px) {
  .collaborator-selection {
    width: 100%;
  }
  
  .collaborator-selection .dropdown-toggle {
    font-size: 0.85rem;
    padding: 6px 8px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: auto;
    min-height: 38px;
  }
  
  /* Fix for "Thêm cá nhân phối hợp" button in responsive mode */
  .collaborator-selection .btn {
    white-space: normal;
    text-align: center;
    word-wrap: break-word;
    height: auto;
    min-height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 10px;
  }
  
  /* Center collaborator badges in responsive mode */
  .collaborator-selection .selected-collaborators {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  
  .collaborator-selection .selected-collaborators .badge {
    width: 100%;
    text-align: center;
    justify-content: center;
    padding-left: 25px; /* Balance the padding for the close button */
    padding-right: 25px;
  }
}

.collaborator-selection .dropdown-toggle:hover {
  border-color: #2563eb;
  background-color: #f8fafc;
}

.collaborator-selection .dropdown-toggle:focus {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  border-color: #2563eb;
}

/* Selected label styling */
.selected-label {
  color: #000 !important;
  background-color: #fff;
  font-weight: 700 !important;
  font-size: 0.9rem !important;
  padding: 4px 8px;
  border-radius: 4px;
  border: 2px solid #2563eb;
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
  transition: all 0.2s ease;
}

/* Responsive adjustments for selected label */
@media (max-width: 768px) {
  .selected-label {
    display: block;
    width: 100%;
    margin-bottom: 8px;
    text-align: center;
    padding: 6px 10px;
    font-size: 0.85rem !important;
  }
}

.selected-label:hover {
  box-shadow: 0 3px 6px rgba(37, 99, 235, 0.2);
  transform: translateY(-1px);
}

/* Selected managers display styling */
.selected-managers-display {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.selected-managers-display .selected-content {
  color: #1f2937;
  font-weight: 600;
  font-size: 0.95rem;
  background-color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  min-height: 24px;
  display: inline-block;
  flex: 1;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  word-break: break-word;
}

/* Responsive adjustments for selected managers display */
@media (max-width: 768px) {
  .selected-managers-display {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .selected-managers-display .selected-content {
    width: 100%;
    margin-top: 5px;
    color: #1f2937 !important;
  }
}

.selected-managers-display .selected-content:empty::before {
  content: "Chưa chọn ai";
  color: #9ca3af;
  font-style: italic;
}

/* Hover effect for the entire display */
.selected-managers-display:hover {
  background-color: #f1f5f9;
  border-color: #2563eb;
}

.selected-managers-display:hover .selected-content {
  border-color: #2563eb;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
}

/* Selected managers badges styling */
.selected-list .badge.bg-success {
  font-size: 0.85rem;
  padding: 6px 10px;
  border-radius: 6px;
  background: linear-gradient(135deg, #10b981, #059669) !important;
  border: none;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
  color: white !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
  word-break: break-word;
  position: relative;
  padding-right: 25px; /* Space for the close button */
  text-align: center;
}

/* Responsive adjustments for selected list */
@media (max-width: 768px) {
  .selected-list {
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  
  .selected-list .badge {
    width: 100%;
    text-align: center;
    justify-content: center;
    padding-left: 25px; /* Balance the padding for the close button */
    padding-right: 25px;
  }
}

.selected-list .btn-close {
  font-size: 0.6em;
  opacity: 0.8;
  transition: opacity 0.2s ease;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.selected-list .btn-close:hover {
  opacity: 1;
}

/* Enhance content sections */
.task-details .p-3.bg-light {
  padding: 15px !important;
  font-size: 1.05rem;
  line-height: 1.5;
  margin-bottom: 20px;
  border-radius: 8px;
}

/* Add a light shadow to each content block */
.task-details .p-3.bg-light {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Status section styling */
.status-section {
  margin: 20px 0;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  border-left: 4px solid #6c757d;
}

.status-section strong {
  margin-right: 10px;
  font-size: 1.1rem;
  color: #333;
}

/* Make preserve-whitespace work for content */
.task-details .p-3.bg-light {
  white-space: pre-line;
  word-break: break-word;
}

/* Project Dropdown Styles */
.project-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 2px;
}

.project-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
  font-size: 0.95rem;
  color: #374151;
}

.project-dropdown-item:hover {
  background-color: #f8f9fa !important;
  color: #1f2937;
}

.project-dropdown-item .fa-times {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.project-dropdown-item:hover .fa-times {
  opacity: 1;
}

.project-dropdown-item .fa-times:hover {
  color: #dc2626 !important;
  transform: scale(1.1);
}

.project-dropdown-item:last-child {
  border-bottom: none;
}

.project-dropdown-empty {
  padding: 12px 16px;
  color: #6b7280;
  font-style: italic;
  text-align: center;
}

/* Project input container */
.project-input-container {
  position: relative;
  z-index: 100;
}

.project-input-container .form-control {
  padding-right: 40px;
}

.project-input-container .dropdown-icon {
  position: absolute;
  right: 12px;
  top: 38%;
  transform: translateY(-50%);
  color: #6b7280;
  cursor: pointer;
  font-size: 0.875rem;
  z-index: 10;
  padding: 4px;
  transition: color 0.2s ease;
}

.project-input-container .dropdown-icon:hover {
  color: #374151;
}

/* Task Comments Styles */
.comments-section {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #f8fafc;
  padding: 15px;
  margin-top: 15px;
}

.comment-item {
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.comment-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.comment-item:last-child {
  margin-bottom: 0;
}

.comment-item strong {
  color: #1e40af;
  font-weight: 600;
  font-size: 0.95rem;
}

.comment-item small {
  color: #64748b;
  font-size: 0.85rem;
}

.comment-item p {
  color: #374151;
  line-height: 1.5;
  margin-bottom: 0;
  margin-top: 8px;
  font-size: 0.95rem;
}

/* Comment form styles */
.comments-section .d-flex textarea {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 12px;
  font-size: 0.95rem;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.comments-section .d-flex textarea:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}

.comments-section .btn-primary {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  border: none;
  border-radius: 6px;
  padding: 12px 20px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.comments-section .btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.comments-section .btn-primary:disabled {
  background: #9ca3af;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Delete comment button */
.comment-item .btn-outline-danger {
  border: 1px solid #ef4444;
  color: #ef4444;
  background: transparent;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.comment-item .btn-outline-danger:hover {
  background-color: #ef4444;
  color: white;
  transform: scale(1.05);
}

/* Empty comments state */
.comments-section .text-muted {
  color: #64748b !important;
  font-style: italic;
  padding: 20px;
  text-align: center;
  background-color: #f1f5f9;
  border-radius: 6px;
  border: 1px dashed #cbd5e1;
}

/* Loading state */
.comments-section .text-center {
  padding: 20px;
  color: #64748b;
}

.comments-section .fa-spinner {
  color: #2563eb;
}

/* Comments section header */
.comments-section h5 {
  color: #1e40af;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
}

.comments-section h5 i {
  margin-right: 8px;
  color: #2563eb;
}

/* Responsive adjustments for comments */
@media (max-width: 768px) {
  .comments-section .d-flex {
    flex-direction: column;
    gap: 10px;
  }
  
  .comments-section .btn-primary {
    width: 100%;
    margin-top: 10px;
  }
  
  .comment-item {
    padding: 12px;
  }
  
  .comment-item .d-flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .comment-item .btn-outline-danger {
    align-self: flex-end;
  }
}

/* Responsive adjustments for project dropdown */
@media (max-width: 768px) {
  .project-dropdown {
    max-height: 150px;
  }
  
  .project-dropdown-item {
    padding: 10px 12px;
    font-size: 0.9rem;
  }
  
  /* Fix for selected individuals and collaborators text in responsive mode */
  .individual-cell .preserve-whitespace,
  .collaborator-cell .preserve-whitespace {
    padding: 5px;
    max-height: none;
    overflow-y: visible;
  }
  
  /* Ensure name items are visible and properly styled in responsive mode */
  .name-item {
    background-color: rgba(248, 249, 250, 0.8);
    border-left: 3px solid #6c757d;
    padding: 4px 6px;
    margin-bottom: 4px;
    border-radius: 4px;
    display: block;
    width: 100%;
    font-size: 0.85rem;
    color: #333333 !important; /* Force text color to be dark and readable */
    word-break: break-word;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .name-item.manual-collaborator {
    background-color: rgba(255, 243, 205, 0.8);
    border-left: 3px solid #ffc107;
    color: #856404 !important; /* Force text color for manual collaborators */
  }
  
  /* Ensure table cells for individuals and collaborators have proper spacing */
  .individual-cell, 
  .collaborator-cell {
    padding: 8px;
  }
  
  /* Improve readability of selected individuals in modal view */
  .task-modal .name-item-modal {
    display: block;
    width: 100%;
    margin: 4px 0;
    padding: 6px 8px;
    font-size: 0.9rem;
    background-color: #f0f7ff;
    border-left: 3px solid #1890ff;
    border-radius: 4px;
    color: #333333 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  /* Fix for selected collaborators badges in responsive mode */
  .selected-list .badge {
    display: inline-block;
    margin-bottom: 5px;
    font-size: 0.85rem !important;
    color: white !important;
    word-break: break-word;
  }
}