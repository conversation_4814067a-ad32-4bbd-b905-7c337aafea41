{"ast": null, "code": "var _jsxFileName = \"E:\\\\NEW ATLANTIC\\\\QU\\u1EA2N L\\xDD XE LOCAL HOST\\\\frontend\\\\src\\\\pages\\\\LeaveRequest.js\";\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Container, Row, Col, Card, Table, Badge, Button, Tabs, Tab, Modal, Form, Tooltip, OverlayTrigger, ListGroup, Alert, Spinner } from 'react-bootstrap';\nimport { useAuth } from '../context/AuthContext';\nimport API from '../services/api';\nimport '../css/LeaveRequest.css';\nimport { useNavigate } from 'react-router-dom';\nimport { format } from 'date-fns';\nimport { vi } from 'date-fns/locale';\nimport '../css/LeaveRequest.css';\nimport '../css/FileAttachments.css';\nimport { FaFileUpload, FaTrashAlt, FaFile, FaFilePdf, FaFileWord, FaFileExcel, FaFileImage, FaDownload } from 'react-icons/fa';\n\n// Hàm tính tổng thời gian nghỉ\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst calculateLeaveDuration = (startDate, startPeriod, endDate, endPeriod) => {\n  // Kiểm tra nếu không có dữ liệu thời gian\n  if (!startDate || !endDate) {\n    return 0;\n  }\n\n  // Đặt giá trị mặc định cho startPeriod và endPeriod nếu không có\n  const actualStartPeriod = startPeriod || 'full_day';\n  const actualEndPeriod = endPeriod || 'full_day';\n\n  // Chuyển đổi ngày thành đối tượng Date để so sánh\n  const start = new Date(startDate);\n  const end = new Date(endDate);\n\n  // Đặt giờ về 0 để so sánh chỉ ngày\n  start.setHours(0, 0, 0, 0);\n  end.setHours(0, 0, 0, 0);\n\n  // Hàm kiểm tra ngày có phải là Chủ nhật không\n  const isSunday = date => {\n    return date.getDay() === 0; // 0 là Chủ nhật trong JavaScript\n  };\n\n  // Hàm kiểm tra ngày có phải là thứ 7 không\n  const isSaturday = date => {\n    return date.getDay() === 6; // 6 là thứ 7 trong JavaScript\n  };\n\n  // Hàm đếm số ngày làm việc (không tính Chủ nhật) giữa hai ngày\n  const countWorkDays = (startDate, endDate) => {\n    let count = 0;\n    const currentDate = new Date(startDate);\n\n    // Lặp qua từng ngày giữa startDate và endDate\n    while (currentDate <= endDate) {\n      // Nếu không phải Chủ nhật thì tăng biến đếm\n      if (!isSunday(currentDate)) {\n        // Nếu là thứ 7, chỉ tính 0.5 ngày\n        if (isSaturday(currentDate)) {\n          count += 0.5;\n        } else {\n          count += 1.0;\n        }\n      }\n      // Tăng ngày lên 1\n      currentDate.setDate(currentDate.getDate() + 1);\n    }\n    return count;\n  };\n\n  // Nếu cùng một ngày\n  if (start.getTime() === end.getTime()) {\n    // Kiểm tra nếu là Chủ nhật thì không tính\n    if (isSunday(start)) {\n      return 0;\n    }\n\n    // Kiểm tra nếu là thứ 7\n    if (isSaturday(start)) {\n      // Thứ 7 chỉ tính buổi sáng\n      return 0.5;\n    }\n    if (actualStartPeriod === 'full_day' && actualEndPeriod === 'full_day') {\n      return 1;\n    } else if (actualStartPeriod === actualEndPeriod && actualStartPeriod !== 'full_day') {\n      return 0.5;\n    } else if (actualStartPeriod === 'morning' && actualEndPeriod === 'afternoon') {\n      return 1;\n    } else {\n      return 0.5;\n    }\n  } else {\n    // Tính số ngày giữa hai ngày\n    const diffTime = Math.abs(end - start);\n    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n\n    // Kiểm tra các ngày đặc biệt\n    const startIsSunday = isSunday(start);\n    const endIsSunday = isSunday(end);\n    const startIsSaturday = isSaturday(start);\n    const endIsSaturday = isSaturday(end);\n\n    // Trường hợp 2 ngày liền kề\n    if (diffDays === 1) {\n      // Trường hợp đặc biệt: từ thứ 6 đến Chủ nhật\n      if (!startIsSaturday && !startIsSunday && endIsSunday) {\n        // Nếu thứ 6 là cả ngày, tính 1.5 ngày (1 ngày thứ 6 + 0.5 ngày thứ 7 buổi sáng)\n        if (actualStartPeriod === 'full_day') {\n          return 1.5;\n        }\n        // Nếu thứ 6 chỉ buổi chiều, tính 1 ngày (0.5 ngày thứ 6 + 0.5 ngày thứ 7 buổi sáng)\n        else if (actualStartPeriod === 'afternoon') {\n          return 1;\n        }\n        // Nếu thứ 6 chỉ buổi sáng, tính 1 ngày (0.5 ngày thứ 6 + 0.5 ngày thứ 7 buổi sáng)\n        else {\n          return 1;\n        }\n      }\n\n      // Trường hợp từ thứ 6 đến thứ 7\n      if (!startIsSaturday && !startIsSunday && endIsSaturday) {\n        // Thứ 7 chỉ tính buổi sáng\n        if (actualStartPeriod === 'full_day') {\n          return 1.5;\n        } else if (actualStartPeriod === 'afternoon') {\n          return 1;\n        } else {\n          return 1;\n        }\n      }\n\n      // Nếu cả hai ngày đều là Chủ nhật\n      if (startIsSunday && endIsSunday) {\n        return 0;\n      }\n\n      // Nếu ngày bắt đầu là Chủ nhật\n      if (startIsSunday) {\n        // Chỉ tính ngày kết thúc\n        if (endIsSaturday) {\n          // Nếu ngày kết thúc là thứ 7, chỉ tính 0.5 ngày\n          return 0.5;\n        } else if (actualEndPeriod === 'full_day') {\n          return 1;\n        } else {\n          return 0.5;\n        }\n      }\n\n      // Nếu ngày kết thúc là Chủ nhật\n      if (endIsSunday) {\n        // Nếu ngày bắt đầu là thứ 7, chỉ tính 0.5 ngày (thứ 7 buổi sáng)\n        if (startIsSaturday) {\n          return 0.5;\n        }\n\n        // Nếu ngày bắt đầu không phải thứ 7\n        if (actualStartPeriod === 'full_day') {\n          return 1;\n        } else {\n          return 0.5;\n        }\n      }\n\n      // Nếu ngày bắt đầu là thứ 7\n      if (startIsSaturday) {\n        // Thứ 7 chỉ tính buổi sáng\n        return 0.5;\n      }\n\n      // Nếu ngày kết thúc là thứ 7\n      if (endIsSaturday) {\n        // Thứ 7 chỉ tính buổi sáng\n        if (actualStartPeriod === 'full_day') {\n          return 1.5;\n        } else if (actualStartPeriod === 'afternoon') {\n          return 1;\n        } else if (actualStartPeriod === 'morning') {\n          return 1;\n        }\n      }\n\n      // Các trường hợp thông thường\n      if (actualStartPeriod === 'afternoon' && actualEndPeriod === 'morning') {\n        return 1;\n      } else if (actualStartPeriod === 'afternoon' && (actualEndPeriod === 'full_day' || actualEndPeriod === 'afternoon')) {\n        return 1.5;\n      } else if ((actualStartPeriod === 'full_day' || actualStartPeriod === 'morning') && actualEndPeriod === 'morning') {\n        return 1.5;\n      } else {\n        return 2;\n      }\n    } else if (diffDays > 1) {\n      // Trường hợp đặc biệt: từ thứ 5 đến thứ 7\n      if (!startIsSaturday && !startIsSunday && endIsSaturday) {\n        // Tính số ngày từ thứ 5 đến thứ 6 (không tính thứ 7)\n        const thursdayToFriday = new Date(end);\n        thursdayToFriday.setDate(thursdayToFriday.getDate() - 1); // Lùi 1 ngày từ thứ 7 để lấy thứ 6\n\n        // Đếm số ngày làm việc từ ngày bắt đầu đến thứ 6\n        const workDaysBeforeSaturday = diffDays; // Số ngày từ thứ 5 đến thứ 6\n\n        // Điều chỉnh ngày đầu tiên nếu không phải cả ngày\n        let adjustedDays = workDaysBeforeSaturday;\n        if (!startIsSunday && !startIsSaturday && actualStartPeriod === 'afternoon') {\n          adjustedDays -= 0.5;\n        }\n\n        // Cộng thêm 0.5 ngày cho thứ 7 buổi sáng\n        return adjustedDays + 0.5;\n      }\n\n      // Trường hợp đặc biệt: từ thứ 5 đến chủ nhật\n      if (!startIsSaturday && !startIsSunday && endIsSunday) {\n        // Tính số ngày từ thứ 5 đến thứ 6 (không tính thứ 7 và chủ nhật)\n        const thursdayToFriday = new Date(end);\n        thursdayToFriday.setDate(thursdayToFriday.getDate() - 2); // Lùi 2 ngày từ chủ nhật để lấy thứ 6\n\n        // Đếm số ngày làm việc từ ngày bắt đầu đến thứ 6\n        const workDaysBeforeSaturday = diffDays - 1; // Số ngày từ thứ 5 đến thứ 6 (trừ chủ nhật)\n\n        // Điều chỉnh ngày đầu tiên nếu không phải cả ngày\n        let adjustedDays = workDaysBeforeSaturday;\n        if (!startIsSunday && !startIsSaturday && actualStartPeriod === 'afternoon') {\n          adjustedDays -= 0.5;\n        }\n\n        // Cộng thêm 0.5 ngày cho thứ 7 buổi sáng\n        return adjustedDays + 0.5;\n      }\n\n      // Sử dụng hàm đếm ngày làm việc để tính toán chính xác\n      let workDays = 0;\n\n      // Đếm số ngày làm việc (không tính chủ nhật)\n      const currentDate = new Date(start);\n      while (currentDate <= end) {\n        // Nếu không phải Chủ nhật thì tăng biến đếm\n        if (!isSunday(currentDate)) {\n          // Nếu là thứ 7, chỉ tính 0.5 ngày\n          if (isSaturday(currentDate)) {\n            workDays += 0.5;\n          } else {\n            workDays += 1;\n          }\n        }\n        // Tăng ngày lên 1\n        currentDate.setDate(currentDate.getDate() + 1);\n      }\n\n      // Điều chỉnh giá trị dựa trên thời gian bắt đầu và kết thúc\n      let adjustedDays = workDays;\n\n      // Điều chỉnh ngày đầu tiên nếu không phải cả ngày\n      if (!startIsSunday && !startIsSaturday && actualStartPeriod === 'afternoon') {\n        adjustedDays -= 0.5;\n      }\n\n      // Điều chỉnh ngày cuối cùng nếu không phải cả ngày\n      if (!endIsSunday && !endIsSaturday && actualEndPeriod === 'morning') {\n        adjustedDays -= 0.5;\n      } else if (endIsSaturday && actualEndPeriod === 'morning') {\n        // Thứ 7 đã được tính là 0.5 trong workDays, không cần điều chỉnh thêm\n      }\n      return adjustedDays;\n    }\n  }\n  return 0;\n};\n\n// Hàm định dạng hiển thị tổng thời gian nghỉ\nconst formatDuration = duration => {\n  if (duration === 0) return '0 ngày';\n  if (duration % 1 === 0) return `${duration} ngày`;\n  return `${Math.floor(duration)}.5 ngày`;\n};\n\n// Hàm tính toán và hiển thị thời gian nghỉ\nconst formatLeaveTime = (startDate, startPeriod, endDate, endPeriod) => {\n  // Kiểm tra nếu không có dữ liệu thời gian\n  if (!startDate || !endDate) {\n    return 'Không có dữ liệu';\n  }\n\n  // Đặt giá trị mặc định cho startPeriod và endPeriod nếu không có\n  const actualStartPeriod = startPeriod || 'full_day';\n  const actualEndPeriod = endPeriod || 'full_day';\n\n  // Chuyển đổi ngày thành đối tượng Date để so sánh\n  const start = new Date(startDate);\n  const end = new Date(endDate);\n\n  // Đặt giờ về 0 để so sánh chỉ ngày\n  start.setHours(0, 0, 0, 0);\n  end.setHours(0, 0, 0, 0);\n\n  // Định dạng ngày tháng\n  const formatDate = date => {\n    return format(new Date(date), 'dd/MM/yyyy', {\n      locale: vi\n    });\n  };\n\n  // Định dạng thời gian\n  const formatPeriod = period => {\n    switch (period) {\n      case 'morning':\n        return 'Sáng';\n      case 'afternoon':\n        return 'Chiều';\n      case 'full_day':\n        return 'Cả ngày';\n      default:\n        return 'Cả ngày';\n    }\n  };\n\n  // Tính toán số ngày nghỉ thực tế\n  const days = calculateLeaveDuration(startDate, actualStartPeriod, endDate, actualEndPeriod);\n\n  // Định dạng hiển thị số ngày\n  let duration = '';\n  if (days === 0) {\n    duration = '(0 ngày)';\n  } else if (days % 1 === 0) {\n    duration = `(${days} ngày)`;\n  } else {\n    duration = `(${Math.floor(days)}.5 ngày)`;\n  }\n  return `${formatDate(startDate)} ${formatPeriod(actualStartPeriod)} - ${formatDate(endDate)} ${formatPeriod(actualEndPeriod)} ${duration}`;\n};\n\n// Thêm component TablePagination\nconst TablePagination = ({\n  totalRecords,\n  recordsPerPage,\n  currentPage,\n  onPageChange\n}) => {\n  const pageNumbers = [];\n  const totalPages = Math.ceil(totalRecords / recordsPerPage);\n  let startPage = Math.max(1, currentPage - 2);\n  let endPage = Math.min(totalPages, startPage + 4);\n  if (endPage - startPage < 4) {\n    startPage = Math.max(1, endPage - 4);\n  }\n  for (let i = startPage; i <= endPage; i++) {\n    pageNumbers.push(i);\n  }\n  if (totalPages <= 1) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"table-pagination\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => onPageChange(1),\n      disabled: currentPage === 1,\n      className: \"pagination-button\",\n      children: \"\\xAB\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => onPageChange(currentPage - 1),\n      disabled: currentPage === 1,\n      className: \"pagination-button\",\n      children: '<'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this), pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => onPageChange(number),\n      className: `pagination-button ${currentPage === number ? 'active' : ''}`,\n      children: number\n    }, number, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => onPageChange(currentPage + 1),\n      disabled: currentPage === totalPages,\n      className: \"pagination-button\",\n      children: '>'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => onPageChange(totalPages),\n      disabled: currentPage === totalPages,\n      className: \"pagination-button\",\n      children: \"\\xBB\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 344,\n    columnNumber: 5\n  }, this);\n};\n\n// Danh sách các vai trò quản lý có thể duyệt đơn\nconst managerRoles = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'SUPER_ADMIN'];\nconst LeaveRequests = () => {\n  var _selectedRequest$empl, _selectedRequest$empl2, _selectedRequest$supe, _requestToDelete$empl;\n  const {\n    user\n  } = useAuth();\n  const token = localStorage.getItem('authToken');\n  const navigate = useNavigate();\n  const [leaveRequests, setLeaveRequests] = useState({\n    asEmployee: [],\n    asSupervisor: [],\n    allRequests: []\n  });\n  const [groupedRequests, setGroupedRequests] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n\n  // States for infinite scroll\n  const [displayedMyRequests, setDisplayedMyRequests] = useState([]);\n  const [myRequestsToShow, setMyRequestsToShow] = useState(20);\n  const [isMyRequestsLoading, setIsMyRequestsLoading] = useState(false);\n  const [displayedApprovalRequests, setDisplayedApprovalRequests] = useState([]);\n  const [approvalRequestsToShow, setApprovalRequestsToShow] = useState(20);\n  const [isApprovalRequestsLoading, setIsApprovalRequestsLoading] = useState(false);\n  const [displayedAllRequests, setDisplayedAllRequests] = useState([]);\n  const [allRequestsToShow, setAllRequestsToShow] = useState(20);\n  const [isAllRequestsLoading, setIsAllRequestsLoading] = useState(false);\n  const [showAttachmentModal, setShowAttachmentModal] = useState(false); // Modal riêng cho xem file đính kèm\n  const [selectedRequest, setSelectedRequest] = useState(null);\n  const [approvalData, setApprovalData] = useState({\n    status: 'approved',\n    comments: ''\n  });\n  const [tableFilters, setTableFilters] = useState({\n    employeeName: '',\n    supervisorName: '',\n    status: ''\n  });\n  const [tableCurrentPage, setTableCurrentPage] = useState(1);\n  const [tableRecordsPerPage, setTableRecordsPerPage] = useState(10);\n\n  // Thêm state cho modal chỉnh sửa\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [editData, setEditData] = useState({\n    startDate: '',\n    endDate: '',\n    reason: '',\n    status: '',\n    supervisor: '',\n    // Thêm trường supervisor\n    visibility: 'all',\n    visibleTo: [],\n    hiddenFrom: []\n  });\n\n  // State cho danh sách người dùng (để chọn trong visibility settings)\n  const [allUsers, setAllUsers] = useState([]);\n\n  // Thêm state cho modal xác nhận xóa\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [requestToDelete, setRequestToDelete] = useState(null);\n\n  // Thêm state cho thông báo thành công\n  const [successMessage, setSuccessMessage] = useState('');\n  const [showSuccessAlert, setShowSuccessAlert] = useState(false);\n\n  // State cho file đính kèm\n  const [attachments, setAttachments] = useState([]);\n  const [loadingAttachments, setLoadingAttachments] = useState(false);\n  const [attachmentError, setAttachmentError] = useState(null);\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [uploadError, setUploadError] = useState(null);\n  const [uploadingFiles, setUploadingFiles] = useState(false);\n  const fileInputRef = useRef(null);\n  useEffect(() => {\n    if (!token) {\n      setLoading(false);\n      return;\n    }\n    setLoading(true);\n    const fetchLeaveRequests = async () => {\n      try {\n        // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js\n        const res = await API.get('/leave-requests');\n        console.log('API trả về:', res.data);\n\n        // Kiểm tra dữ liệu trả về từ API\n        if (user && (user.role === 'SUPER_ADMIN' || user.role === 'LEVEL_II_MANAGER')) {\n          var _res$data$allRequests;\n          console.log(`Người dùng là ${user.role}`);\n          console.log('Số lượng đơn trong allRequests:', ((_res$data$allRequests = res.data.allRequests) === null || _res$data$allRequests === void 0 ? void 0 : _res$data$allRequests.length) || 0);\n        }\n\n        // Cập nhật state với dữ liệu từ API\n        setLeaveRequests({\n          asEmployee: res.data.asEmployee || [],\n          asSupervisor: res.data.asSupervisor || [],\n          allRequests: res.data.allRequests || []\n        });\n\n        // Nhóm các đơn có cùng thời gian và lý do\n        const grouped = groupLeaveRequests(res.data.asEmployee || []);\n        setGroupedRequests(grouped);\n      } catch (err) {\n        console.error('Lỗi lấy đơn nghỉ:', err);\n        setError('Không thể lấy danh sách đơn. Vui lòng thử lại.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchLeaveRequests();\n  }, [token]);\n\n  // Lấy danh sách tất cả người dùng (cho SUPER_ADMIN và LEVEL_II_MANAGER)\n  useEffect(() => {\n    if (user && (user.role === 'SUPER_ADMIN' || user.role === 'LEVEL_II_MANAGER')) {\n      const fetchAllUsers = async () => {\n        try {\n          console.log('Đang lấy danh sách người dùng...');\n          // Gọi API đúng đường dẫn, không thêm '/api' vì đã có trong baseURL\n          const res = await API.get('/users');\n          console.log('Kết quả API:', res);\n\n          // Kiểm tra cấu trúc dữ liệu trả về\n          if (res.data && res.data.users && Array.isArray(res.data.users)) {\n            setAllUsers(res.data.users);\n            console.log(`Đã tải danh sách ${res.data.users.length} người dùng cho thiết lập quyền xem đơn`);\n          } else {\n            console.error('Dữ liệu người dùng không đúng định dạng:', res.data);\n            setAllUsers([]); // Đặt giá trị mặc định là mảng rỗng\n          }\n        } catch (err) {\n          console.error('Lỗi lấy danh sách người dùng:', err.message);\n          console.error('Chi tiết lỗi:', err);\n          setAllUsers([]); // Đặt giá trị mặc định là mảng rỗng khi có lỗi\n        }\n      };\n      fetchAllUsers();\n    } else {\n      // Đảm bảo allUsers luôn là mảng rỗng khi không phải SUPER_ADMIN\n      setAllUsers([]);\n    }\n  }, [user]);\n\n  // Infinite scroll setup for My Requests\n  useEffect(() => {\n    const handleMyRequestsScroll = () => {\n      const container = document.getElementById('myRequestsContainer');\n      if (!container) return;\n      const {\n        scrollTop,\n        scrollHeight,\n        clientHeight\n      } = container;\n      if (scrollTop + clientHeight >= scrollHeight - 20 && !isMyRequestsLoading) {\n        loadMoreMyRequests();\n      }\n    };\n    const container = document.getElementById('myRequestsContainer');\n    if (container) {\n      container.addEventListener('scroll', handleMyRequestsScroll);\n      return () => container.removeEventListener('scroll', handleMyRequestsScroll);\n    }\n  }, [isMyRequestsLoading, myRequestsToShow, groupedRequests.length]);\n\n  // Infinite scroll setup for Approval Requests\n  useEffect(() => {\n    const handleApprovalRequestsScroll = () => {\n      const container = document.getElementById('approvalRequestsContainer');\n      if (!container) return;\n      const {\n        scrollTop,\n        scrollHeight,\n        clientHeight\n      } = container;\n      if (scrollTop + clientHeight >= scrollHeight - 20 && !isApprovalRequestsLoading) {\n        loadMoreApprovalRequests();\n      }\n    };\n    const container = document.getElementById('approvalRequestsContainer');\n    if (container) {\n      container.addEventListener('scroll', handleApprovalRequestsScroll);\n      return () => container.removeEventListener('scroll', handleApprovalRequestsScroll);\n    }\n  }, [isApprovalRequestsLoading, approvalRequestsToShow, leaveRequests.asSupervisor.length]);\n\n  // Infinite scroll setup for All Requests\n  useEffect(() => {\n    const handleAllRequestsScroll = () => {\n      const container = document.getElementById('allRequestsContainer');\n      if (!container) return;\n      const {\n        scrollTop,\n        scrollHeight,\n        clientHeight\n      } = container;\n      if (scrollTop + clientHeight >= scrollHeight - 20 && !isAllRequestsLoading) {\n        loadMoreAllRequests();\n      }\n    };\n    const container = document.getElementById('allRequestsContainer');\n    if (container) {\n      container.addEventListener('scroll', handleAllRequestsScroll);\n      return () => container.removeEventListener('scroll', handleAllRequestsScroll);\n    }\n  }, [isAllRequestsLoading, allRequestsToShow, leaveRequests.allRequests.length]);\n\n  // Update displayed requests when data changes\n  useEffect(() => {\n    setDisplayedMyRequests(groupedRequests.slice(0, myRequestsToShow));\n  }, [groupedRequests, myRequestsToShow]);\n  useEffect(() => {\n    setDisplayedApprovalRequests(leaveRequests.asSupervisor.slice(0, approvalRequestsToShow));\n  }, [leaveRequests.asSupervisor, approvalRequestsToShow]);\n  useEffect(() => {\n    const filteredRequests = filterTableRecords(tableFilters);\n    setDisplayedAllRequests(filteredRequests.slice(0, allRequestsToShow));\n  }, [leaveRequests.allRequests, tableFilters, allRequestsToShow]);\n\n  // Load more functions\n  const loadMoreMyRequests = () => {\n    if (isMyRequestsLoading || displayedMyRequests.length >= groupedRequests.length) return;\n    setIsMyRequestsLoading(true);\n    setTimeout(() => {\n      const nextRequestsToShow = Math.min(myRequestsToShow + 20, groupedRequests.length);\n      setMyRequestsToShow(nextRequestsToShow);\n      setIsMyRequestsLoading(false);\n    }, 300);\n  };\n  const loadMoreApprovalRequests = () => {\n    if (isApprovalRequestsLoading || displayedApprovalRequests.length >= leaveRequests.asSupervisor.length) return;\n    setIsApprovalRequestsLoading(true);\n    setTimeout(() => {\n      const nextRequestsToShow = Math.min(approvalRequestsToShow + 20, leaveRequests.asSupervisor.length);\n      setApprovalRequestsToShow(nextRequestsToShow);\n      setIsApprovalRequestsLoading(false);\n    }, 300);\n  };\n  const loadMoreAllRequests = () => {\n    const filteredRequests = filterTableRecords(tableFilters);\n    if (isAllRequestsLoading || displayedAllRequests.length >= filteredRequests.length) return;\n    setIsAllRequestsLoading(true);\n    setTimeout(() => {\n      const nextRequestsToShow = Math.min(allRequestsToShow + 20, filteredRequests.length);\n      setAllRequestsToShow(nextRequestsToShow);\n      setIsAllRequestsLoading(false);\n    }, 300);\n  };\n\n  // Hàm nhóm các đơn có cùng thời gian và lý do\n  const groupLeaveRequests = requests => {\n    const requestGroups = {};\n    requests.forEach(request => {\n      // Tạo key dựa trên startDate, startPeriod, endDate, endPeriod và reason\n      const startPeriod = request.startPeriod || 'full_day';\n      const endPeriod = request.endPeriod || 'full_day';\n      const key = `${request.startDate}_${startPeriod}_${request.endDate}_${endPeriod}_${request.reason}`;\n      if (!requestGroups[key]) {\n        requestGroups[key] = {\n          id: key,\n          _id: request._id,\n          // Thêm _id từ request đầu tiên\n          startDate: request.startDate,\n          startPeriod: request.startPeriod || 'full_day',\n          // Thêm startPeriod với giá trị mặc định\n          endDate: request.endDate,\n          endPeriod: request.endPeriod || 'full_day',\n          // Thêm endPeriod với giá trị mặc định\n          reason: request.reason,\n          createdAt: request.createdAt,\n          supervisors: [],\n          status: request.status,\n          comments: request.comments || '',\n          hasAttachments: request.hasAttachments || false,\n          attachmentCount: request.attachmentCount || 0,\n          requests: []\n        };\n      }\n\n      // Thêm supervisor vào nhóm\n      if (request.supervisor && !requestGroups[key].supervisors.some(s => s._id === request.supervisor._id)) {\n        requestGroups[key].supervisors.push(request.supervisor);\n      }\n\n      // Thêm request vào nhóm\n      requestGroups[key].requests.push(request);\n\n      // Nếu bất kỳ request nào là pending, nhóm sẽ hiển thị là pending\n      if (request.status === 'pending') {\n        requestGroups[key].status = 'pending';\n      }\n      // Nếu có bất kỳ request nào bị reject mà chưa có request nào pending, nhóm sẽ hiển thị là rejected\n      else if (request.status === 'rejected' && requestGroups[key].status !== 'pending') {\n        requestGroups[key].status = 'rejected';\n      }\n\n      // Tổng hợp comments từ các quản lý\n      if (request.comments && !requestGroups[key].comments.includes(request.comments)) {\n        var _request$supervisor;\n        requestGroups[key].comments += (requestGroups[key].comments ? '\\n' : '') + `${((_request$supervisor = request.supervisor) === null || _request$supervisor === void 0 ? void 0 : _request$supervisor.fullName) || 'Quản lý'}: ${request.comments}`;\n      }\n\n      // Cập nhật thông tin file đính kèm\n      if (request.hasAttachments) {\n        requestGroups[key].hasAttachments = true;\n        requestGroups[key].attachmentCount = Math.max(requestGroups[key].attachmentCount || 0, request.attachmentCount || 0);\n      }\n    });\n    return Object.values(requestGroups);\n  };\n  const handleCreateRequest = () => {\n    navigate('/leave-request/create');\n  };\n\n  // Hàm lấy danh sách file đính kèm\n  const fetchAttachments = async leaveRequestId => {\n    // Kiểm tra leaveRequestId có hợp lệ không\n    if (!leaveRequestId) {\n      console.error('ID đơn nghỉ phép không hợp lệ');\n      setAttachmentError('Không thể lấy danh sách file đính kèm');\n      setLoadingAttachments(false);\n      setAttachments([]); // Đặt danh sách file rỗng\n      return;\n    }\n\n    // Kiểm tra leaveRequestId có phải là chuỗi \"undefined\" không\n    if (leaveRequestId === 'undefined') {\n      console.error('ID đơn nghỉ phép là chuỗi \"undefined\"');\n      setAttachmentError('Không thể lấy danh sách file đính kèm');\n      setLoadingAttachments(false);\n      setAttachments([]); // Đặt danh sách file rỗng\n      return;\n    }\n    setLoadingAttachments(true);\n    setAttachmentError(null);\n    try {\n      console.log('Đang lấy danh sách file đính kèm cho đơn có ID:', leaveRequestId);\n      const response = await API.get(`/file-attachments/${leaveRequestId}/files`, {\n        headers: {\n          'x-auth-token': token\n        }\n      });\n      setAttachments(response.data);\n      console.log('Đã lấy danh sách file đính kèm:', response.data);\n    } catch (err) {\n      console.error('Lỗi khi lấy danh sách file đính kèm:', err);\n      setAttachmentError('Không thể tải danh sách file đính kèm');\n      setAttachments([]); // Đặt danh sách file rỗng\n    } finally {\n      setLoadingAttachments(false);\n    }\n  };\n\n  // Hàm xử lý khi chọn file\n  const handleFileChange = e => {\n    const files = Array.from(e.target.files);\n\n    // Kiểm tra số lượng file (tối đa 5 file)\n    if (selectedFiles.length + files.length > 5) {\n      setUploadError('Chỉ được phép tải lên tối đa 5 file');\n      return;\n    }\n\n    // Kiểm tra kích thước file (tối đa 10MB mỗi file)\n    const maxSize = 10 * 1024 * 1024; // 10MB\n    const oversizedFiles = files.filter(file => file.size > maxSize);\n    if (oversizedFiles.length > 0) {\n      setUploadError(`File ${oversizedFiles.map(f => f.name).join(', ')} vượt quá kích thước tối đa 10MB`);\n      return;\n    }\n\n    // Kiểm tra loại file\n    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'image/jpeg', 'image/png', 'image/gif', 'text/plain'];\n    const invalidFiles = files.filter(file => !allowedTypes.includes(file.type));\n    if (invalidFiles.length > 0) {\n      setUploadError(`File ${invalidFiles.map(f => f.name).join(', ')} không được hỗ trợ. Chỉ chấp nhận PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF và TXT.`);\n      return;\n    }\n\n    // Thêm các file hợp lệ vào danh sách\n    setSelectedFiles(prev => [...prev, ...files]);\n    setUploadError(null);\n\n    // Reset input để có thể chọn lại cùng một file\n    e.target.value = null;\n  };\n\n  // Hàm xóa file khỏi danh sách\n  const handleRemoveFile = index => {\n    setSelectedFiles(prev => prev.filter((_, i) => i !== index));\n  };\n\n  // Hàm lấy icon tương ứng với loại file\n  const getFileIcon = fileType => {\n    // Kiểm tra fileType có tồn tại không\n    if (!fileType) return /*#__PURE__*/_jsxDEV(FaFile, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 804,\n      columnNumber: 27\n    }, this);\n\n    // Chuyển đổi fileType thành chuỗi để đảm bảo có thể gọi includes()\n    const type = String(fileType).toLowerCase();\n    if (type.includes('pdf')) return /*#__PURE__*/_jsxDEV(FaFilePdf, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 38\n    }, this);\n    if (type.includes('word') || type.includes('msword') || type.includes('doc')) return /*#__PURE__*/_jsxDEV(FaFileWord, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 810,\n      columnNumber: 90\n    }, this);\n    if (type.includes('excel') || type.includes('sheet') || type.includes('xls')) return /*#__PURE__*/_jsxDEV(FaFileExcel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 811,\n      columnNumber: 90\n    }, this);\n    if (type.includes('image') || type.includes('jpg') || type.includes('jpeg') || type.includes('png') || type.includes('gif')) return /*#__PURE__*/_jsxDEV(FaFileImage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 812,\n      columnNumber: 137\n    }, this);\n    return /*#__PURE__*/_jsxDEV(FaFile, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 813,\n      columnNumber: 12\n    }, this);\n  };\n\n  // Hàm upload file\n  const uploadFiles = async leaveRequestId => {\n    if (selectedFiles.length === 0) return;\n\n    // Kiểm tra leaveRequestId có hợp lệ không\n    if (!leaveRequestId) {\n      console.error('ID đơn nghỉ phép không hợp lệ');\n      setUploadError('Không thể tải file lên. Vui lòng thử lại sau.');\n      return;\n    }\n    setUploadingFiles(true);\n    setUploadError(null);\n\n    // Hiển thị thông báo đang tải lên\n    setSuccessMessage('Đang tải file lên...');\n    setShowSuccessAlert(true);\n    try {\n      const formData = new FormData();\n      selectedFiles.forEach(file => {\n        formData.append('files', file);\n      });\n      await API.post(`/file-attachments/${leaveRequestId}/upload`, formData, {\n        headers: {\n          'x-auth-token': token,\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n\n      // Sau khi upload thành công, làm mới danh sách file\n      await fetchAttachments(leaveRequestId);\n\n      // Xóa danh sách file đã chọn\n      setSelectedFiles([]);\n\n      // Hiển thị thông báo thành công\n      setSuccessMessage('Tải file lên thành công');\n      setShowSuccessAlert(true);\n      setTimeout(() => setShowSuccessAlert(false), 3000);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Lỗi khi tải file lên:', err);\n      setUploadError('Lỗi khi tải file lên: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n    } finally {\n      setUploadingFiles(false);\n    }\n  };\n\n  // Hàm download file\n  const handleDownloadFile = async fileId => {\n    try {\n      // Kiểm tra selectedRequest và fileId có hợp lệ không\n      if (!selectedRequest || !selectedRequest._id) {\n        console.error('Không có đơn nghỉ phép được chọn');\n        alert('Không thể tải file. Vui lòng thử lại sau.');\n        return;\n      }\n      if (!fileId) {\n        console.error('ID file không hợp lệ');\n        alert('Không thể tải file. Vui lòng thử lại sau.');\n        return;\n      }\n      const response = await API.get(`/file-attachments/${selectedRequest._id}/files/${fileId}/download`, {\n        headers: {\n          'x-auth-token': token\n        },\n        responseType: 'blob'\n      });\n\n      // Tìm thông tin file trong danh sách attachments\n      const fileInfo = attachments.find(file => file._id === fileId);\n\n      // Tạo URL cho blob và tạo link tải xuống\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', fileInfo.originalName);\n      document.body.appendChild(link);\n      link.click();\n\n      // Dọn dẹp\n      link.parentNode.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (err) {\n      console.error('Lỗi khi tải file:', err);\n      alert('Không thể tải file. Vui lòng thử lại sau.');\n    }\n  };\n\n  // Hàm xóa file\n  const handleDeleteFile = async fileId => {\n    if (!confirm('Bạn có chắc chắn muốn xóa file này không?')) {\n      return;\n    }\n\n    // Kiểm tra selectedRequest và fileId có hợp lệ không\n    if (!selectedRequest || !selectedRequest._id) {\n      console.error('Không có đơn nghỉ phép được chọn');\n      alert('Không thể xóa file. Vui lòng thử lại sau.');\n      return;\n    }\n    if (!fileId) {\n      console.error('ID file không hợp lệ');\n      alert('Không thể xóa file. Vui lòng thử lại sau.');\n      return;\n    }\n    try {\n      await API.delete(`/file-attachments/${selectedRequest._id}/files/${fileId}`, {\n        headers: {\n          'x-auth-token': token\n        }\n      });\n\n      // Sau khi xóa thành công, làm mới danh sách file\n      await fetchAttachments(selectedRequest._id);\n\n      // Hiển thị thông báo thành công\n      setSuccessMessage('Xóa file thành công');\n      setShowSuccessAlert(true);\n      setTimeout(() => setShowSuccessAlert(false), 3000);\n    } catch (err) {\n      console.error('Lỗi khi xóa file:', err);\n      alert('Không thể xóa file. Vui lòng thử lại sau.');\n    }\n  };\n  const handleShowApprovalModal = async request => {\n    // Kiểm tra request có hợp lệ không\n    if (!request) {\n      console.error('Request không hợp lệ');\n      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');\n      setShowSuccessAlert(true);\n      setTimeout(() => setShowSuccessAlert(false), 3000);\n      return;\n    }\n\n    // Kiểm tra request có ID hợp lệ không\n    if (!request._id) {\n      console.error('Request không có ID hợp lệ:', request);\n      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');\n      setShowSuccessAlert(true);\n      setTimeout(() => setShowSuccessAlert(false), 3000);\n      return;\n    }\n    setSelectedRequest(request);\n    setApprovalData({\n      status: 'approved',\n      comments: ''\n    });\n    setShowModal(true);\n\n    // Lấy danh sách file đính kèm\n    try {\n      await fetchAttachments(request._id);\n    } catch (err) {\n      console.error('Lỗi khi lấy danh sách file đính kèm:', err);\n      // Không hiển thị lỗi cho người dùng vì modal đã được mở\n    }\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setSelectedRequest(null);\n  };\n\n  // Hàm hiển thị modal xem file đính kèm\n  const handleShowAttachmentModal = async request => {\n    // Kiểm tra request có hợp lệ không\n    if (!request) {\n      console.error('Request không hợp lệ');\n      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');\n      setShowSuccessAlert(true);\n      setTimeout(() => setShowSuccessAlert(false), 3000);\n      return;\n    }\n\n    // Kiểm tra request có ID hợp lệ không\n    if (!request._id) {\n      console.error('Request không có ID hợp lệ:', request);\n      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');\n      setShowSuccessAlert(true);\n      setTimeout(() => setShowSuccessAlert(false), 3000);\n      return;\n    }\n    setSelectedRequest(request);\n    setShowAttachmentModal(true);\n\n    // Lấy danh sách file đính kèm\n    try {\n      await fetchAttachments(request._id);\n    } catch (err) {\n      console.error('Lỗi khi lấy danh sách file đính kèm:', err);\n      // Không hiển thị lỗi cho người dùng vì modal đã được mở\n    }\n  };\n\n  // Hàm đóng modal xem file đính kèm\n  const handleCloseAttachmentModal = () => {\n    setShowAttachmentModal(false);\n    setSelectedRequest(null);\n  };\n  const handleApprovalChange = e => {\n    setApprovalData({\n      ...approvalData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmitApproval = async e => {\n    e.preventDefault();\n    try {\n      console.log(`Đang duyệt đơn nghỉ phép với ID: ${selectedRequest._id}`, approvalData);\n\n      // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js\n      await API.put(`/leave-requests/${selectedRequest._id}`, approvalData);\n\n      // Cập nhật trạng thái trong tất cả các danh sách\n      setLeaveRequests(prev => {\n        // Cập nhật trong danh sách asSupervisor\n        const updatedAsSupervisor = prev.asSupervisor.map(req => req._id === selectedRequest._id ? {\n          ...req,\n          status: approvalData.status,\n          comments: approvalData.comments\n        } : req);\n\n        // Cập nhật trong danh sách allRequests\n        const updatedAllRequests = prev.allRequests.map(req => req._id === selectedRequest._id ? {\n          ...req,\n          status: approvalData.status,\n          comments: approvalData.comments\n        } : req);\n\n        // Cập nhật trong danh sách asEmployee nếu có\n        const updatedAsEmployee = prev.asEmployee.map(req => req._id === selectedRequest._id ? {\n          ...req,\n          status: approvalData.status,\n          comments: approvalData.comments\n        } : req);\n        return {\n          asEmployee: updatedAsEmployee,\n          asSupervisor: updatedAsSupervisor,\n          allRequests: updatedAllRequests\n        };\n      });\n\n      // Cập nhật groupedRequests nếu đơn được duyệt/từ chối là của người dùng hiện tại\n      const isUserRequest = leaveRequests.asEmployee.some(req => req._id === selectedRequest._id);\n      if (isUserRequest) {\n        const updatedGrouped = groupLeaveRequests(leaveRequests.asEmployee.map(req => req._id === selectedRequest._id ? {\n          ...req,\n          status: approvalData.status,\n          comments: approvalData.comments\n        } : req));\n        setGroupedRequests(updatedGrouped);\n      }\n\n      // Hiển thị thông báo thành công\n      setSuccessMessage('Đã cập nhật trạng thái đơn thành công');\n      setShowSuccessAlert(true);\n      setTimeout(() => setShowSuccessAlert(false), 3000);\n\n      // Đóng modal\n      handleCloseModal();\n\n      // Làm mới dữ liệu sau 500ms để đảm bảo API đã cập nhật xong\n      setTimeout(async () => {\n        try {\n          const res = await API.get('/leave-requests');\n\n          // Cập nhật state với dữ liệu mới từ API\n          setLeaveRequests({\n            asEmployee: res.data.asEmployee || [],\n            asSupervisor: res.data.asSupervisor || [],\n            allRequests: res.data.allRequests || []\n          });\n\n          // Cập nhật groupedRequests\n          const grouped = groupLeaveRequests(res.data.asEmployee || []);\n          setGroupedRequests(grouped);\n        } catch (err) {\n          console.error('Lỗi khi làm mới dữ liệu:', err);\n        }\n      }, 500);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Lỗi khi cập nhật đơn xin nghỉ phép:', err);\n      setError('Không thể cập nhật đơn xin nghỉ phép. Vui lòng thử lại sau.');\n      // Hiển thị thông báo lỗi chi tiết\n      alert(`Lỗi: ${((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.msg) || err.message}`);\n    }\n  };\n\n  // Hàm mở modal chỉnh sửa đơn\n  const handleShowEditModal = request => {\n    var _request$supervisor2;\n    // Chuyển đổi định dạng ngày tháng từ ISO sang YYYY-MM-DD cho input date\n    const formatDateForInput = dateString => {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n    };\n\n    // Kiểm tra quyền chỉnh sửa\n    const isAdmin = user && user.role === 'SUPER_ADMIN';\n\n    // Kiểm tra quyền sở hữu đơn - xử lý nhiều trường hợp khác nhau\n    let isOwner = false;\n\n    // Trường hợp 1: request có thuộc tính employee với _id\n    if (request.employee && request.employee._id === user._id) {\n      isOwner = true;\n    }\n    // Trường hợp 2: request có thuộc tính employeeId\n    else if (request.employeeId === user._id) {\n      isOwner = true;\n    }\n    // Trường hợp 3: request có thuộc tính employee với id\n    else if (request.employee && request.employee.id === user._id) {\n      isOwner = true;\n    }\n    // Trường hợp 4: Đơn được tạo bởi người dùng hiện tại (dựa vào createdBy)\n    else if (request.createdBy === user._id) {\n      isOwner = true;\n    }\n\n    // Debug: Ghi log thông tin để kiểm tra\n    console.log('User ID:', user === null || user === void 0 ? void 0 : user._id);\n    console.log('Request:', request);\n    console.log('Is owner:', isOwner);\n    const isPending = request.status === 'pending';\n\n    // Kiểm tra quyền chỉnh sửa:\n    // 1. Admin có thể chỉnh sửa bất kỳ đơn nào\n    // 2. Nhân viên chỉ có thể chỉnh sửa đơn của mình và đơn phải ở trạng thái chờ duyệt\n    if (isAdmin) {\n      // Admin có thể chỉnh sửa bất kỳ đơn nào\n    } else if (isOwner && isPending) {\n      // Nhân viên có thể chỉnh sửa đơn của mình nếu đơn đang ở trạng thái chờ duyệt\n    } else {\n      // Các trường hợp khác không được phép chỉnh sửa\n      if (!isOwner) {\n        alert('Bạn không có quyền chỉnh sửa đơn này vì đây không phải đơn của bạn');\n      } else if (!isPending) {\n        alert('Bạn không thể chỉnh sửa đơn này vì đơn đã được duyệt/từ chối');\n      } else {\n        alert('Bạn không có quyền chỉnh sửa đơn này');\n      }\n      return;\n    }\n    setSelectedRequest(request);\n    setEditData({\n      startDate: formatDateForInput(request.startDate),\n      startPeriod: request.startPeriod || 'full_day',\n      endDate: formatDateForInput(request.endDate),\n      endPeriod: request.endPeriod || 'full_day',\n      reason: request.reason,\n      status: request.status,\n      supervisor: ((_request$supervisor2 = request.supervisor) === null || _request$supervisor2 === void 0 ? void 0 : _request$supervisor2._id) || request.supervisor || '',\n      // Lưu ID của cấp trên\n      visibility: request.visibility || 'all',\n      visibleTo: request.visibleTo || [],\n      hiddenFrom: request.hiddenFrom || []\n    });\n    setShowEditModal(true);\n\n    // Lấy danh sách file đính kèm\n    fetchAttachments(request._id);\n  };\n\n  // Hàm đóng modal chỉnh sửa\n  const handleCloseEditModal = () => {\n    setShowEditModal(false);\n    setSelectedRequest(null);\n  };\n\n  // Hàm xử lý thay đổi dữ liệu chỉnh sửa\n  const handleEditChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setEditData(prev => {\n      const newState = {\n        ...prev,\n        [name]: value\n      };\n\n      // Xử lý logic đặc biệt cho ngày tháng\n      if (name === 'startDate' || name === 'endDate') {\n        // Kiểm tra nếu ngày bắt đầu và kết thúc là cùng một ngày\n        const startDate = name === 'startDate' ? value : prev.startDate;\n        const endDate = name === 'endDate' ? value : prev.endDate;\n        const isSameDay = new Date(startDate).setHours(0, 0, 0, 0) === new Date(endDate).setHours(0, 0, 0, 0);\n\n        // Nếu cùng một ngày, áp dụng các quy tắc thời gian kết thúc\n        if (isSameDay) {\n          if (prev.startPeriod === 'full_day') {\n            newState.endPeriod = 'full_day';\n          } else if (prev.startPeriod === 'afternoon') {\n            newState.endPeriod = 'afternoon';\n          }\n        }\n      }\n      return newState;\n    });\n  };\n\n  // Hàm gửi dữ liệu chỉnh sửa\n  const handleSubmitEdit = async e => {\n    e.preventDefault();\n    try {\n      // Tạo dữ liệu để gửi lên server\n      const dataToSubmit = {\n        ...editData\n      };\n\n      // Nếu không phải admin, không cho phép thay đổi trạng thái và visibility\n      if (user.role !== 'SUPER_ADMIN') {\n        // Xóa các trường không được phép thay đổi\n        delete dataToSubmit.status;\n        delete dataToSubmit.visibility;\n        delete dataToSubmit.visibleTo;\n        delete dataToSubmit.hiddenFrom;\n      }\n      console.log(`Đang cập nhật đơn nghỉ phép với ID: ${selectedRequest._id}`, dataToSubmit);\n\n      // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js\n      const response = await API.put(`/leave-requests/${selectedRequest._id}`, dataToSubmit);\n\n      // Cập nhật state với dữ liệu mới\n      const updatedRequest = response.data;\n\n      // Cập nhật các danh sách đơn\n      setLeaveRequests(prev => {\n        // Cập nhật trong danh sách asEmployee\n        const updatedAsEmployee = prev.asEmployee.map(req => req._id === updatedRequest._id ? updatedRequest : req);\n\n        // Cập nhật trong danh sách asSupervisor\n        const updatedAsSupervisor = prev.asSupervisor.map(req => req._id === updatedRequest._id ? updatedRequest : req);\n\n        // Cập nhật trong danh sách allRequests\n        const updatedAllRequests = prev.allRequests.map(req => req._id === updatedRequest._id ? updatedRequest : req);\n        return {\n          asEmployee: updatedAsEmployee,\n          asSupervisor: updatedAsSupervisor,\n          allRequests: updatedAllRequests\n        };\n      });\n\n      // Cập nhật groupedRequests\n      const updatedGrouped = groupLeaveRequests(leaveRequests.asEmployee.map(req => req._id === updatedRequest._id ? updatedRequest : req));\n      setGroupedRequests(updatedGrouped);\n\n      // Hiển thị thông báo thành công\n      setSuccessMessage('Đã cập nhật đơn nghỉ phép thành công');\n      setShowSuccessAlert(true);\n      setTimeout(() => setShowSuccessAlert(false), 3000);\n      handleCloseEditModal();\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      console.error('Lỗi khi cập nhật đơn xin nghỉ phép:', err);\n      setError('Không thể cập nhật đơn xin nghỉ phép. Vui lòng thử lại sau.');\n      // Hiển thị thông báo lỗi chi tiết\n      alert(`Lỗi: ${((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.msg) || err.message}`);\n    }\n  };\n\n  // Hàm mở modal xác nhận xóa\n  const handleShowDeleteModal = request => {\n    // Kiểm tra quyền xóa\n    const isAdmin = user && user.role === 'SUPER_ADMIN';\n\n    // Kiểm tra quyền sở hữu đơn - xử lý nhiều trường hợp khác nhau\n    let isOwner = false;\n\n    // Trường hợp 1: request có thuộc tính employee với _id\n    if (request.employee && request.employee._id === user._id) {\n      isOwner = true;\n    }\n    // Trường hợp 2: request có thuộc tính employeeId\n    else if (request.employeeId === user._id) {\n      isOwner = true;\n    }\n    // Trường hợp 3: request có thuộc tính employee với id\n    else if (request.employee && request.employee.id === user._id) {\n      isOwner = true;\n    }\n    // Trường hợp 4: Đơn được tạo bởi người dùng hiện tại (dựa vào createdBy)\n    else if (request.createdBy === user._id) {\n      isOwner = true;\n    }\n\n    // Debug: Ghi log thông tin để kiểm tra\n    console.log('Delete - User ID:', user === null || user === void 0 ? void 0 : user._id);\n    console.log('Delete - Request:', request);\n    console.log('Delete - Is owner:', isOwner);\n    const isPending = request.status === 'pending';\n\n    // Kiểm tra quyền xóa:\n    // 1. Admin có thể xóa bất kỳ đơn nào\n    // 2. Nhân viên chỉ có thể xóa đơn của mình và đơn phải ở trạng thái chờ duyệt\n    if (isAdmin) {\n      // Admin có thể xóa bất kỳ đơn nào\n    } else if (isOwner && isPending) {\n      // Nhân viên có thể xóa đơn của mình nếu đơn đang ở trạng thái chờ duyệt\n    } else {\n      // Các trường hợp khác không được phép xóa\n      if (!isOwner) {\n        alert('Bạn không có quyền xóa đơn này vì đây không phải đơn của bạn');\n      } else if (!isPending) {\n        alert('Bạn không thể xóa đơn này vì đơn đã được duyệt/từ chối');\n      } else {\n        alert('Bạn không có quyền xóa đơn này');\n      }\n      return;\n    }\n    setRequestToDelete(request);\n    setShowDeleteModal(true);\n  };\n\n  // Hàm đóng modal xác nhận xóa\n  const handleCloseDeleteModal = () => {\n    setShowDeleteModal(false);\n    setRequestToDelete(null);\n  };\n\n  // Hàm xóa đơn nghỉ phép\n  const handleDeleteRequest = async () => {\n    try {\n      console.log(`Đang xóa đơn nghỉ phép với ID: ${requestToDelete._id}`);\n\n      // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js\n      await API.delete(`/leave-requests/${requestToDelete._id}`);\n\n      // Cập nhật state sau khi xóa\n      setLeaveRequests(prev => ({\n        asEmployee: prev.asEmployee.filter(req => req._id !== requestToDelete._id),\n        asSupervisor: prev.asSupervisor.filter(req => req._id !== requestToDelete._id),\n        allRequests: prev.allRequests.filter(req => req._id !== requestToDelete._id)\n      }));\n\n      // Cập nhật groupedRequests\n      const updatedGrouped = groupLeaveRequests(leaveRequests.asEmployee.filter(req => req._id !== requestToDelete._id));\n      setGroupedRequests(updatedGrouped);\n\n      // Hiển thị thông báo thành công\n      setSuccessMessage('Đã xóa đơn nghỉ phép thành công');\n      setShowSuccessAlert(true);\n      setTimeout(() => setShowSuccessAlert(false), 3000);\n      handleCloseDeleteModal();\n    } catch (err) {\n      var _err$response4, _err$response5, _err$response5$data, _err$response5$data$e;\n      console.error('Lỗi khi xóa đơn xin nghỉ phép:', err);\n\n      // Kiểm tra nếu lỗi là 500 nhưng đơn vẫn được xóa (lỗi khi tạo thông báo)\n      if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 500 && (_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && (_err$response5$data$e = _err$response5$data.error) !== null && _err$response5$data$e !== void 0 && _err$response5$data$e.includes('Notification validation failed')) {\n        // Vẫn cập nhật UI như đã xóa thành công\n        setLeaveRequests(prev => ({\n          asEmployee: prev.asEmployee.filter(req => req._id !== requestToDelete._id),\n          asSupervisor: prev.asSupervisor.filter(req => req._id !== requestToDelete._id),\n          allRequests: prev.allRequests.filter(req => req._id !== requestToDelete._id)\n        }));\n\n        // Cập nhật groupedRequests\n        const updatedGrouped = groupLeaveRequests(leaveRequests.asEmployee.filter(req => req._id !== requestToDelete._id));\n        setGroupedRequests(updatedGrouped);\n\n        // Hiển thị thông báo thành công\n        setSuccessMessage('Đã xóa đơn nghỉ phép thành công (có lỗi thông báo)');\n        setShowSuccessAlert(true);\n        setTimeout(() => setShowSuccessAlert(false), 3000);\n        handleCloseDeleteModal();\n      } else {\n        var _err$response6, _err$response6$data;\n        setError('Không thể xóa đơn xin nghỉ phép. Vui lòng thử lại sau.');\n        // Hiển thị thông báo lỗi chi tiết\n        alert(`Lỗi: ${((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.msg) || err.message}`);\n      }\n    }\n  };\n  const getStatusBadge = status => {\n    switch (status) {\n      case 'pending':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"warning\",\n          children: \"Ch\\u1EDD duy\\u1EC7t\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1428,\n          columnNumber: 16\n        }, this);\n      case 'approved':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"success\",\n          children: \"\\u0110\\xE3 duy\\u1EC7t\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1430,\n          columnNumber: 16\n        }, this);\n      case 'rejected':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"danger\",\n          children: \"\\u0110\\xE3 t\\u1EEB ch\\u1ED1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1432,\n          columnNumber: 16\n        }, this);\n      case 'cancelled':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"secondary\",\n          children: \"\\u0110\\xE3 h\\u1EE7y\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1434,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"secondary\",\n          children: \"Kh\\xF4ng x\\xE1c \\u0111\\u1ECBnh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1436,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Hiển thị biểu tượng file đính kèm\n  const getAttachmentBadge = request => {\n    if (request && request.hasAttachments) {\n      // Kiểm tra xem request có ID hợp lệ không\n      const hasValidId = request._id || request.requests && request.requests.length > 0 && request.requests[0]._id;\n      if (!hasValidId) {\n        console.warn('Đơn nghỉ phép không có ID hợp lệ:', request);\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"secondary\",\n          title: \"Kh\\xF4ng th\\u1EC3 xem file \\u0111\\xEDnh k\\xE8m\",\n          children: [/*#__PURE__*/_jsxDEV(FaFile, {\n            className: \"me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1450,\n            columnNumber: 13\n          }, this), \" \", request.attachmentCount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1449,\n          columnNumber: 11\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n        placement: \"top\",\n        overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n          children: [\"Xem \", request.attachmentCount, \" file \\u0111\\xEDnh k\\xE8m\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1458,\n          columnNumber: 20\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-info\",\n          size: \"sm\",\n          className: \"d-flex align-items-center file-attachment-btn\",\n          onClick: () => {\n            // Tìm ID hợp lệ cho đơn nghỉ phép\n            let validRequest = {\n              ...request\n            };\n\n            // Nếu request không có _id nhưng có requests array, sử dụng _id của request đầu tiên\n            if (!validRequest._id && validRequest.requests && validRequest.requests.length > 0) {\n              validRequest._id = validRequest.requests[0]._id;\n            }\n\n            // Mở modal duyệt đơn\n            handleShowApprovalModal(validRequest);\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaFile, {\n            className: \"me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1477,\n            columnNumber: 13\n          }, this), \" \", request.attachmentCount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1460,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1456,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-muted\",\n      children: \"-\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1482,\n      columnNumber: 12\n    }, this);\n  };\n\n  // Hiển thị biểu tượng file đính kèm cho tab \"Đơn xin nghỉ phép của tôi\"\n  const getMyRequestAttachmentBadge = request => {\n    if (request && request.hasAttachments) {\n      // Kiểm tra xem request có ID hợp lệ không\n      const hasValidId = request._id || request.requests && request.requests.length > 0 && request.requests[0]._id;\n      if (!hasValidId) {\n        console.warn('Đơn nghỉ phép không có ID hợp lệ:', request);\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"secondary\",\n          title: \"Kh\\xF4ng th\\u1EC3 xem file \\u0111\\xEDnh k\\xE8m\",\n          children: [/*#__PURE__*/_jsxDEV(FaFile, {\n            className: \"me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1495,\n            columnNumber: 13\n          }, this), \" \", request.attachmentCount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1494,\n          columnNumber: 11\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n        placement: \"top\",\n        overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n          children: [\"Xem \", request.attachmentCount, \" file \\u0111\\xEDnh k\\xE8m\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1503,\n          columnNumber: 20\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-info\",\n          size: \"sm\",\n          className: \"d-flex align-items-center file-attachment-btn\",\n          onClick: () => {\n            // Tìm ID hợp lệ cho đơn nghỉ phép\n            let validRequest = {\n              ...request\n            };\n\n            // Nếu request không có _id nhưng có requests array, sử dụng _id của request đầu tiên\n            if (!validRequest._id && validRequest.requests && validRequest.requests.length > 0) {\n              validRequest._id = validRequest.requests[0]._id;\n            }\n\n            // Mở modal xem file đính kèm\n            handleShowAttachmentModal(validRequest);\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaFile, {\n            className: \"me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1522,\n            columnNumber: 13\n          }, this), \" \", request.attachmentCount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1505,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1501,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-muted\",\n      children: \"-\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1527,\n      columnNumber: 12\n    }, this);\n  };\n  const formatDate = dateString => {\n    if (!dateString) return '';\n    try {\n      // Xử lý timezone để đảm bảo hiển thị đúng ngày theo giờ Việt Nam\n      const date = new Date(dateString);\n\n      // Tạo một đối tượng Date mới với giờ là 12:00 giờ Việt Nam để tránh vấn đề timezone\n      // Lấy các thành phần ngày tháng năm từ đối tượng Date gốc\n      const year = date.getFullYear();\n      const month = date.getMonth();\n      const day = date.getDate();\n\n      // Tạo đối tượng Date mới với giờ cố định là 12:00\n      const adjustedDate = new Date(year, month, day, 12, 0, 0);\n      console.log(`Ngày gốc: ${dateString}, Ngày hiển thị: ${adjustedDate.toISOString()}`);\n\n      // Format ngày theo định dạng Việt Nam\n      return format(adjustedDate, 'dd/MM/yyyy', {\n        locale: vi\n      });\n    } catch (error) {\n      console.error('Lỗi khi format ngày:', error, dateString);\n      return 'Ngày không hợp lệ';\n    }\n  };\n\n  // Hàm định dạng thời gian nghỉ với period\n  const formatLeaveTime = (startDate, startPeriod, endDate, endPeriod) => {\n    const start = formatDate(startDate);\n    const end = formatDate(endDate);\n    const startText = startPeriod && startPeriod !== 'full_day' ? `${start} (${startPeriod === 'morning' ? 'Sáng' : 'Chiều'})` : start;\n    const endText = endPeriod && endPeriod !== 'full_day' ? `${end} (${endPeriod === 'morning' ? 'Sáng' : 'Chiều'})` : end;\n    return `${startText} - ${endText}`;\n  };\n\n  // Render danh sách quản lý\n  const renderSupervisors = supervisors => {\n    if (!supervisors || supervisors.length === 0) return 'Chưa xác định';\n    if (supervisors.length === 1) {\n      return supervisors[0].fullName || 'Chưa xác định';\n    }\n    return /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n      placement: \"right\",\n      overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n        id: \"supervisors-tooltip\",\n        children: supervisors.map(sup => sup.fullName).join(', ')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1584,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [supervisors[0].fullName, \" v\\xE0 \", supervisors.length - 1, \" kh\\xE1c\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1589,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1581,\n      columnNumber: 7\n    }, this);\n  };\n  const handleTableFilterChange = e => {\n    setTableFilters({\n      ...tableFilters,\n      [e.target.name]: e.target.value\n    });\n  };\n  const resetTableFilters = () => {\n    setTableFilters({\n      employeeName: '',\n      supervisorName: '',\n      status: ''\n    });\n  };\n\n  // Calculate leave request statistics for all employees\n  const calculateLeaveStatistics = () => {\n    if (!leaveRequests.allRequests || leaveRequests.allRequests.length === 0) {\n      return [];\n    }\n    const currentYear = new Date().getFullYear();\n    const employeeStats = {};\n    const processedRequests = new Set(); // Track processed request IDs to avoid duplicates\n\n    leaveRequests.allRequests.forEach(request => {\n      var _request$employee, _request$employee2;\n      const requestYear = new Date(request.createdAt).getFullYear();\n      const requestMonth = new Date(request.createdAt).getMonth();\n      const employeeId = (_request$employee = request.employee) === null || _request$employee === void 0 ? void 0 : _request$employee._id;\n      const employeeName = ((_request$employee2 = request.employee) === null || _request$employee2 === void 0 ? void 0 : _request$employee2.fullName) || 'Chưa xác định';\n      if (!employeeId || requestYear !== currentYear) return;\n\n      // Create unique key based on employee + dates only (ignore reason)\n      // This treats requests on same dates as one request regardless of reason\n      const uniqueKey = `${employeeId}-${request.startDate}-${request.endDate}-${request.startPeriod || ''}-${request.endPeriod || ''}`;\n      if (processedRequests.has(uniqueKey)) {\n        return; // Skip duplicate request (same employee, same dates, same reason)\n      }\n      processedRequests.add(uniqueKey);\n      if (!employeeStats[employeeId]) {\n        employeeStats[employeeId] = {\n          employeeName,\n          totalRequests: 0,\n          totalDays: 0,\n          monthlyRequests: {},\n          approvedRequests: 0,\n          pendingRequests: 0,\n          rejectedRequests: 0\n        };\n      }\n      const stats = employeeStats[employeeId];\n      stats.totalRequests++;\n\n      // Calculate leave days\n      const leaveDays = calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod);\n      stats.totalDays += leaveDays;\n\n      // Track monthly requests\n      const monthKey = `${requestYear}-${requestMonth}`;\n      stats.monthlyRequests[monthKey] = (stats.monthlyRequests[monthKey] || 0) + 1;\n\n      // Track by status\n      if (request.status === 'approved') stats.approvedRequests++;else if (request.status === 'pending') stats.pendingRequests++;else if (request.status === 'rejected') stats.rejectedRequests++;\n    });\n    return Object.values(employeeStats).sort((a, b) => a.employeeName.localeCompare(b.employeeName));\n  };\n\n  // Check if employee has exceeded monthly limit\n  const checkMonthlyLimit = employeeStats => {\n    const currentMonth = `${new Date().getFullYear()}-${new Date().getMonth()}`;\n    return (employeeStats.monthlyRequests[currentMonth] || 0) >= 1;\n  };\n\n  // Check if employee has exceeded annual limit\n  const checkAnnualLimit = employeeStats => {\n    return employeeStats.totalRequests >= 12;\n  };\n  const filterTableRecords = filters => {\n    // Kiểm tra nếu allRequests là undefined hoặc null\n    if (!leaveRequests.allRequests || leaveRequests.allRequests.length === 0) {\n      console.log('Không có đơn nghỉ phép nào để lọc');\n      return [];\n    }\n    return leaveRequests.allRequests.filter(request => {\n      var _request$employee3, _request$supervisor3;\n      // Kiểm tra nếu employee hoặc supervisor là undefined hoặc null\n      const employeeFullName = ((_request$employee3 = request.employee) === null || _request$employee3 === void 0 ? void 0 : _request$employee3.fullName) || '';\n      const supervisorFullName = ((_request$supervisor3 = request.supervisor) === null || _request$supervisor3 === void 0 ? void 0 : _request$supervisor3.fullName) || '';\n      const matchesEmployee = filters.employeeName ? employeeFullName.toLowerCase().includes(filters.employeeName.toLowerCase()) : true;\n      const matchesSupervisor = filters.supervisorName ? supervisorFullName.toLowerCase().includes(filters.supervisorName.toLowerCase()) : true;\n      const matchesStatus = filters.status ? request.status === filters.status : true;\n      return matchesEmployee && matchesSupervisor && matchesStatus;\n    });\n  };\n  const getTablePaginatedRecords = records => {\n    // Kiểm tra nếu records là undefined hoặc null\n    if (!records || records.length === 0) {\n      console.log('Không có đơn nghỉ phép nào để hiển thị');\n      return [];\n    }\n    const filteredRecords = filterTableRecords(tableFilters);\n    console.log(`Số lượng đơn sau khi lọc: ${filteredRecords.length}`);\n    const startIndex = (tableCurrentPage - 1) * tableRecordsPerPage;\n    const endIndex = startIndex + tableRecordsPerPage;\n    return filteredRecords.slice(startIndex, endIndex);\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"mt-4\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"B\\u1EA1n c\\u1EA7n \\u0111\\u0103ng nh\\u1EADp \\u0111\\u1EC3 xem \\u0111\\u01A1n xin ngh\\u1EC9 ph\\xE9p\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1730,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: () => navigate('/login'),\n            children: \"\\u0110\\u0103ng nh\\u1EADp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1731,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1729,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1728,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1727,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"mt-4\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0110ang t\\u1EA3i d\\u1EEF li\\u1EC7u...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1745,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1744,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1743,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1742,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"mt-4\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-danger\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1757,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: () => window.location.reload(),\n            children: \"Th\\u1EED l\\u1EA1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1758,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1756,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1755,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1754,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"leave-requests-container\",\n    children: [showSuccessAlert && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"success\",\n      className: \"position-fixed top-0 start-50 translate-middle-x mt-3\",\n      style: {\n        zIndex: 1050\n      },\n      onClose: () => setShowSuccessAlert(false),\n      dismissible: true,\n      children: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1771,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Qu\\u1EA3n l\\xFD ngh\\u1EC9 ph\\xE9p\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1783,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1782,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: \"auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleCreateRequest,\n          children: \"T\\u1EA1o \\u0111\\u01A1n xin ngh\\u1EC9 ph\\xE9p\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1786,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1785,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1781,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      defaultActiveKey: \"my-requests\",\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"my-requests\",\n        title: \"\\u0110\\u01A1n xin ngh\\u1EC9 ph\\xE9p c\\u1EE7a t\\xF4i\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"employee-simple-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"employee-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                margin: '0',\n                color: '#333',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-calendar-alt\",\n                style: {\n                  marginRight: '8px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1797,\n                columnNumber: 17\n              }, this), \"\\u0110\\u01A1n xin ngh\\u1EC9 ph\\xE9p c\\u1EE7a t\\xF4i\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1796,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"employee-count\",\n              children: [groupedRequests.length, \" \\u0111\\u01A1n xin ngh\\u1EC9 ph\\xE9p\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1800,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1795,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"employee-list-simple\",\n            id: \"myRequestsContainer\",\n            children: [displayedMyRequests.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-employees\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-employees-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-calendar-times\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1810,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1809,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"B\\u1EA1n ch\\u01B0a c\\xF3 \\u0111\\u01A1n xin ngh\\u1EC9 ph\\xE9p n\\xE0o\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1812,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1808,\n              columnNumber: 17\n            }, this) : displayedMyRequests.map((request, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"employee-card-simple\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"employee-info-main\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-avatar\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-calendar-check\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1819,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1818,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: [\"\\u0110\\u01A1n ngh\\u1EC9 ph\\xE9p #\", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1822,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-calendar\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1823,\n                      columnNumber: 28\n                    }, this), \" \", formatDate(request.createdAt)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1823,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-clock\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1824,\n                      columnNumber: 28\n                    }, this), \" \", formatLeaveTime(request.startDate, request.startPeriod, request.endDate, request.endPeriod)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1824,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: formatDuration(calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1825,\n                      columnNumber: 28\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1825,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1821,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1817,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"employee-contact\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-user-tie\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1830,\n                    columnNumber: 26\n                  }, this), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"C\\u1EA5p tr\\xEAn:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1830,\n                    columnNumber: 62\n                  }, this), \" \", renderSupervisors(request.supervisors)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1830,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1831,\n                    columnNumber: 26\n                  }, this), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"L\\xFD do:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1831,\n                    columnNumber: 61\n                  }, this), \" \", request.reason]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1831,\n                  columnNumber: 23\n                }, this), request.comments && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-sticky-note\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1833,\n                    columnNumber: 28\n                  }, this), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Nh\\u1EADn x\\xE9t:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1833,\n                    columnNumber: 67\n                  }, this), \" \", request.comments]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1833,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1829,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"employee-work-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-info-circle\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1839,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Tr\\u1EA1ng th\\xE1i:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1840,\n                    columnNumber: 25\n                  }, this), \" \", getStatusBadge(request.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1838,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-paperclip\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1843,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"File \\u0111\\xEDnh k\\xE8m:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1844,\n                    columnNumber: 25\n                  }, this), \" \", getMyRequestAttachmentBadge(request)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1842,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1837,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"employee-actions\",\n                children: request.status === 'pending' && request.requests && request.requests.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn-view\",\n                    onClick: () => {\n                      const enhancedRequest = {\n                        ...request.requests[0],\n                        employee: {\n                          _id: user._id,\n                          fullName: user.fullName\n                        },\n                        employeeId: user._id,\n                        createdBy: user._id\n                      };\n                      handleShowEditModal(enhancedRequest);\n                    },\n                    title: \"Ch\\u1EC9nh s\\u1EEDa \\u0111\\u01A1n\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1864,\n                      columnNumber: 29\n                    }, this), \" Ch\\u1EC9nh s\\u1EEDa\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1851,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn-delete\",\n                    onClick: () => {\n                      const enhancedRequest = {\n                        ...request.requests[0],\n                        employee: {\n                          _id: user._id,\n                          fullName: user.fullName\n                        },\n                        employeeId: user._id,\n                        createdBy: user._id\n                      };\n                      handleShowDeleteModal(enhancedRequest);\n                    },\n                    title: \"X\\xF3a \\u0111\\u01A1n\",\n                    style: {\n                      background: '#dc3545',\n                      color: 'white'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-trash-alt\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1880,\n                      columnNumber: 29\n                    }, this), \" X\\xF3a\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1866,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1848,\n                columnNumber: 21\n              }, this)]\n            }, request.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1816,\n              columnNumber: 19\n            }, this)), isMyRequestsLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-more\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1892,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0110ang t\\u1EA3i th\\xEAm...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1893,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1891,\n              columnNumber: 17\n            }, this), displayedMyRequests.length > 0 && displayedMyRequests.length >= groupedRequests.length && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"end-of-results\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u0110\\xE3 hi\\u1EC3n th\\u1ECB t\\u1EA5t c\\u1EA3 \", groupedRequests.length, \" \\u0111\\u01A1n xin ngh\\u1EC9 ph\\xE9p\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1900,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1899,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1806,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1794,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1793,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"approval-requests\",\n        title: \"\\u0110\\u01A1n c\\u1EA7n duy\\u1EC7t\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"employee-simple-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"employee-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                margin: '0',\n                color: '#333',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user-check\",\n                style: {\n                  marginRight: '8px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1911,\n                columnNumber: 17\n              }, this), \"\\u0110\\u01A1n c\\u1EA7n duy\\u1EC7t\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1910,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"employee-count\",\n              children: [leaveRequests.asSupervisor.length, \" \\u0111\\u01A1n c\\u1EA7n duy\\u1EC7t\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1914,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1909,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"employee-list-simple\",\n            id: \"approvalRequestsContainer\",\n            children: [displayedApprovalRequests.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-employees\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-employees-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-calendar-check\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1924,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1923,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Kh\\xF4ng c\\xF3 \\u0111\\u01A1n xin ngh\\u1EC9 ph\\xE9p n\\xE0o c\\u1EA7n duy\\u1EC7t\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1926,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1922,\n              columnNumber: 17\n            }, this) : displayedApprovalRequests.map((request, index) => {\n              var _request$employee4, _request$employee5;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"employee-card-simple\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-info-main\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"employee-avatar\",\n                    children: (((_request$employee4 = request.employee) === null || _request$employee4 === void 0 ? void 0 : _request$employee4.fullName) || request.employeeName || 'N/A').charAt(0).toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1932,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"employee-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: ((_request$employee5 = request.employee) === null || _request$employee5 === void 0 ? void 0 : _request$employee5.fullName) || request.employeeName || 'Chưa xác định'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1936,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-calendar\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1937,\n                        columnNumber: 28\n                      }, this), \" \", formatDate(request.createdAt)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1937,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-clock\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1938,\n                        columnNumber: 28\n                      }, this), \" \", formatLeaveTime(request.startDate, request.startPeriod, request.endDate, request.endPeriod)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1938,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: formatDuration(calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1939,\n                        columnNumber: 28\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1939,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1935,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1931,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-contact\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-comment\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1944,\n                      columnNumber: 26\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"L\\xFD do:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1944,\n                      columnNumber: 61\n                    }, this), \" \", request.reason]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1944,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1943,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-work-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-info-circle\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1949,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tr\\u1EA1ng th\\xE1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1950,\n                      columnNumber: 25\n                    }, this), \" \", getStatusBadge(request.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1948,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-paperclip\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1953,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"File \\u0111\\xEDnh k\\xE8m:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1954,\n                      columnNumber: 25\n                    }, this), \" \", getAttachmentBadge(request)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1952,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1947,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-actions\",\n                  children: request.status === 'pending' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn-view\",\n                    onClick: () => handleShowApprovalModal(request),\n                    style: {\n                      background: '#28a745',\n                      color: 'white'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-check-circle\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1965,\n                      columnNumber: 27\n                    }, this), \" Duy\\u1EC7t/T\\u1EEB ch\\u1ED1i\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1960,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1958,\n                  columnNumber: 21\n                }, this)]\n              }, request._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1930,\n                columnNumber: 19\n              }, this);\n            }), isApprovalRequestsLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-more\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1976,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0110ang t\\u1EA3i th\\xEAm...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1977,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1975,\n              columnNumber: 17\n            }, this), displayedApprovalRequests.length > 0 && displayedApprovalRequests.length >= leaveRequests.asSupervisor.length && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"end-of-results\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u0110\\xE3 hi\\u1EC3n th\\u1ECB t\\u1EA5t c\\u1EA3 \", leaveRequests.asSupervisor.length, \" \\u0111\\u01A1n c\\u1EA7n duy\\u1EC7t\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1984,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1983,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1920,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1908,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1907,\n        columnNumber: 9\n      }, this), user && (user.role === 'SUPER_ADMIN' || user.role === 'LEVEL_II_MANAGER') && /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"all-requests\",\n        title: \"T\\u1EA5t c\\u1EA3 \\u0111\\u01A1n ngh\\u1EC9 ph\\xE9p\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"employee-table-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"leave-statistics-banner\",\n            style: {\n              background: 'linear-gradient(135deg, #3498db, #2980b9)',\n              color: 'white',\n              padding: '20px',\n              borderRadius: '10px',\n              marginBottom: '20px',\n              boxShadow: '0 4px 15px rgba(0,0,0,0.1)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 15px 0',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-chart-bar\",\n                style: {\n                  marginRight: '10px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2005,\n                columnNumber: 19\n              }, this), \"T\\xF3m t\\u1EAFt \\u0111\\u01A1n ngh\\u1EC9 ph\\xE9p n\\u0103m \", new Date().getFullYear()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2004,\n              columnNumber: 17\n            }, this), (() => {\n              const stats = calculateLeaveStatistics();\n              const totalEmployees = stats.length;\n              const totalRequests = stats.reduce((sum, emp) => sum + emp.totalRequests, 0);\n              const totalDays = stats.reduce((sum, emp) => sum + emp.totalDays, 0);\n              const employeesAtLimit = stats.filter(emp => checkAnnualLimit(emp)).length;\n              const employeesAtMonthlyLimit = stats.filter(emp => checkMonthlyLimit(emp)).length;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-card\",\n                    style: {\n                      background: 'rgba(255,255,255,0.2)',\n                      padding: '15px',\n                      borderRadius: '8px',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '24px',\n                        fontWeight: 'bold'\n                      },\n                      children: totalEmployees\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2021,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '14px',\n                        opacity: '0.9'\n                      },\n                      children: \"Nh\\xE2n vi\\xEAn c\\xF3 \\u0111\\u01A1n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2022,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2020,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2019,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-card\",\n                    style: {\n                      background: 'rgba(255,255,255,0.2)',\n                      padding: '15px',\n                      borderRadius: '8px',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '24px',\n                        fontWeight: 'bold'\n                      },\n                      children: totalRequests\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2027,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '14px',\n                        opacity: '0.9'\n                      },\n                      children: \"T\\u1ED5ng s\\u1ED1 \\u0111\\u01A1n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2028,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2026,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2025,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-card\",\n                    style: {\n                      background: 'rgba(255,255,255,0.2)',\n                      padding: '15px',\n                      borderRadius: '8px',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '24px',\n                        fontWeight: 'bold'\n                      },\n                      children: totalDays\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2033,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '14px',\n                        opacity: '0.9'\n                      },\n                      children: \"T\\u1ED5ng ng\\xE0y ngh\\u1EC9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2034,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2032,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2031,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-card\",\n                    style: {\n                      background: 'rgba(255,255,255,0.2)',\n                      padding: '15px',\n                      borderRadius: '8px',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '24px',\n                        fontWeight: 'bold',\n                        color: employeesAtLimit > 0 ? '#ffeb3b' : '#fff'\n                      },\n                      children: employeesAtLimit\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2039,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '14px',\n                        opacity: '0.9'\n                      },\n                      children: \"\\u0110\\u1EA1t gi\\u1EDBi h\\u1EA1n n\\u0103m\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2042,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2038,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2037,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2018,\n                columnNumber: 21\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1996,\n            columnNumber: 15\n          }, this), (() => {\n            const stats = calculateLeaveStatistics();\n            return stats.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"employee-stats-section\",\n              style: {\n                marginBottom: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                style: {\n                  marginBottom: '15px',\n                  color: '#333'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-users\",\n                  style: {\n                    marginRight: '8px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2056,\n                  columnNumber: 23\n                }, this), \"Chi ti\\u1EBFt theo nh\\xE2n vi\\xEAn\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2055,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-responsive\",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"table table-striped table-hover\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    className: \"table-dark\",\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Nh\\xE2n vi\\xEAn\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2063,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"S\\u1ED1 \\u0111\\u01A1n n\\u0103m\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2064,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"T\\u1ED5ng ng\\xE0y ngh\\u1EC9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2065,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0110\\xE3 duy\\u1EC7t\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2066,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Ch\\u1EDD duy\\u1EC7t\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2067,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"T\\u1EEB ch\\u1ED1i\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2068,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Tr\\u1EA1ng th\\xE1i\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2069,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2062,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2061,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: stats.map((empStat, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: empStat.employeeName\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2076,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2075,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `badge ${checkAnnualLimit(empStat) ? 'bg-danger' : empStat.totalRequests >= 8 ? 'bg-warning' : 'bg-success'}`,\n                          children: [empStat.totalRequests, \"/12\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2079,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2078,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: empStat.totalDays\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2084,\n                          columnNumber: 33\n                        }, this), \" ng\\xE0y\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2083,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-success\",\n                          children: empStat.approvedRequests\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2087,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2086,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-warning\",\n                          children: empStat.pendingRequests\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2090,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2089,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-danger\",\n                          children: empStat.rejectedRequests\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2093,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2092,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [checkAnnualLimit(empStat) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-danger me-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-exclamation-triangle\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2098,\n                            columnNumber: 37\n                          }, this), \" \\u0110\\u1EA1t gi\\u1EDBi h\\u1EA1n n\\u0103m\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2097,\n                          columnNumber: 35\n                        }, this), checkMonthlyLimit(empStat) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-warning\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-calendar-times\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2103,\n                            columnNumber: 37\n                          }, this), \" \\u0110\\u1EA1t gi\\u1EDBi h\\u1EA1n th\\xE1ng\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2102,\n                          columnNumber: 35\n                        }, this), !checkAnnualLimit(empStat) && !checkMonthlyLimit(empStat) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"badge bg-success\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-check\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2108,\n                            columnNumber: 37\n                          }, this), \" B\\xECnh th\\u01B0\\u1EDDng\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2107,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2095,\n                        columnNumber: 31\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2074,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2072,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2060,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2059,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2054,\n              columnNumber: 19\n            }, this);\n          })(), leaveRequests.allRequests.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-warning\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Th\\xF4ng b\\xE1o:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2124,\n              columnNumber: 19\n            }, this), \" Kh\\xF4ng t\\xECm th\\u1EA5y \\u0111\\u01A1n ngh\\u1EC9 ph\\xE9p n\\xE0o. Vui l\\xF2ng ki\\u1EC3m tra quy\\u1EC1n truy c\\u1EADp c\\u1EE7a b\\u1EA1n.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2123,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-filters\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"L\\u1ECDc d\\u1EEF li\\u1EC7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Nh\\xE2n vi\\xEAn:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2132,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"employeeName\",\n                  value: tableFilters.employeeName || '',\n                  onChange: handleTableFilterChange,\n                  placeholder: \"Nh\\u1EADp t\\xEAn nh\\xE2n vi\\xEAn...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2133,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2131,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Qu\\u1EA3n l\\xFD:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2142,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"supervisorName\",\n                  value: tableFilters.supervisorName || '',\n                  onChange: handleTableFilterChange,\n                  placeholder: \"Nh\\u1EADp t\\xEAn qu\\u1EA3n l\\xFD...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2143,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Tr\\u1EA1ng th\\xE1i:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2152,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"status\",\n                  value: tableFilters.status,\n                  onChange: handleTableFilterChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"T\\u1EA5t c\\u1EA3 tr\\u1EA1ng th\\xE1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2154,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"pending\",\n                    children: \"\\u0110ang ch\\u1EDD duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2155,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"approved\",\n                    children: \"\\u0110\\xE3 duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2156,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"rejected\",\n                    children: \"\\u0110\\xE3 t\\u1EEB ch\\u1ED1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2157,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2153,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2151,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2130,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-actions\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"clear-filters-btn\",\n                onClick: resetTableFilters,\n                children: \"X\\xF3a b\\u1ED9 l\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2162,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2161,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2128,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"T\\u1ED5ng s\\u1ED1 \\u0111\\u01A1n: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: filterTableRecords(tableFilters).length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2172,\n                columnNumber: 33\n              }, this), \" / \", leaveRequests.allRequests.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2172,\n              columnNumber: 17\n            }, this), tableFilters.status && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Tr\\u1EA1ng th\\xE1i \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: getStatusBadge(tableFilters.status).props.children\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2175,\n                columnNumber: 33\n              }, this), \": \", filterTableRecords(tableFilters).length, \" \\u0111\\u01A1n\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2175,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2171,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"employee-list-simple\",\n            id: \"allRequestsContainer\",\n            children: [displayedAllRequests.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-employees\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-employees-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-calendar-times\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2184,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2183,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Kh\\xF4ng t\\xECm th\\u1EA5y \\u0111\\u01A1n ngh\\u1EC9 ph\\xE9p n\\xE0o ph\\xF9 h\\u1EE3p v\\u1EDBi b\\u1ED9 l\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2186,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2182,\n              columnNumber: 19\n            }, this) : displayedAllRequests.map((request, index) => {\n              var _request$employee6, _request$employee7, _request$supervisor4, _request$employee8;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"employee-card-simple\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-info-main\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"employee-avatar\",\n                    children: (((_request$employee6 = request.employee) === null || _request$employee6 === void 0 ? void 0 : _request$employee6.fullName) || 'N/A').charAt(0).toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2192,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"employee-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: ((_request$employee7 = request.employee) === null || _request$employee7 === void 0 ? void 0 : _request$employee7.fullName) || 'Chưa xác định'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2196,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-calendar\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2197,\n                        columnNumber: 30\n                      }, this), \" \", formatDate(request.createdAt)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2197,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-clock\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2198,\n                        columnNumber: 30\n                      }, this), \" \", formatLeaveTime(request.startDate, request.startPeriod, request.endDate, request.endPeriod)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2198,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: formatDuration(calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2199,\n                        columnNumber: 30\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2199,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2195,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2191,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-contact\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-user-tie\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2204,\n                      columnNumber: 28\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Qu\\u1EA3n l\\xFD:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2204,\n                      columnNumber: 64\n                    }, this), \" \", ((_request$supervisor4 = request.supervisor) === null || _request$supervisor4 === void 0 ? void 0 : _request$supervisor4.fullName) || 'Chưa xác định']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2204,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-comment\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2205,\n                      columnNumber: 28\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"L\\xFD do:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2205,\n                      columnNumber: 63\n                    }, this), \" \", request.reason]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2205,\n                    columnNumber: 25\n                  }, this), request.comments && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-sticky-note\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2207,\n                      columnNumber: 30\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Nh\\u1EADn x\\xE9t:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2207,\n                      columnNumber: 69\n                    }, this), \" \", request.comments]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2207,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2203,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-work-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-info-circle\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2213,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tr\\u1EA1ng th\\xE1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2214,\n                      columnNumber: 27\n                    }, this), \" \", getStatusBadge(request.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2212,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-paperclip\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2217,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"File \\u0111\\xEDnh k\\xE8m:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2218,\n                      columnNumber: 27\n                    }, this), \" \", getAttachmentBadge(request)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2216,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2211,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"employee-actions\",\n                  children: [(user.role === 'SUPER_ADMIN' || ((_request$employee8 = request.employee) === null || _request$employee8 === void 0 ? void 0 : _request$employee8._id) === user._id && request.status === 'pending') && user.role !== 'LEVEL_II_MANAGER' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn-view\",\n                      onClick: () => handleShowEditModal(request),\n                      title: \"Ch\\u1EC9nh s\\u1EEDa \\u0111\\u01A1n\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-edit\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2235,\n                        columnNumber: 31\n                      }, this), \" Ch\\u1EC9nh s\\u1EEDa\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2230,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn-delete\",\n                      onClick: () => handleShowDeleteModal(request),\n                      title: \"X\\xF3a \\u0111\\u01A1n\",\n                      style: {\n                        background: '#dc3545',\n                        color: 'white'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-trash-alt\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2243,\n                        columnNumber: 31\n                      }, this), \" X\\xF3a\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2237,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true), user.role === 'LEVEL_II_MANAGER' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-muted fst-italic\",\n                    style: {\n                      padding: '6px 12px',\n                      background: '#f8f9fa',\n                      borderRadius: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-eye\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2250,\n                      columnNumber: 29\n                    }, this), \" Ch\\u1EC9 xem\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2249,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2222,\n                  columnNumber: 23\n                }, this)]\n              }, request._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2190,\n                columnNumber: 21\n              }, this);\n            }), isAllRequestsLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-more\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2261,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0110ang t\\u1EA3i th\\xEAm...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2262,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2260,\n              columnNumber: 19\n            }, this), displayedAllRequests.length > 0 && displayedAllRequests.length >= filterTableRecords(tableFilters).length && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"end-of-results\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u0110\\xE3 hi\\u1EC3n th\\u1ECB t\\u1EA5t c\\u1EA3 \", filterTableRecords(tableFilters).length, \" \\u0111\\u01A1n ngh\\u1EC9 ph\\xE9p\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2269,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2268,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2180,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1994,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1993,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1792,\n      columnNumber: 7\n    }, this), showSuccessAlert && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-alert\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-alert-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-check-circle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2282,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: successMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2283,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2281,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2280,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: handleCloseModal,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Duy\\u1EC7t \\u0111\\u01A1n xin ngh\\u1EC9 ph\\xE9p\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedRequest && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Nh\\xE2n vi\\xEAn:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2297,\n              columnNumber: 17\n            }, this), \" \", ((_selectedRequest$empl = selectedRequest.employee) === null || _selectedRequest$empl === void 0 ? void 0 : _selectedRequest$empl.fullName) || 'Không xác định']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Th\\u1EDDi gian ngh\\u1EC9:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2300,\n              columnNumber: 17\n            }, this), \" \", formatLeaveTime(selectedRequest.startDate, selectedRequest.startPeriod, selectedRequest.endDate, selectedRequest.endPeriod)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"T\\u1ED5ng ng\\xE0y ngh\\u1EC9:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2303,\n              columnNumber: 17\n            }, this), \" \", formatDuration(calculateLeaveDuration(selectedRequest.startDate, selectedRequest.startPeriod, selectedRequest.endDate, selectedRequest.endPeriod))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"L\\xFD do:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2306,\n              columnNumber: 17\n            }, this), \" \", selectedRequest.reason]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2305,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"File \\u0111\\xEDnh k\\xE8m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2311,\n              columnNumber: 17\n            }, this), loadingAttachments ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\",\n                size: \"sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2314,\n                columnNumber: 21\n              }, this), \" \\u0110ang t\\u1EA3i...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2313,\n              columnNumber: 19\n            }, this) : attachmentError ? /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: attachmentError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2317,\n              columnNumber: 19\n            }, this) : attachments.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Kh\\xF4ng c\\xF3 file \\u0111\\xEDnh k\\xE8m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2319,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(ListGroup, {\n              children: attachments.map(file => /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                className: \"d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"me-2\",\n                    children: getFileIcon(file.fileType)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2325,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: file.originalName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2326,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-muted ms-2\",\n                    children: [\"(\", (file.fileSize / 1024 / 1024).toFixed(2), \" MB)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2327,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2324,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-primary\",\n                    size: \"sm\",\n                    className: \"me-2\",\n                    onClick: () => handleDownloadFile(file._id),\n                    children: /*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2336,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2330,\n                    columnNumber: 27\n                  }, this), (user.role === 'SUPER_ADMIN' || user._id === file.uploadedBy) && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-danger\",\n                    size: \"sm\",\n                    onClick: () => handleDeleteFile(file._id),\n                    children: /*#__PURE__*/_jsxDEV(FaTrashAlt, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2344,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2339,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2329,\n                  columnNumber: 25\n                }, this)]\n              }, file._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2323,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2321,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2310,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            onSubmit: handleSubmitApproval,\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Quy\\u1EBFt \\u0111\\u1ECBnh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"status\",\n                value: approvalData.status,\n                onChange: handleApprovalChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"approved\",\n                  children: \"Duy\\u1EC7t \\u0111\\u01A1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2362,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"rejected\",\n                  children: \"T\\u1EEB ch\\u1ED1i \\u0111\\u01A1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2363,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Nh\\u1EADn x\\xE9t (kh\\xF4ng b\\u1EAFt bu\\u1ED9c)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2368,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 3,\n                name: \"comments\",\n                value: approvalData.comments,\n                onChange: handleApprovalChange,\n                placeholder: \"Nh\\u1EADp nh\\u1EADn x\\xE9t c\\u1EE7a b\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2369,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-end\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                className: \"me-2\",\n                onClick: handleCloseModal,\n                children: \"H\\u1EE7y\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                type: \"submit\",\n                children: \"X\\xE1c nh\\u1EADn\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2383,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2379,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2354,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showEditModal,\n      onHide: handleCloseEditModal,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Ch\\u1EC9nh s\\u1EEDa \\u0111\\u01A1n xin ngh\\u1EC9 ph\\xE9p\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2396,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedRequest && /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: handleSubmitEdit,\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Nh\\xE2n vi\\xEAn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: ((_selectedRequest$empl2 = selectedRequest.employee) === null || _selectedRequest$empl2 === void 0 ? void 0 : _selectedRequest$empl2.fullName) || 'Không xác định',\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2401,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"C\\u1EA5p tr\\xEAn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2411,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: ((_selectedRequest$supe = selectedRequest.supervisor) === null || _selectedRequest$supe === void 0 ? void 0 : _selectedRequest$supe.fullName) || 'Không xác định',\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2412,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2410,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2422,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  name: \"startDate\",\n                  value: editData.startDate,\n                  onChange: handleEditChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2423,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2421,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2420,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Th\\u1EDDi gian b\\u1EAFt \\u0111\\u1EA7u\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2434,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"startPeriod\",\n                  value: editData.startPeriod || 'full_day',\n                  onChange: e => {\n                    const newStartPeriod = e.target.value;\n\n                    // Cập nhật thời gian bắt đầu\n                    handleEditChange({\n                      target: {\n                        name: 'startPeriod',\n                        value: newStartPeriod\n                      }\n                    });\n\n                    // Kiểm tra nếu ngày bắt đầu và kết thúc là cùng một ngày\n                    const isSameDay = new Date(editData.startDate).setHours(0, 0, 0, 0) === new Date(editData.endDate).setHours(0, 0, 0, 0);\n\n                    // Nếu cùng một ngày, áp dụng các quy tắc thời gian kết thúc\n                    if (isSameDay) {\n                      if (newStartPeriod === 'full_day') {\n                        // Nếu chọn cả ngày, tự động đặt thời gian kết thúc cũng là cả ngày\n                        handleEditChange({\n                          target: {\n                            name: 'endPeriod',\n                            value: 'full_day'\n                          }\n                        });\n                      } else if (newStartPeriod === 'afternoon') {\n                        // Nếu chọn buổi chiều, thời gian kết thúc chỉ có thể là buổi chiều\n                        handleEditChange({\n                          target: {\n                            name: 'endPeriod',\n                            value: 'afternoon'\n                          }\n                        });\n                      } else if (newStartPeriod === 'morning' && editData.endPeriod === 'full_day') {\n                        // Nếu chọn buổi sáng và thời gian kết thúc đang là cả ngày, đổi thành buổi chiều\n                        handleEditChange({\n                          target: {\n                            name: 'endPeriod',\n                            value: 'afternoon'\n                          }\n                        });\n                      }\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"full_day\",\n                    children: \"C\\u1EA3 ng\\xE0y\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2471,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"morning\",\n                    children: \"Bu\\u1ED5i s\\xE1ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2472,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"afternoon\",\n                    children: \"Bu\\u1ED5i chi\\u1EC1u\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2473,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2435,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2433,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2432,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2419,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Ng\\xE0y k\\u1EBFt th\\xFAc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  name: \"endDate\",\n                  value: editData.endDate,\n                  onChange: handleEditChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2483,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2481,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Th\\u1EDDi gian k\\u1EBFt th\\xFAc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2494,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"endPeriod\",\n                  value: editData.endPeriod || 'full_day',\n                  onChange: handleEditChange,\n                  disabled:\n                  // Chỉ disable khi cùng một ngày và thời gian bắt đầu là cả ngày hoặc buổi chiều\n                  new Date(editData.startDate).setHours(0, 0, 0, 0) === new Date(editData.endDate).setHours(0, 0, 0, 0) && (editData.startPeriod === 'full_day' || editData.startPeriod === 'afternoon'),\n                  children: new Date(editData.startDate).setHours(0, 0, 0, 0) !== new Date(editData.endDate).setHours(0, 0, 0, 0) ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"full_day\",\n                      children: \"C\\u1EA3 ng\\xE0y\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2508,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"morning\",\n                      children: \"Bu\\u1ED5i s\\xE1ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2509,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"afternoon\",\n                      children: \"Bu\\u1ED5i chi\\u1EC1u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2510,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true) :\n                  /*#__PURE__*/\n                  /* Nếu cùng một ngày, hiển thị tùy chọn dựa trên thời gian bắt đầu */\n                  _jsxDEV(_Fragment, {\n                    children: [editData.startPeriod === 'full_day' && /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"full_day\",\n                      children: \"C\\u1EA3 ng\\xE0y\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2516,\n                      columnNumber: 29\n                    }, this), editData.startPeriod === 'morning' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"morning\",\n                        children: \"Bu\\u1ED5i s\\xE1ng\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2521,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"afternoon\",\n                        children: \"Bu\\u1ED5i chi\\u1EC1u\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2522,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true), editData.startPeriod === 'afternoon' && /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"afternoon\",\n                      children: \"Bu\\u1ED5i chi\\u1EC1u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2527,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2495,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: new Date(editData.startDate).setHours(0, 0, 0, 0) === new Date(editData.endDate).setHours(0, 0, 0, 0) ?\n                  // Nếu cùng một ngày\n                  editData.startPeriod === 'full_day' ? 'Khi chọn thời gian bắt đầu là cả ngày, thời gian kết thúc cũng sẽ là cả ngày' : editData.startPeriod === 'afternoon' ? 'Khi chọn thời gian bắt đầu là buổi chiều, thời gian kết thúc cũng sẽ là buổi chiều' : 'Sáng: 8:00 - 12:00, Chiều: 13:30 - 17:30' :\n                  // Nếu khác ngày\n                  'Sáng: 8:00 - 12:00, Chiều: 13:30 - 17:30, Cả ngày: 8:00 - 17:30'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2532,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2493,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2492,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2479,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"L\\xFD do\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2550,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              name: \"reason\",\n              value: editData.reason,\n              onChange: handleEditChange,\n              placeholder: \"Nh\\u1EADp l\\xFD do ngh\\u1EC9 ph\\xE9p\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2551,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2549,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"File \\u0111\\xEDnh k\\xE8m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2564,\n              columnNumber: 17\n            }, this), loadingAttachments ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\",\n                size: \"sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2567,\n                columnNumber: 21\n              }, this), \" \\u0110ang t\\u1EA3i...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2566,\n              columnNumber: 19\n            }, this) : attachmentError ? /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: attachmentError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2570,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [attachments.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"Kh\\xF4ng c\\xF3 file \\u0111\\xEDnh k\\xE8m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2575,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(ListGroup, {\n                className: \"mb-3\",\n                children: attachments.map(file => /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"me-2\",\n                      children: getFileIcon(file.fileType)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2581,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.originalName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2582,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted ms-2\",\n                      children: [\"(\", (file.fileSize / 1024 / 1024).toFixed(2), \" MB)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2583,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2580,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      className: \"me-2\",\n                      onClick: () => handleDownloadFile(file._id),\n                      children: /*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2592,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2586,\n                      columnNumber: 31\n                    }, this), (user.role === 'SUPER_ADMIN' || user._id === file.uploadedBy) && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-danger\",\n                      size: \"sm\",\n                      onClick: () => handleDeleteFile(file._id),\n                      children: /*#__PURE__*/_jsxDEV(FaTrashAlt, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2600,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2595,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2585,\n                    columnNumber: 29\n                  }, this)]\n                }, file._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2579,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2577,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-primary\",\n                    onClick: () => fileInputRef.current.click(),\n                    className: \"d-flex align-items-center\",\n                    disabled: uploadingFiles,\n                    children: [/*#__PURE__*/_jsxDEV(FaFileUpload, {\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2618,\n                      columnNumber: 27\n                    }, this), \" Ch\\u1ECDn file\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2612,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                    className: \"text-muted ms-3\",\n                    children: \"H\\u1ED7 tr\\u1EE3 PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF, TXT (t\\u1ED1i \\u0111a 5 file, m\\u1ED7i file kh\\xF4ng qu\\xE1 10MB)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2620,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    ref: fileInputRef,\n                    onChange: handleFileChange,\n                    style: {\n                      display: 'none'\n                    },\n                    multiple: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2623,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2611,\n                  columnNumber: 23\n                }, this), uploadError && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"danger\",\n                  className: \"mt-2 mb-2\",\n                  children: uploadError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2633,\n                  columnNumber: 25\n                }, this), selectedFiles.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(ListGroup, {\n                    className: \"mt-2\",\n                    children: selectedFiles.map((file, index) => /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"me-2\",\n                          children: getFileIcon(file.type)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2644,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2645,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-muted ms-2\",\n                          children: [\"(\", (file.size / 1024 / 1024).toFixed(2), \" MB)\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2646,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2643,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleRemoveFile(index),\n                        disabled: uploadingFiles,\n                        children: /*#__PURE__*/_jsxDEV(FaTrashAlt, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2654,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2648,\n                        columnNumber: 33\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2642,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2640,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-end mt-2\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"success\",\n                      size: \"sm\",\n                      onClick: () => uploadFiles(selectedRequest._id),\n                      disabled: uploadingFiles,\n                      children: uploadingFiles ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                          animation: \"border\",\n                          size: \"sm\",\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2669,\n                          columnNumber: 35\n                        }, this), \"\\u0110ang t\\u1EA3i l\\xEAn...\"]\n                      }, void 0, true) : 'Tải lên'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2661,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2660,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2610,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2563,\n            columnNumber: 15\n          }, this), user && user.role === 'SUPER_ADMIN' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2688,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"status\",\n                value: editData.status,\n                onChange: handleEditChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending\",\n                  children: \"Ch\\u1EDD duy\\u1EC7t\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2694,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"approved\",\n                  children: \"\\u0110\\xE3 duy\\u1EC7t\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2695,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"rejected\",\n                  children: \"\\u0110\\xE3 t\\u1EEB ch\\u1ED1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2696,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"cancelled\",\n                  children: \"\\u0110\\xE3 h\\u1EE7y\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2697,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2689,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2687,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2702,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Thi\\u1EBFt l\\u1EADp quy\\u1EC1n xem \\u0111\\u01A1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2703,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Ch\\u1EBF \\u0111\\u1ED9 hi\\u1EC3n th\\u1ECB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2706,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"visibility\",\n                value: editData.visibility || 'all',\n                onChange: handleEditChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"T\\u1EA5t c\\u1EA3 (m\\u1EB7c \\u0111\\u1ECBnh)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2712,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"restricted\",\n                  children: \"H\\u1EA1n ch\\u1EBF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2713,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2707,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                className: \"text-muted\",\n                children: \"Ch\\u1EBF \\u0111\\u1ED9 \\\"T\\u1EA5t c\\u1EA3\\\" cho ph\\xE9p m\\u1ECDi ng\\u01B0\\u1EDDi c\\xF3 quy\\u1EC1n xem \\u0111\\u01A1n ngh\\u1EC9 ph\\xE9p. Ch\\u1EBF \\u0111\\u1ED9 \\\"H\\u1EA1n ch\\u1EBF\\\" ch\\u1EC9 cho ph\\xE9p nh\\u1EEFng ng\\u01B0\\u1EDDi \\u0111\\u01B0\\u1EE3c ch\\u1EC9 \\u0111\\u1ECBnh xem \\u0111\\u01A1n.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2715,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2705,\n              columnNumber: 19\n            }, this), editData.visibility === 'restricted' && /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Ng\\u01B0\\u1EDDi d\\xF9ng \\u0111\\u01B0\\u1EE3c ph\\xE9p xem\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2722,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"select\",\n                multiple: true,\n                value: editData.visibleTo || [],\n                onChange: e => {\n                  const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);\n                  setEditData({\n                    ...editData,\n                    visibleTo: selectedOptions\n                  });\n                },\n                style: {\n                  height: '150px'\n                },\n                children: Array.isArray(allUsers) ? allUsers.filter(user => ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'SUPER_ADMIN'].includes(user.role)).map(user => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: user._id,\n                  children: user.fullName || user.name || user.email\n                }, user._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2739,\n                  columnNumber: 29\n                }, this)) : []\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2723,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                className: \"text-muted\",\n                children: \"Ch\\u1EC9 qu\\u1EA3n l\\xFD v\\xE0 admin \\u0111\\u01B0\\u1EE3c hi\\u1EC3n th\\u1ECB trong danh s\\xE1ch n\\xE0y. Nh\\xE2n vi\\xEAn th\\u01B0\\u1EDDng kh\\xF4ng \\u0111\\u01B0\\u1EE3c th\\xEAm v\\xE0o. Gi\\u1EEF Ctrl (ho\\u1EB7c Cmd tr\\xEAn Mac) \\u0111\\u1EC3 ch\\u1ECDn nhi\\u1EC1u ng\\u01B0\\u1EDDi d\\xF9ng.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2744,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2721,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"\\u1EA8n \\u0111\\u01A1n kh\\u1ECFi ng\\u01B0\\u1EDDi d\\xF9ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2752,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"select\",\n                multiple: true,\n                value: editData.hiddenFrom || [],\n                onChange: e => {\n                  const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);\n                  setEditData({\n                    ...editData,\n                    hiddenFrom: selectedOptions\n                  });\n                },\n                style: {\n                  height: '150px'\n                },\n                children: Array.isArray(allUsers) ? allUsers.filter(user => ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'SUPER_ADMIN'].includes(user.role)).map(user => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: user._id,\n                  children: user.fullName || user.name || user.email\n                }, user._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2769,\n                  columnNumber: 27\n                }, this)) : []\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2753,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                className: \"text-muted\",\n                children: \"Nh\\u1EEFng qu\\u1EA3n l\\xFD/admin \\u0111\\u01B0\\u1EE3c ch\\u1ECDn s\\u1EBD kh\\xF4ng th\\u1EC3 xem \\u0111\\u01A1n n\\xE0y, ngay c\\u1EA3 khi h\\u1ECD c\\xF3 quy\\u1EC1n qu\\u1EA3n l\\xFD \\u0111\\u01A1n ngh\\u1EC9 ph\\xE9p. Ch\\u1EC9 qu\\u1EA3n l\\xFD v\\xE0 admin \\u0111\\u01B0\\u1EE3c hi\\u1EC3n th\\u1ECB trong danh s\\xE1ch n\\xE0y. Nh\\xE2n vi\\xEAn th\\u01B0\\u1EDDng kh\\xF4ng \\u0111\\u01B0\\u1EE3c th\\xEAm v\\xE0o. Gi\\u1EEF Ctrl (ho\\u1EB7c Cmd tr\\xEAn Mac) \\u0111\\u1EC3 ch\\u1ECDn nhi\\u1EC1u ng\\u01B0\\u1EDDi d\\xF9ng.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2774,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2751,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Tr\\u1EA1ng th\\xE1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2783,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: getStatusBadge(editData.status).props.children,\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2784,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"Ch\\u1EC9 qu\\u1EA3n tr\\u1ECB vi\\xEAn m\\u1EDBi c\\xF3 th\\u1EC3 thay \\u0111\\u1ED5i tr\\u1EA1ng th\\xE1i \\u0111\\u01A1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2789,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2782,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              className: \"me-2\",\n              onClick: handleCloseEditModal,\n              children: \"H\\u1EE7y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2796,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              type: \"submit\",\n              children: \"L\\u01B0u thay \\u0111\\u1ED5i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2799,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2795,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2400,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2398,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2394,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showDeleteModal,\n      onHide: handleCloseDeleteModal,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"X\\xE1c nh\\u1EADn x\\xF3a\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2811,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2810,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: requestToDelete && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n x\\xF3a \\u0111\\u01A1n xin ngh\\u1EC9 ph\\xE9p n\\xE0y?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2816,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Nh\\xE2n vi\\xEAn:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2818,\n              columnNumber: 17\n            }, this), \" \", ((_requestToDelete$empl = requestToDelete.employee) === null || _requestToDelete$empl === void 0 ? void 0 : _requestToDelete$empl.fullName) || 'Không xác định']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2817,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Th\\u1EDDi gian ngh\\u1EC9:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2821,\n              columnNumber: 17\n            }, this), \" \", formatLeaveTime(requestToDelete.startDate, requestToDelete.startPeriod, requestToDelete.endDate, requestToDelete.endPeriod)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2820,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"T\\u1ED5ng ng\\xE0y ngh\\u1EC9:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2824,\n              columnNumber: 17\n            }, this), \" \", formatDuration(calculateLeaveDuration(requestToDelete.startDate, requestToDelete.startPeriod, requestToDelete.endDate, requestToDelete.endPeriod))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2823,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"L\\xFD do:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2827,\n              columnNumber: 17\n            }, this), \" \", requestToDelete.reason]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2826,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Tr\\u1EA1ng th\\xE1i:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2830,\n              columnNumber: 17\n            }, this), \" \", getStatusBadge(requestToDelete.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2829,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-warning\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-exclamation-triangle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2833,\n              columnNumber: 17\n            }, this), \"H\\xE0nh \\u0111\\u1ED9ng n\\xE0y kh\\xF4ng th\\u1EC3 ho\\xE0n t\\xE1c. \\u0110\\u01A1n xin ngh\\u1EC9 ph\\xE9p s\\u1EBD b\\u1ECB x\\xF3a v\\u0129nh vi\\u1EC5n.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2832,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2813,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCloseDeleteModal,\n          children: \"H\\u1EE7y\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2840,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"danger\",\n          onClick: handleDeleteRequest,\n          children: \"X\\xF3a\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2843,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2839,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2809,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showAttachmentModal,\n      onHide: handleCloseAttachmentModal,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Xem file \\u0111\\xEDnh k\\xE8m\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2852,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2851,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedRequest && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Th\\xF4ng tin \\u0111\\u01A1n ngh\\u1EC9 ph\\xE9p\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Table, {\n              bordered: true,\n              children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    width: \"30%\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Th\\u1EDDi gian ngh\\u1EC9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2862,\n                      columnNumber: 39\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2862,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatLeaveTime(selectedRequest.startDate, selectedRequest.startPeriod, selectedRequest.endDate, selectedRequest.endPeriod)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2863,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2861,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"L\\xFD do\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2866,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2866,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: selectedRequest.reason\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2867,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2865,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tr\\u1EA1ng th\\xE1i\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2870,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2870,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getStatusBadge(selectedRequest.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2871,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2869,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2860,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2859,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2857,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"File \\u0111\\xEDnh k\\xE8m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2878,\n              columnNumber: 17\n            }, this), loadingAttachments ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-3\",\n              children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\",\n                variant: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2881,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2\",\n                children: \"\\u0110ang t\\u1EA3i danh s\\xE1ch file...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2882,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2880,\n              columnNumber: 19\n            }, this) : attachmentError ? /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: attachmentError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2885,\n              columnNumber: 19\n            }, this) : attachments.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"info\",\n              children: \"Kh\\xF4ng c\\xF3 file \\u0111\\xEDnh k\\xE8m n\\xE0o\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2887,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(ListGroup, {\n              children: attachments.map(file => {\n                var _file$uploadedBy;\n                return /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"me-2\",\n                      children: getFileIcon(file.fileType)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2893,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: file.originalName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2894,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted ms-2\",\n                      children: [\"(\", (file.fileSize / 1024 / 1024).toFixed(2), \" MB)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2895,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted ms-2\",\n                      children: [\"- T\\u1EA3i l\\xEAn b\\u1EDFi \", ((_file$uploadedBy = file.uploadedBy) === null || _file$uploadedBy === void 0 ? void 0 : _file$uploadedBy.fullName) || 'Không xác định']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2896,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2892,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      className: \"me-2\",\n                      onClick: () => handleDownloadFile(file._id),\n                      children: /*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2905,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2899,\n                      columnNumber: 27\n                    }, this), (user.role === 'SUPER_ADMIN' || user._id === file.uploadedBy) && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-danger\",\n                      size: \"sm\",\n                      onClick: () => handleDeleteFile(file._id),\n                      children: /*#__PURE__*/_jsxDEV(FaTrashAlt, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2913,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2908,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2898,\n                    columnNumber: 25\n                  }, this)]\n                }, file._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2891,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2889,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2877,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2856,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2854,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCloseAttachmentModal,\n          children: \"\\u0110\\xF3ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2926,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2925,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2850,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1768,\n    columnNumber: 5\n  }, this);\n};\nexport default LeaveRequests;\n\n// Additional CSS styles for leave statistics - inject into document head\nif (typeof document !== 'undefined') {\n  const additionalStyles = `\n    .leave-statistics-banner .stat-card {\n      transition: transform 0.2s ease, box-shadow 0.2s ease;\n    }\n\n    .leave-statistics-banner .stat-card:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 6px 20px rgba(0,0,0,0.15);\n    }\n\n    .employee-stats-section .table th {\n      border-top: none;\n      font-weight: 600;\n      font-size: 14px;\n      background-color: #343a40;\n      color: white;\n    }\n\n    .employee-stats-section .table td {\n      vertical-align: middle;\n      font-size: 13px;\n      padding: 12px 8px;\n    }\n\n    .employee-stats-section .badge {\n      font-size: 11px;\n      padding: 4px 8px;\n      margin-right: 4px;\n    }\n\n    .leave-statistics-banner h3 {\n      text-shadow: 0 1px 3px rgba(0,0,0,0.3);\n      font-weight: 600;\n    }\n\n    .leave-statistics-banner .stat-card div:first-child {\n      font-family: 'Arial', sans-serif;\n      font-weight: 700;\n    }\n\n    @media (max-width: 768px) {\n      .leave-statistics-banner .row > div {\n        margin-bottom: 15px;\n      }\n\n      .employee-stats-section {\n        overflow-x: auto;\n      }\n\n      .leave-statistics-banner {\n        padding: 15px;\n      }\n\n      .leave-statistics-banner h3 {\n        font-size: 18px;\n      }\n    }\n  `;\n\n  // Check if styles already exist to avoid duplicates\n  if (!document.querySelector('#leave-stats-styles')) {\n    const styleSheet = document.createElement('style');\n    styleSheet.id = 'leave-stats-styles';\n    styleSheet.type = 'text/css';\n    styleSheet.innerText = additionalStyles;\n    document.head.appendChild(styleSheet);\n  }\n}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Container", "Row", "Col", "Card", "Table", "Badge", "<PERSON><PERSON>", "Tabs", "Tab", "Modal", "Form", "<PERSON><PERSON><PERSON>", "OverlayTrigger", "ListGroup", "<PERSON><PERSON>", "Spinner", "useAuth", "API", "useNavigate", "format", "vi", "FaFileUpload", "FaTrashAlt", "FaFile", "FaFilePdf", "FaFileWord", "FaFileExcel", "FaFileImage", "FaDownload", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "calculateLeaveDuration", "startDate", "startPeriod", "endDate", "endPeriod", "actualStartPeriod", "actualEndPeriod", "start", "Date", "end", "setHours", "is<PERSON><PERSON><PERSON>", "date", "getDay", "isSaturday", "countWorkDays", "count", "currentDate", "setDate", "getDate", "getTime", "diffTime", "Math", "abs", "diffDays", "floor", "startIsSunday", "endIsSunday", "startIsSaturday", "endIsSaturday", "thursdayToFriday", "workDaysBeforeSaturday", "adjustedDays", "workDays", "formatDuration", "duration", "formatLeaveTime", "formatDate", "locale", "formatPeriod", "period", "days", "TablePagination", "totalRecords", "recordsPerPage", "currentPage", "onPageChange", "pageNumbers", "totalPages", "ceil", "startPage", "max", "endPage", "min", "i", "push", "className", "children", "onClick", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "number", "<PERSON><PERSON><PERSON><PERSON>", "LeaveRequests", "_selectedRequest$empl", "_selectedRequest$empl2", "_selectedRequest$supe", "_requestToDelete$empl", "user", "token", "localStorage", "getItem", "navigate", "leaveRequests", "setLeaveRequests", "asEmployee", "asSupervisor", "allRequests", "groupedRequests", "setGroupedRequests", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "displayedMyRequests", "setDisplayedMyRequests", "myRequestsToShow", "setMyRequestsToShow", "isMyRequestsLoading", "setIsMyRequestsLoading", "displayedApprovalRequests", "setDisplayedApprovalRequests", "approvalRequestsToShow", "setApprovalRequestsToShow", "isApprovalRequestsLoading", "setIsApprovalRequestsLoading", "displayedAllRequests", "setDisplayedAllRequests", "allRequestsToShow", "setAllRequestsToShow", "isAllRequestsLoading", "setIsAllRequestsLoading", "showAttachmentModal", "setShowAttachmentModal", "selectedRequest", "setSelectedRequest", "approvalData", "setApprovalData", "status", "comments", "tableFilters", "setTableFilters", "employeeName", "<PERSON><PERSON><PERSON>", "tableCurrentPage", "setTableCurrentPage", "tableRecordsPerPage", "setTableRecordsPerPage", "showEditModal", "setShowEditModal", "editData", "setEditData", "reason", "supervisor", "visibility", "visibleTo", "hiddenFrom", "allUsers", "setAllUsers", "showDeleteModal", "setShowDeleteModal", "requestToDelete", "setRequestToDelete", "successMessage", "setSuccessMessage", "showSuccessAlert", "setShowSuccessAlert", "attachments", "setAttachments", "loadingAttachments", "setLoadingAttachments", "attachmentError", "setAttachmentError", "selectedFiles", "setSelectedFiles", "uploadError", "setUploadError", "uploadingFiles", "setUploadingFiles", "fileInputRef", "fetchLeaveRequests", "res", "get", "console", "log", "data", "role", "_res$data$allRequests", "length", "grouped", "groupLeaveRequests", "err", "fetchAllUsers", "users", "Array", "isArray", "message", "handleMyRequestsScroll", "container", "document", "getElementById", "scrollTop", "scrollHeight", "clientHeight", "loadMoreMyRequests", "addEventListener", "removeEventListener", "handleApprovalRequestsScroll", "loadMoreApprovalRequests", "handleAllRequestsScroll", "loadMoreAllRequests", "slice", "filteredRequests", "filterTableRecords", "setTimeout", "nextRequestsToShow", "requests", "requestGroups", "for<PERSON>ach", "request", "key", "id", "_id", "createdAt", "supervisors", "hasAttachments", "attachmentCount", "some", "s", "includes", "_request$supervisor", "fullName", "Object", "values", "handleCreateRequest", "fetchAttachments", "leaveRequestId", "response", "headers", "handleFileChange", "e", "files", "from", "target", "maxSize", "oversizedFiles", "filter", "file", "size", "f", "name", "join", "allowedTypes", "invalidFiles", "type", "prev", "value", "handleRemoveFile", "index", "_", "getFileIcon", "fileType", "String", "toLowerCase", "uploadFiles", "formData", "FormData", "append", "post", "_err$response", "_err$response$data", "handleDownloadFile", "fileId", "alert", "responseType", "fileInfo", "find", "url", "window", "URL", "createObjectURL", "Blob", "link", "createElement", "href", "setAttribute", "originalName", "body", "append<PERSON><PERSON><PERSON>", "click", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleDeleteFile", "confirm", "delete", "handleShowApprovalModal", "handleCloseModal", "handleShowAttachmentModal", "handleCloseAttachmentModal", "handleApprovalChange", "handleSubmitApproval", "preventDefault", "put", "updatedAsSupervisor", "req", "updatedAllRequests", "updatedAsEmployee", "isUserRequest", "updatedGrouped", "_err$response2", "_err$response2$data", "msg", "handleShowEditModal", "_request$supervisor2", "formatDateForInput", "dateString", "getFullYear", "getMonth", "padStart", "isAdmin", "isOwner", "employee", "employeeId", "created<PERSON>y", "isPending", "handleCloseEditModal", "handleEditChange", "newState", "isSameDay", "handleSubmitEdit", "dataToSubmit", "updatedRequest", "_err$response3", "_err$response3$data", "handleShowDeleteModal", "handleCloseDeleteModal", "handleDeleteRequest", "_err$response4", "_err$response5", "_err$response5$data", "_err$response5$data$e", "_err$response6", "_err$response6$data", "getStatusBadge", "bg", "getAttachmentBadge", "hasValidId", "warn", "title", "placement", "overlay", "variant", "validRequest", "getMyRequestAttachmentBadge", "year", "month", "day", "adjustedDate", "toISOString", "startText", "endText", "renderSupervisors", "sup", "handleTableFilterChange", "resetTableFilters", "calculateLeaveStatistics", "currentYear", "employeeStats", "processedRequests", "Set", "_request$employee", "_request$employee2", "requestYear", "requestMonth", "<PERSON><PERSON><PERSON>", "has", "add", "totalRequests", "totalDays", "monthlyRequests", "approvedRequests", "pendingRequests", "rejectedRequests", "stats", "leaveDays", "<PERSON><PERSON><PERSON>", "sort", "a", "b", "localeCompare", "checkMonthlyLimit", "currentMonth", "checkAnnualLimit", "filters", "_request$employee3", "_request$supervisor3", "employeeFullName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchesEmployee", "matchesSupervisor", "matchesStatus", "getTablePaginatedRecords", "records", "filteredRecords", "startIndex", "endIndex", "Body", "location", "reload", "style", "zIndex", "onClose", "dismissible", "xs", "defaultActiveKey", "eventKey", "margin", "color", "display", "alignItems", "marginRight", "enhancedRequest", "background", "_request$employee4", "_request$employee5", "char<PERSON>t", "toUpperCase", "padding", "borderRadius", "marginBottom", "boxShadow", "totalEmployees", "reduce", "sum", "emp", "employeesAtLimit", "employeesAtMonthlyLimit", "textAlign", "fontSize", "fontWeight", "opacity", "empStat", "onChange", "placeholder", "props", "_request$employee6", "_request$employee7", "_request$supervisor4", "_request$employee8", "show", "onHide", "Header", "closeButton", "Title", "animation", "<PERSON><PERSON>", "fileSize", "toFixed", "uploadedBy", "onSubmit", "Group", "Label", "Select", "Control", "as", "rows", "md", "required", "newStartPeriod", "Text", "current", "ref", "multiple", "selectedOptions", "option", "height", "email", "Footer", "bordered", "width", "_file$uploadedBy", "additionalStyles", "querySelector", "styleSheet", "innerText", "head"], "sources": ["E:/NEW ATLANTIC/QUẢN LÝ XE LOCAL HOST/frontend/src/pages/LeaveRequest.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { Container, Row, Col, Card, Table, Badge, Button, Tabs, Tab, Modal, Form, Tooltip, OverlayTrigger, ListGroup, Alert, Spinner } from 'react-bootstrap';\r\nimport { useAuth } from '../context/AuthContext'; \r\nimport API from '../services/api';\r\nimport '../css/LeaveRequest.css';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport '../css/LeaveRequest.css';\r\nimport '../css/FileAttachments.css';\r\nimport { FaFileUpload, FaTrashAlt, FaFile, FaFilePdf, FaFileWord, FaFileExcel, FaFileImage, FaDownload } from 'react-icons/fa';\r\n\r\n// Hàm tính tổng thời gian nghỉ\r\nconst calculateLeaveDuration = (startDate, startPeriod, endDate, endPeriod) => {\r\n  // Kiểm tra nếu không có dữ liệu thời gian\r\n  if (!startDate || !endDate) {\r\n    return 0;\r\n  }\r\n  \r\n  // Đặt giá trị mặc định cho startPeriod và endPeriod nếu không có\r\n  const actualStartPeriod = startPeriod || 'full_day';\r\n  const actualEndPeriod = endPeriod || 'full_day';\r\n  \r\n  // Chuyển đổi ngày thành đối tượng Date để so sánh\r\n  const start = new Date(startDate);\r\n  const end = new Date(endDate);\r\n  \r\n  // Đặt giờ về 0 để so sánh chỉ ngày\r\n  start.setHours(0, 0, 0, 0);\r\n  end.setHours(0, 0, 0, 0);\r\n  \r\n  // Hàm kiểm tra ngày có phải là Chủ nhật không\r\n  const isSunday = (date) => {\r\n    return date.getDay() === 0; // 0 là Chủ nhật trong JavaScript\r\n  };\r\n  \r\n  // Hàm kiểm tra ngày có phải là thứ 7 không\r\n  const isSaturday = (date) => {\r\n    return date.getDay() === 6; // 6 là thứ 7 trong JavaScript\r\n  };\r\n  \r\n  // Hàm đếm số ngày làm việc (không tính Chủ nhật) giữa hai ngày\r\n  const countWorkDays = (startDate, endDate) => {\r\n    let count = 0;\r\n    const currentDate = new Date(startDate);\r\n    \r\n    // Lặp qua từng ngày giữa startDate và endDate\r\n    while (currentDate <= endDate) {\r\n      // Nếu không phải Chủ nhật thì tăng biến đếm\r\n      if (!isSunday(currentDate)) {\r\n        // Nếu là thứ 7, chỉ tính 0.5 ngày\r\n        if (isSaturday(currentDate)) {\r\n          count += 0.5;\r\n        } else {\r\n          count += 1.0;\r\n        }\r\n      }\r\n      // Tăng ngày lên 1\r\n      currentDate.setDate(currentDate.getDate() + 1);\r\n    }\r\n    \r\n    return count;\r\n  };\r\n  \r\n  // Nếu cùng một ngày\r\n  if (start.getTime() === end.getTime()) {\r\n    // Kiểm tra nếu là Chủ nhật thì không tính\r\n    if (isSunday(start)) {\r\n      return 0;\r\n    }\r\n    \r\n    // Kiểm tra nếu là thứ 7\r\n    if (isSaturday(start)) {\r\n      // Thứ 7 chỉ tính buổi sáng\r\n      return 0.5;\r\n    }\r\n    \r\n    if (actualStartPeriod === 'full_day' && actualEndPeriod === 'full_day') {\r\n      return 1;\r\n    } else if (actualStartPeriod === actualEndPeriod && actualStartPeriod !== 'full_day') {\r\n      return 0.5;\r\n    } else if (actualStartPeriod === 'morning' && actualEndPeriod === 'afternoon') {\r\n      return 1;\r\n    } else {\r\n      return 0.5;\r\n    }\r\n  } else {\r\n    // Tính số ngày giữa hai ngày\r\n    const diffTime = Math.abs(end - start);\r\n    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\r\n    \r\n    // Kiểm tra các ngày đặc biệt\r\n    const startIsSunday = isSunday(start);\r\n    const endIsSunday = isSunday(end);\r\n    const startIsSaturday = isSaturday(start);\r\n    const endIsSaturday = isSaturday(end);\r\n    \r\n    // Trường hợp 2 ngày liền kề\r\n    if (diffDays === 1) {\r\n      // Trường hợp đặc biệt: từ thứ 6 đến Chủ nhật\r\n      if (!startIsSaturday && !startIsSunday && endIsSunday) {\r\n        // Nếu thứ 6 là cả ngày, tính 1.5 ngày (1 ngày thứ 6 + 0.5 ngày thứ 7 buổi sáng)\r\n        if (actualStartPeriod === 'full_day') {\r\n          return 1.5;\r\n        } \r\n        // Nếu thứ 6 chỉ buổi chiều, tính 1 ngày (0.5 ngày thứ 6 + 0.5 ngày thứ 7 buổi sáng)\r\n        else if (actualStartPeriod === 'afternoon') {\r\n          return 1;\r\n        }\r\n        // Nếu thứ 6 chỉ buổi sáng, tính 1 ngày (0.5 ngày thứ 6 + 0.5 ngày thứ 7 buổi sáng)\r\n        else {\r\n          return 1;\r\n        }\r\n      }\r\n      \r\n      // Trường hợp từ thứ 6 đến thứ 7\r\n      if (!startIsSaturday && !startIsSunday && endIsSaturday) {\r\n        // Thứ 7 chỉ tính buổi sáng\r\n        if (actualStartPeriod === 'full_day') {\r\n          return 1.5;\r\n        } else if (actualStartPeriod === 'afternoon') {\r\n          return 1;\r\n        } else {\r\n          return 1;\r\n        }\r\n      }\r\n      \r\n      // Nếu cả hai ngày đều là Chủ nhật\r\n      if (startIsSunday && endIsSunday) {\r\n        return 0;\r\n      }\r\n      \r\n      // Nếu ngày bắt đầu là Chủ nhật\r\n      if (startIsSunday) {\r\n        // Chỉ tính ngày kết thúc\r\n        if (endIsSaturday) {\r\n          // Nếu ngày kết thúc là thứ 7, chỉ tính 0.5 ngày\r\n          return 0.5;\r\n        } else if (actualEndPeriod === 'full_day') {\r\n          return 1;\r\n        } else {\r\n          return 0.5;\r\n        }\r\n      }\r\n      \r\n      // Nếu ngày kết thúc là Chủ nhật\r\n      if (endIsSunday) {\r\n        // Nếu ngày bắt đầu là thứ 7, chỉ tính 0.5 ngày (thứ 7 buổi sáng)\r\n        if (startIsSaturday) {\r\n          return 0.5;\r\n        }\r\n        \r\n        // Nếu ngày bắt đầu không phải thứ 7\r\n        if (actualStartPeriod === 'full_day') {\r\n          return 1;\r\n        } else {\r\n          return 0.5;\r\n        }\r\n      }\r\n      \r\n      // Nếu ngày bắt đầu là thứ 7\r\n      if (startIsSaturday) {\r\n        // Thứ 7 chỉ tính buổi sáng\r\n        return 0.5;\r\n      }\r\n      \r\n      // Nếu ngày kết thúc là thứ 7\r\n      if (endIsSaturday) {\r\n        // Thứ 7 chỉ tính buổi sáng\r\n        if (actualStartPeriod === 'full_day') {\r\n          return 1.5;\r\n        } else if (actualStartPeriod === 'afternoon') {\r\n          return 1;\r\n        } else if (actualStartPeriod === 'morning') {\r\n          return 1;\r\n        }\r\n      }\r\n      \r\n      // Các trường hợp thông thường\r\n      if (actualStartPeriod === 'afternoon' && actualEndPeriod === 'morning') {\r\n        return 1;\r\n      } else if (actualStartPeriod === 'afternoon' && (actualEndPeriod === 'full_day' || actualEndPeriod === 'afternoon')) {\r\n        return 1.5;\r\n      } else if ((actualStartPeriod === 'full_day' || actualStartPeriod === 'morning') && actualEndPeriod === 'morning') {\r\n        return 1.5;\r\n      } else {\r\n        return 2;\r\n      }\r\n    } else if (diffDays > 1) {\r\n      // Trường hợp đặc biệt: từ thứ 5 đến thứ 7\r\n      if (!startIsSaturday && !startIsSunday && endIsSaturday) {\r\n        // Tính số ngày từ thứ 5 đến thứ 6 (không tính thứ 7)\r\n        const thursdayToFriday = new Date(end);\r\n        thursdayToFriday.setDate(thursdayToFriday.getDate() - 1); // Lùi 1 ngày từ thứ 7 để lấy thứ 6\r\n        \r\n        // Đếm số ngày làm việc từ ngày bắt đầu đến thứ 6\r\n        const workDaysBeforeSaturday = diffDays; // Số ngày từ thứ 5 đến thứ 6\r\n        \r\n        // Điều chỉnh ngày đầu tiên nếu không phải cả ngày\r\n        let adjustedDays = workDaysBeforeSaturday;\r\n        if (!startIsSunday && !startIsSaturday && actualStartPeriod === 'afternoon') {\r\n          adjustedDays -= 0.5;\r\n        }\r\n        \r\n        // Cộng thêm 0.5 ngày cho thứ 7 buổi sáng\r\n        return adjustedDays + 0.5;\r\n      }\r\n      \r\n      // Trường hợp đặc biệt: từ thứ 5 đến chủ nhật\r\n      if (!startIsSaturday && !startIsSunday && endIsSunday) {\r\n        // Tính số ngày từ thứ 5 đến thứ 6 (không tính thứ 7 và chủ nhật)\r\n        const thursdayToFriday = new Date(end);\r\n        thursdayToFriday.setDate(thursdayToFriday.getDate() - 2); // Lùi 2 ngày từ chủ nhật để lấy thứ 6\r\n        \r\n        // Đếm số ngày làm việc từ ngày bắt đầu đến thứ 6\r\n        const workDaysBeforeSaturday = diffDays - 1; // Số ngày từ thứ 5 đến thứ 6 (trừ chủ nhật)\r\n        \r\n        // Điều chỉnh ngày đầu tiên nếu không phải cả ngày\r\n        let adjustedDays = workDaysBeforeSaturday;\r\n        if (!startIsSunday && !startIsSaturday && actualStartPeriod === 'afternoon') {\r\n          adjustedDays -= 0.5;\r\n        }\r\n        \r\n        // Cộng thêm 0.5 ngày cho thứ 7 buổi sáng\r\n        return adjustedDays + 0.5;\r\n      }\r\n      \r\n      // Sử dụng hàm đếm ngày làm việc để tính toán chính xác\r\n      let workDays = 0;\r\n      \r\n      // Đếm số ngày làm việc (không tính chủ nhật)\r\n      const currentDate = new Date(start);\r\n      while (currentDate <= end) {\r\n        // Nếu không phải Chủ nhật thì tăng biến đếm\r\n        if (!isSunday(currentDate)) {\r\n          // Nếu là thứ 7, chỉ tính 0.5 ngày\r\n          if (isSaturday(currentDate)) {\r\n            workDays += 0.5;\r\n          } else {\r\n            workDays += 1;\r\n          }\r\n        }\r\n        // Tăng ngày lên 1\r\n        currentDate.setDate(currentDate.getDate() + 1);\r\n      }\r\n      \r\n      // Điều chỉnh giá trị dựa trên thời gian bắt đầu và kết thúc\r\n      let adjustedDays = workDays;\r\n      \r\n      // Điều chỉnh ngày đầu tiên nếu không phải cả ngày\r\n      if (!startIsSunday && !startIsSaturday && actualStartPeriod === 'afternoon') {\r\n        adjustedDays -= 0.5;\r\n      }\r\n      \r\n      // Điều chỉnh ngày cuối cùng nếu không phải cả ngày\r\n      if (!endIsSunday && !endIsSaturday && actualEndPeriod === 'morning') {\r\n        adjustedDays -= 0.5;\r\n      } else if (endIsSaturday && actualEndPeriod === 'morning') {\r\n        // Thứ 7 đã được tính là 0.5 trong workDays, không cần điều chỉnh thêm\r\n      }\r\n      \r\n      return adjustedDays;\r\n    }\r\n  }\r\n  \r\n  return 0;\r\n};\r\n\r\n// Hàm định dạng hiển thị tổng thời gian nghỉ\r\nconst formatDuration = (duration) => {\r\n  if (duration === 0) return '0 ngày';\r\n  if (duration % 1 === 0) return `${duration} ngày`;\r\n  return `${Math.floor(duration)}.5 ngày`;\r\n};\r\n\r\n// Hàm tính toán và hiển thị thời gian nghỉ\r\nconst formatLeaveTime = (startDate, startPeriod, endDate, endPeriod) => {\r\n  // Kiểm tra nếu không có dữ liệu thời gian\r\n  if (!startDate || !endDate) {\r\n    return 'Không có dữ liệu';\r\n  }\r\n  \r\n  // Đặt giá trị mặc định cho startPeriod và endPeriod nếu không có\r\n  const actualStartPeriod = startPeriod || 'full_day';\r\n  const actualEndPeriod = endPeriod || 'full_day';\r\n  \r\n  // Chuyển đổi ngày thành đối tượng Date để so sánh\r\n  const start = new Date(startDate);\r\n  const end = new Date(endDate);\r\n  \r\n  // Đặt giờ về 0 để so sánh chỉ ngày\r\n  start.setHours(0, 0, 0, 0);\r\n  end.setHours(0, 0, 0, 0);\r\n  \r\n  // Định dạng ngày tháng\r\n  const formatDate = (date) => {\r\n    return format(new Date(date), 'dd/MM/yyyy', { locale: vi });\r\n  };\r\n  \r\n  // Định dạng thời gian\r\n  const formatPeriod = (period) => {\r\n    switch(period) {\r\n      case 'morning': return 'Sáng';\r\n      case 'afternoon': return 'Chiều';\r\n      case 'full_day': return 'Cả ngày';\r\n      default: return 'Cả ngày';\r\n    }\r\n  };\r\n  \r\n  // Tính toán số ngày nghỉ thực tế\r\n  const days = calculateLeaveDuration(startDate, actualStartPeriod, endDate, actualEndPeriod);\r\n  \r\n  // Định dạng hiển thị số ngày\r\n  let duration = '';\r\n  if (days === 0) {\r\n    duration = '(0 ngày)';\r\n  } else if (days % 1 === 0) {\r\n    duration = `(${days} ngày)`;\r\n  } else {\r\n    duration = `(${Math.floor(days)}.5 ngày)`;\r\n  }\r\n  \r\n  return `${formatDate(startDate)} ${formatPeriod(actualStartPeriod)} - ${formatDate(endDate)} ${formatPeriod(actualEndPeriod)} ${duration}`;\r\n};\r\n\r\n// Thêm component TablePagination\r\nconst TablePagination = ({ totalRecords, recordsPerPage, currentPage, onPageChange }) => {\r\n  const pageNumbers = [];\r\n  const totalPages = Math.ceil(totalRecords / recordsPerPage);\r\n  let startPage = Math.max(1, currentPage - 2);\r\n  let endPage = Math.min(totalPages, startPage + 4);\r\n\r\n  if (endPage - startPage < 4) {\r\n    startPage = Math.max(1, endPage - 4);\r\n  }\r\n\r\n  for (let i = startPage; i <= endPage; i++) {\r\n    pageNumbers.push(i);\r\n  }\r\n\r\n  if (totalPages <= 1) return null;\r\n\r\n  return (\r\n    <div className=\"table-pagination\">\r\n      <button\r\n        onClick={() => onPageChange(1)}\r\n        disabled={currentPage === 1}\r\n        className=\"pagination-button\"\r\n      >\r\n        «\r\n      </button>\r\n      <button\r\n        onClick={() => onPageChange(currentPage - 1)}\r\n        disabled={currentPage === 1}\r\n        className=\"pagination-button\"\r\n      >\r\n        {'<'}\r\n      </button>\r\n      {pageNumbers.map((number) => (\r\n        <button\r\n          key={number}\r\n          onClick={() => onPageChange(number)}\r\n          className={`pagination-button ${currentPage === number ? 'active' : ''}`}\r\n        >\r\n          {number}\r\n        </button>\r\n      ))}\r\n      <button\r\n        onClick={() => onPageChange(currentPage + 1)}\r\n        disabled={currentPage === totalPages}\r\n        className=\"pagination-button\"\r\n      >\r\n        {'>'}\r\n      </button>\r\n      <button\r\n        onClick={() => onPageChange(totalPages)}\r\n        disabled={currentPage === totalPages}\r\n        className=\"pagination-button\"\r\n      >\r\n        »\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Danh sách các vai trò quản lý có thể duyệt đơn\r\nconst managerRoles = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'SUPER_ADMIN'];\r\n\r\nconst LeaveRequests = () => {\r\n  const { user } = useAuth();\r\n  const token = localStorage.getItem('authToken')\r\n  const navigate = useNavigate();\r\n  const [leaveRequests, setLeaveRequests] = useState({\r\n    asEmployee: [],\r\n    asSupervisor: [],\r\n    allRequests: []\r\n  });\r\n  const [groupedRequests, setGroupedRequests] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  \r\n  // States for infinite scroll\r\n  const [displayedMyRequests, setDisplayedMyRequests] = useState([]);\r\n  const [myRequestsToShow, setMyRequestsToShow] = useState(20);\r\n  const [isMyRequestsLoading, setIsMyRequestsLoading] = useState(false);\r\n  \r\n  const [displayedApprovalRequests, setDisplayedApprovalRequests] = useState([]);\r\n  const [approvalRequestsToShow, setApprovalRequestsToShow] = useState(20);\r\n  const [isApprovalRequestsLoading, setIsApprovalRequestsLoading] = useState(false);\r\n  \r\n  const [displayedAllRequests, setDisplayedAllRequests] = useState([]);\r\n  const [allRequestsToShow, setAllRequestsToShow] = useState(20);\r\n  const [isAllRequestsLoading, setIsAllRequestsLoading] = useState(false);\r\n  const [showAttachmentModal, setShowAttachmentModal] = useState(false); // Modal riêng cho xem file đính kèm\r\n  const [selectedRequest, setSelectedRequest] = useState(null);\r\n  const [approvalData, setApprovalData] = useState({\r\n    status: 'approved',\r\n    comments: ''\r\n  });\r\n  const [tableFilters, setTableFilters] = useState({\r\n    employeeName: '',\r\n    supervisorName: '',\r\n    status: ''\r\n  });\r\n  const [tableCurrentPage, setTableCurrentPage] = useState(1);\r\n  const [tableRecordsPerPage, setTableRecordsPerPage] = useState(10);\r\n  \r\n  // Thêm state cho modal chỉnh sửa\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [editData, setEditData] = useState({\r\n    startDate: '',\r\n    endDate: '',\r\n    reason: '',\r\n    status: '',\r\n    supervisor: '', // Thêm trường supervisor\r\n    visibility: 'all',\r\n    visibleTo: [],\r\n    hiddenFrom: []\r\n  });\r\n  \r\n  // State cho danh sách người dùng (để chọn trong visibility settings)\r\n  const [allUsers, setAllUsers] = useState([]);\r\n  \r\n  // Thêm state cho modal xác nhận xóa\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [requestToDelete, setRequestToDelete] = useState(null);\r\n  \r\n  // Thêm state cho thông báo thành công\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [showSuccessAlert, setShowSuccessAlert] = useState(false);\r\n  \r\n  // State cho file đính kèm\r\n  const [attachments, setAttachments] = useState([]);\r\n  const [loadingAttachments, setLoadingAttachments] = useState(false);\r\n  const [attachmentError, setAttachmentError] = useState(null);\r\n  const [selectedFiles, setSelectedFiles] = useState([]);\r\n  const [uploadError, setUploadError] = useState(null);\r\n  const [uploadingFiles, setUploadingFiles] = useState(false);\r\n  const fileInputRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    if (!token) {\r\n      setLoading(false);\r\n      return;\r\n    }\r\n  \r\n    setLoading(true);\r\n  \r\n    const fetchLeaveRequests = async () => {\r\n      try {\r\n        // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js\r\n        const res = await API.get('/leave-requests');\r\n        console.log('API trả về:', res.data);\r\n        \r\n        // Kiểm tra dữ liệu trả về từ API\r\n        if (user && (user.role === 'SUPER_ADMIN' || user.role === 'LEVEL_II_MANAGER')) {\r\n          console.log(`Người dùng là ${user.role}`);\r\n          console.log('Số lượng đơn trong allRequests:', res.data.allRequests?.length || 0);\r\n        }\r\n        \r\n        // Cập nhật state với dữ liệu từ API\r\n        setLeaveRequests({\r\n          asEmployee: res.data.asEmployee || [],\r\n          asSupervisor: res.data.asSupervisor || [],\r\n          allRequests: res.data.allRequests || []\r\n        });\r\n        \r\n        // Nhóm các đơn có cùng thời gian và lý do\r\n        const grouped = groupLeaveRequests(res.data.asEmployee || []);\r\n        setGroupedRequests(grouped);\r\n      } catch (err) {\r\n        console.error('Lỗi lấy đơn nghỉ:', err);\r\n        setError('Không thể lấy danh sách đơn. Vui lòng thử lại.');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n  \r\n    fetchLeaveRequests();\r\n  }, [token]);\r\n  \r\n  // Lấy danh sách tất cả người dùng (cho SUPER_ADMIN và LEVEL_II_MANAGER)\r\n  useEffect(() => {\r\n    if (user && (user.role === 'SUPER_ADMIN' || user.role === 'LEVEL_II_MANAGER')) {\r\n      const fetchAllUsers = async () => {\r\n        try {\r\n          console.log('Đang lấy danh sách người dùng...');\r\n          // Gọi API đúng đường dẫn, không thêm '/api' vì đã có trong baseURL\r\n          const res = await API.get('/users');\r\n          console.log('Kết quả API:', res);\r\n          \r\n          // Kiểm tra cấu trúc dữ liệu trả về\r\n          if (res.data && res.data.users && Array.isArray(res.data.users)) {\r\n            setAllUsers(res.data.users);\r\n            console.log(`Đã tải danh sách ${res.data.users.length} người dùng cho thiết lập quyền xem đơn`);\r\n          } else {\r\n            console.error('Dữ liệu người dùng không đúng định dạng:', res.data);\r\n            setAllUsers([]); // Đặt giá trị mặc định là mảng rỗng\r\n          }\r\n        } catch (err) {\r\n          console.error('Lỗi lấy danh sách người dùng:', err.message);\r\n          console.error('Chi tiết lỗi:', err);\r\n          setAllUsers([]); // Đặt giá trị mặc định là mảng rỗng khi có lỗi\r\n        }\r\n      };\r\n      \r\n      fetchAllUsers();\r\n    } else {\r\n      // Đảm bảo allUsers luôn là mảng rỗng khi không phải SUPER_ADMIN\r\n      setAllUsers([]);\r\n    }\r\n  }, [user]);\r\n  \r\n  // Infinite scroll setup for My Requests\r\n  useEffect(() => {\r\n    const handleMyRequestsScroll = () => {\r\n      const container = document.getElementById('myRequestsContainer');\r\n      if (!container) return;\r\n      \r\n      const { scrollTop, scrollHeight, clientHeight } = container;\r\n      \r\n      if (scrollTop + clientHeight >= scrollHeight - 20 && !isMyRequestsLoading) {\r\n        loadMoreMyRequests();\r\n      }\r\n    };\r\n\r\n    const container = document.getElementById('myRequestsContainer');\r\n    if (container) {\r\n      container.addEventListener('scroll', handleMyRequestsScroll);\r\n      return () => container.removeEventListener('scroll', handleMyRequestsScroll);\r\n    }\r\n  }, [isMyRequestsLoading, myRequestsToShow, groupedRequests.length]);\r\n\r\n  // Infinite scroll setup for Approval Requests\r\n  useEffect(() => {\r\n    const handleApprovalRequestsScroll = () => {\r\n      const container = document.getElementById('approvalRequestsContainer');\r\n      if (!container) return;\r\n      \r\n      const { scrollTop, scrollHeight, clientHeight } = container;\r\n      \r\n      if (scrollTop + clientHeight >= scrollHeight - 20 && !isApprovalRequestsLoading) {\r\n        loadMoreApprovalRequests();\r\n      }\r\n    };\r\n\r\n    const container = document.getElementById('approvalRequestsContainer');\r\n    if (container) {\r\n      container.addEventListener('scroll', handleApprovalRequestsScroll);\r\n      return () => container.removeEventListener('scroll', handleApprovalRequestsScroll);\r\n    }\r\n  }, [isApprovalRequestsLoading, approvalRequestsToShow, leaveRequests.asSupervisor.length]);\r\n\r\n  // Infinite scroll setup for All Requests\r\n  useEffect(() => {\r\n    const handleAllRequestsScroll = () => {\r\n      const container = document.getElementById('allRequestsContainer');\r\n      if (!container) return;\r\n      \r\n      const { scrollTop, scrollHeight, clientHeight } = container;\r\n      \r\n      if (scrollTop + clientHeight >= scrollHeight - 20 && !isAllRequestsLoading) {\r\n        loadMoreAllRequests();\r\n      }\r\n    };\r\n\r\n    const container = document.getElementById('allRequestsContainer');\r\n    if (container) {\r\n      container.addEventListener('scroll', handleAllRequestsScroll);\r\n      return () => container.removeEventListener('scroll', handleAllRequestsScroll);\r\n    }\r\n  }, [isAllRequestsLoading, allRequestsToShow, leaveRequests.allRequests.length]);\r\n\r\n  // Update displayed requests when data changes\r\n  useEffect(() => {\r\n    setDisplayedMyRequests(groupedRequests.slice(0, myRequestsToShow));\r\n  }, [groupedRequests, myRequestsToShow]);\r\n\r\n  useEffect(() => {\r\n    setDisplayedApprovalRequests(leaveRequests.asSupervisor.slice(0, approvalRequestsToShow));\r\n  }, [leaveRequests.asSupervisor, approvalRequestsToShow]);\r\n\r\n  useEffect(() => {\r\n    const filteredRequests = filterTableRecords(tableFilters);\r\n    setDisplayedAllRequests(filteredRequests.slice(0, allRequestsToShow));\r\n  }, [leaveRequests.allRequests, tableFilters, allRequestsToShow]);\r\n\r\n  // Load more functions\r\n  const loadMoreMyRequests = () => {\r\n    if (isMyRequestsLoading || displayedMyRequests.length >= groupedRequests.length) return;\r\n    \r\n    setIsMyRequestsLoading(true);\r\n    setTimeout(() => {\r\n      const nextRequestsToShow = Math.min(myRequestsToShow + 20, groupedRequests.length);\r\n      setMyRequestsToShow(nextRequestsToShow);\r\n      setIsMyRequestsLoading(false);\r\n    }, 300);\r\n  };\r\n\r\n  const loadMoreApprovalRequests = () => {\r\n    if (isApprovalRequestsLoading || displayedApprovalRequests.length >= leaveRequests.asSupervisor.length) return;\r\n    \r\n    setIsApprovalRequestsLoading(true);\r\n    setTimeout(() => {\r\n      const nextRequestsToShow = Math.min(approvalRequestsToShow + 20, leaveRequests.asSupervisor.length);\r\n      setApprovalRequestsToShow(nextRequestsToShow);\r\n      setIsApprovalRequestsLoading(false);\r\n    }, 300);\r\n  };\r\n\r\n  const loadMoreAllRequests = () => {\r\n    const filteredRequests = filterTableRecords(tableFilters);\r\n    if (isAllRequestsLoading || displayedAllRequests.length >= filteredRequests.length) return;\r\n    \r\n    setIsAllRequestsLoading(true);\r\n    setTimeout(() => {\r\n      const nextRequestsToShow = Math.min(allRequestsToShow + 20, filteredRequests.length);\r\n      setAllRequestsToShow(nextRequestsToShow);\r\n      setIsAllRequestsLoading(false);\r\n    }, 300);\r\n  };\r\n\r\n\r\n  // Hàm nhóm các đơn có cùng thời gian và lý do\r\n  const groupLeaveRequests = (requests) => {\r\n    const requestGroups = {};\r\n    \r\n    requests.forEach(request => {\r\n      // Tạo key dựa trên startDate, startPeriod, endDate, endPeriod và reason\r\n      const startPeriod = request.startPeriod || 'full_day';\r\n      const endPeriod = request.endPeriod || 'full_day';\r\n      const key = `${request.startDate}_${startPeriod}_${request.endDate}_${endPeriod}_${request.reason}`;\r\n      \r\n      if (!requestGroups[key]) {\r\n        requestGroups[key] = {\r\n          id: key,\r\n          _id: request._id, // Thêm _id từ request đầu tiên\r\n          startDate: request.startDate,\r\n          startPeriod: request.startPeriod || 'full_day', // Thêm startPeriod với giá trị mặc định\r\n          endDate: request.endDate,\r\n          endPeriod: request.endPeriod || 'full_day', // Thêm endPeriod với giá trị mặc định\r\n          reason: request.reason,\r\n          createdAt: request.createdAt,\r\n          supervisors: [],\r\n          status: request.status,\r\n          comments: request.comments || '',\r\n          hasAttachments: request.hasAttachments || false,\r\n          attachmentCount: request.attachmentCount || 0,\r\n          requests: []\r\n        };\r\n      }\r\n      \r\n      // Thêm supervisor vào nhóm\r\n      if (request.supervisor && !requestGroups[key].supervisors.some(s => s._id === request.supervisor._id)) {\r\n        requestGroups[key].supervisors.push(request.supervisor);\r\n      }\r\n      \r\n      // Thêm request vào nhóm\r\n      requestGroups[key].requests.push(request);\r\n      \r\n      // Nếu bất kỳ request nào là pending, nhóm sẽ hiển thị là pending\r\n      if (request.status === 'pending') {\r\n        requestGroups[key].status = 'pending';\r\n      }\r\n      // Nếu có bất kỳ request nào bị reject mà chưa có request nào pending, nhóm sẽ hiển thị là rejected\r\n      else if (request.status === 'rejected' && requestGroups[key].status !== 'pending') {\r\n        requestGroups[key].status = 'rejected';\r\n      }\r\n      \r\n      // Tổng hợp comments từ các quản lý\r\n      if (request.comments && !requestGroups[key].comments.includes(request.comments)) {\r\n        requestGroups[key].comments += (requestGroups[key].comments ? '\\n' : '') + \r\n          `${request.supervisor?.fullName || 'Quản lý'}: ${request.comments}`;\r\n      }\r\n      \r\n      // Cập nhật thông tin file đính kèm\r\n      if (request.hasAttachments) {\r\n        requestGroups[key].hasAttachments = true;\r\n        requestGroups[key].attachmentCount = Math.max(requestGroups[key].attachmentCount || 0, request.attachmentCount || 0);\r\n      }\r\n    });\r\n    \r\n    return Object.values(requestGroups);\r\n  };\r\n\r\n  const handleCreateRequest = () => {\r\n    navigate('/leave-request/create');\r\n  };\r\n\r\n  // Hàm lấy danh sách file đính kèm\r\n  const fetchAttachments = async (leaveRequestId) => {\r\n    // Kiểm tra leaveRequestId có hợp lệ không\r\n    if (!leaveRequestId) {\r\n      console.error('ID đơn nghỉ phép không hợp lệ');\r\n      setAttachmentError('Không thể lấy danh sách file đính kèm');\r\n      setLoadingAttachments(false);\r\n      setAttachments([]); // Đặt danh sách file rỗng\r\n      return;\r\n    }\r\n    \r\n    // Kiểm tra leaveRequestId có phải là chuỗi \"undefined\" không\r\n    if (leaveRequestId === 'undefined') {\r\n      console.error('ID đơn nghỉ phép là chuỗi \"undefined\"');\r\n      setAttachmentError('Không thể lấy danh sách file đính kèm');\r\n      setLoadingAttachments(false);\r\n      setAttachments([]); // Đặt danh sách file rỗng\r\n      return;\r\n    }\r\n    \r\n    setLoadingAttachments(true);\r\n    setAttachmentError(null);\r\n    \r\n    try {\r\n      console.log('Đang lấy danh sách file đính kèm cho đơn có ID:', leaveRequestId);\r\n      const response = await API.get(`/file-attachments/${leaveRequestId}/files`, {\r\n        headers: { 'x-auth-token': token }\r\n      });\r\n      \r\n      setAttachments(response.data);\r\n      console.log('Đã lấy danh sách file đính kèm:', response.data);\r\n    } catch (err) {\r\n      console.error('Lỗi khi lấy danh sách file đính kèm:', err);\r\n      setAttachmentError('Không thể tải danh sách file đính kèm');\r\n      setAttachments([]); // Đặt danh sách file rỗng\r\n    } finally {\r\n      setLoadingAttachments(false);\r\n    }\r\n  };\r\n  \r\n  // Hàm xử lý khi chọn file\r\n  const handleFileChange = (e) => {\r\n    const files = Array.from(e.target.files);\r\n    \r\n    // Kiểm tra số lượng file (tối đa 5 file)\r\n    if (selectedFiles.length + files.length > 5) {\r\n      setUploadError('Chỉ được phép tải lên tối đa 5 file');\r\n      return;\r\n    }\r\n    \r\n    // Kiểm tra kích thước file (tối đa 10MB mỗi file)\r\n    const maxSize = 10 * 1024 * 1024; // 10MB\r\n    const oversizedFiles = files.filter(file => file.size > maxSize);\r\n    if (oversizedFiles.length > 0) {\r\n      setUploadError(`File ${oversizedFiles.map(f => f.name).join(', ')} vượt quá kích thước tối đa 10MB`);\r\n      return;\r\n    }\r\n    \r\n    // Kiểm tra loại file\r\n    const allowedTypes = [\r\n      'application/pdf',\r\n      'application/msword',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n      'application/vnd.ms-excel',\r\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n      'image/jpeg',\r\n      'image/png',\r\n      'image/gif',\r\n      'text/plain'\r\n    ];\r\n    \r\n    const invalidFiles = files.filter(file => !allowedTypes.includes(file.type));\r\n    if (invalidFiles.length > 0) {\r\n      setUploadError(`File ${invalidFiles.map(f => f.name).join(', ')} không được hỗ trợ. Chỉ chấp nhận PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF và TXT.`);\r\n      return;\r\n    }\r\n    \r\n    // Thêm các file hợp lệ vào danh sách\r\n    setSelectedFiles(prev => [...prev, ...files]);\r\n    setUploadError(null);\r\n    \r\n    // Reset input để có thể chọn lại cùng một file\r\n    e.target.value = null;\r\n  };\r\n  \r\n  // Hàm xóa file khỏi danh sách\r\n  const handleRemoveFile = (index) => {\r\n    setSelectedFiles(prev => prev.filter((_, i) => i !== index));\r\n  };\r\n  \r\n  // Hàm lấy icon tương ứng với loại file\r\n  const getFileIcon = (fileType) => {\r\n    // Kiểm tra fileType có tồn tại không\r\n    if (!fileType) return <FaFile />;\r\n    \r\n    // Chuyển đổi fileType thành chuỗi để đảm bảo có thể gọi includes()\r\n    const type = String(fileType).toLowerCase();\r\n    \r\n    if (type.includes('pdf')) return <FaFilePdf />;\r\n    if (type.includes('word') || type.includes('msword') || type.includes('doc')) return <FaFileWord />;\r\n    if (type.includes('excel') || type.includes('sheet') || type.includes('xls')) return <FaFileExcel />;\r\n    if (type.includes('image') || type.includes('jpg') || type.includes('jpeg') || type.includes('png') || type.includes('gif')) return <FaFileImage />;\r\n    return <FaFile />;\r\n  };\r\n  \r\n  // Hàm upload file\r\n  const uploadFiles = async (leaveRequestId) => {\r\n    if (selectedFiles.length === 0) return;\r\n    \r\n    // Kiểm tra leaveRequestId có hợp lệ không\r\n    if (!leaveRequestId) {\r\n      console.error('ID đơn nghỉ phép không hợp lệ');\r\n      setUploadError('Không thể tải file lên. Vui lòng thử lại sau.');\r\n      return;\r\n    }\r\n    \r\n    setUploadingFiles(true);\r\n    setUploadError(null);\r\n    \r\n    // Hiển thị thông báo đang tải lên\r\n    setSuccessMessage('Đang tải file lên...');\r\n    setShowSuccessAlert(true);\r\n    \r\n    try {\r\n      const formData = new FormData();\r\n      selectedFiles.forEach(file => {\r\n        formData.append('files', file);\r\n      });\r\n      \r\n      await API.post(\r\n        `/file-attachments/${leaveRequestId}/upload`,\r\n        formData,\r\n        {\r\n          headers: {\r\n            'x-auth-token': token,\r\n            'Content-Type': 'multipart/form-data'\r\n          }\r\n        }\r\n      );\r\n      \r\n      // Sau khi upload thành công, làm mới danh sách file\r\n      await fetchAttachments(leaveRequestId);\r\n      \r\n      // Xóa danh sách file đã chọn\r\n      setSelectedFiles([]);\r\n      \r\n      // Hiển thị thông báo thành công\r\n      setSuccessMessage('Tải file lên thành công');\r\n      setShowSuccessAlert(true);\r\n      setTimeout(() => setShowSuccessAlert(false), 3000);\r\n    } catch (err) {\r\n      console.error('Lỗi khi tải file lên:', err);\r\n      setUploadError('Lỗi khi tải file lên: ' + (err.response?.data?.message || err.message));\r\n    } finally {\r\n      setUploadingFiles(false);\r\n    }\r\n  };\r\n  \r\n  // Hàm download file\r\n  const handleDownloadFile = async (fileId) => {\r\n    try {\r\n      // Kiểm tra selectedRequest và fileId có hợp lệ không\r\n      if (!selectedRequest || !selectedRequest._id) {\r\n        console.error('Không có đơn nghỉ phép được chọn');\r\n        alert('Không thể tải file. Vui lòng thử lại sau.');\r\n        return;\r\n      }\r\n      \r\n      if (!fileId) {\r\n        console.error('ID file không hợp lệ');\r\n        alert('Không thể tải file. Vui lòng thử lại sau.');\r\n        return;\r\n      }\r\n      \r\n      const response = await API.get(`/file-attachments/${selectedRequest._id}/files/${fileId}/download`, {\r\n        headers: { 'x-auth-token': token },\r\n        responseType: 'blob'\r\n      });\r\n      \r\n      // Tìm thông tin file trong danh sách attachments\r\n      const fileInfo = attachments.find(file => file._id === fileId);\r\n      \r\n      // Tạo URL cho blob và tạo link tải xuống\r\n      const url = window.URL.createObjectURL(new Blob([response.data]));\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.setAttribute('download', fileInfo.originalName);\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      \r\n      // Dọn dẹp\r\n      link.parentNode.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n    } catch (err) {\r\n      console.error('Lỗi khi tải file:', err);\r\n      alert('Không thể tải file. Vui lòng thử lại sau.');\r\n    }\r\n  };\r\n  \r\n  // Hàm xóa file\r\n  const handleDeleteFile = async (fileId) => {\r\n    if (!confirm('Bạn có chắc chắn muốn xóa file này không?')) {\r\n      return;\r\n    }\r\n    \r\n    // Kiểm tra selectedRequest và fileId có hợp lệ không\r\n    if (!selectedRequest || !selectedRequest._id) {\r\n      console.error('Không có đơn nghỉ phép được chọn');\r\n      alert('Không thể xóa file. Vui lòng thử lại sau.');\r\n      return;\r\n    }\r\n    \r\n    if (!fileId) {\r\n      console.error('ID file không hợp lệ');\r\n      alert('Không thể xóa file. Vui lòng thử lại sau.');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      await API.delete(`/file-attachments/${selectedRequest._id}/files/${fileId}`, {\r\n        headers: { 'x-auth-token': token }\r\n      });\r\n      \r\n      // Sau khi xóa thành công, làm mới danh sách file\r\n      await fetchAttachments(selectedRequest._id);\r\n      \r\n      // Hiển thị thông báo thành công\r\n      setSuccessMessage('Xóa file thành công');\r\n      setShowSuccessAlert(true);\r\n      setTimeout(() => setShowSuccessAlert(false), 3000);\r\n    } catch (err) {\r\n      console.error('Lỗi khi xóa file:', err);\r\n      alert('Không thể xóa file. Vui lòng thử lại sau.');\r\n    }\r\n  };\r\n\r\n  const handleShowApprovalModal = async (request) => {\r\n    // Kiểm tra request có hợp lệ không\r\n    if (!request) {\r\n      console.error('Request không hợp lệ');\r\n      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');\r\n      setShowSuccessAlert(true);\r\n      setTimeout(() => setShowSuccessAlert(false), 3000);\r\n      return;\r\n    }\r\n    \r\n    // Kiểm tra request có ID hợp lệ không\r\n    if (!request._id) {\r\n      console.error('Request không có ID hợp lệ:', request);\r\n      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');\r\n      setShowSuccessAlert(true);\r\n      setTimeout(() => setShowSuccessAlert(false), 3000);\r\n      return;\r\n    }\r\n    \r\n    setSelectedRequest(request);\r\n    setApprovalData({\r\n      status: 'approved',\r\n      comments: ''\r\n    });\r\n    setShowModal(true);\r\n    \r\n    // Lấy danh sách file đính kèm\r\n    try {\r\n      await fetchAttachments(request._id);\r\n    } catch (err) {\r\n      console.error('Lỗi khi lấy danh sách file đính kèm:', err);\r\n      // Không hiển thị lỗi cho người dùng vì modal đã được mở\r\n    }\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setShowModal(false);\r\n    setSelectedRequest(null);\r\n  };\r\n  \r\n  // Hàm hiển thị modal xem file đính kèm\r\n  const handleShowAttachmentModal = async (request) => {\r\n    // Kiểm tra request có hợp lệ không\r\n    if (!request) {\r\n      console.error('Request không hợp lệ');\r\n      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');\r\n      setShowSuccessAlert(true);\r\n      setTimeout(() => setShowSuccessAlert(false), 3000);\r\n      return;\r\n    }\r\n    \r\n    // Kiểm tra request có ID hợp lệ không\r\n    if (!request._id) {\r\n      console.error('Request không có ID hợp lệ:', request);\r\n      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');\r\n      setShowSuccessAlert(true);\r\n      setTimeout(() => setShowSuccessAlert(false), 3000);\r\n      return;\r\n    }\r\n    \r\n    setSelectedRequest(request);\r\n    setShowAttachmentModal(true);\r\n    \r\n    // Lấy danh sách file đính kèm\r\n    try {\r\n      await fetchAttachments(request._id);\r\n    } catch (err) {\r\n      console.error('Lỗi khi lấy danh sách file đính kèm:', err);\r\n      // Không hiển thị lỗi cho người dùng vì modal đã được mở\r\n    }\r\n  };\r\n  \r\n  // Hàm đóng modal xem file đính kèm\r\n  const handleCloseAttachmentModal = () => {\r\n    setShowAttachmentModal(false);\r\n    setSelectedRequest(null);\r\n  };\r\n\r\n  const handleApprovalChange = (e) => {\r\n    setApprovalData({\r\n      ...approvalData,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  const handleSubmitApproval = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    try {\r\n      console.log(`Đang duyệt đơn nghỉ phép với ID: ${selectedRequest._id}`, approvalData);\r\n      \r\n      // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js\r\n      await API.put(\r\n        `/leave-requests/${selectedRequest._id}`,\r\n        approvalData\r\n      );\r\n\r\n      // Cập nhật trạng thái trong tất cả các danh sách\r\n      setLeaveRequests(prev => {\r\n        // Cập nhật trong danh sách asSupervisor\r\n        const updatedAsSupervisor = prev.asSupervisor.map(req => \r\n          req._id === selectedRequest._id \r\n            ? { ...req, status: approvalData.status, comments: approvalData.comments } \r\n            : req\r\n        );\r\n        \r\n        // Cập nhật trong danh sách allRequests\r\n        const updatedAllRequests = prev.allRequests.map(req => \r\n          req._id === selectedRequest._id \r\n            ? { ...req, status: approvalData.status, comments: approvalData.comments } \r\n            : req\r\n        );\r\n        \r\n        // Cập nhật trong danh sách asEmployee nếu có\r\n        const updatedAsEmployee = prev.asEmployee.map(req => \r\n          req._id === selectedRequest._id \r\n            ? { ...req, status: approvalData.status, comments: approvalData.comments } \r\n            : req\r\n        );\r\n        \r\n        return {\r\n          asEmployee: updatedAsEmployee,\r\n          asSupervisor: updatedAsSupervisor,\r\n          allRequests: updatedAllRequests\r\n        };\r\n      });\r\n      \r\n      // Cập nhật groupedRequests nếu đơn được duyệt/từ chối là của người dùng hiện tại\r\n      const isUserRequest = leaveRequests.asEmployee.some(req => req._id === selectedRequest._id);\r\n      if (isUserRequest) {\r\n        const updatedGrouped = groupLeaveRequests(\r\n          leaveRequests.asEmployee.map(req => \r\n            req._id === selectedRequest._id \r\n              ? { ...req, status: approvalData.status, comments: approvalData.comments } \r\n              : req\r\n          )\r\n        );\r\n        setGroupedRequests(updatedGrouped);\r\n      }\r\n\r\n      // Hiển thị thông báo thành công\r\n      setSuccessMessage('Đã cập nhật trạng thái đơn thành công');\r\n      setShowSuccessAlert(true);\r\n      setTimeout(() => setShowSuccessAlert(false), 3000);\r\n\r\n      // Đóng modal\r\n      handleCloseModal();\r\n      \r\n      // Làm mới dữ liệu sau 500ms để đảm bảo API đã cập nhật xong\r\n      setTimeout(async () => {\r\n        try {\r\n          const res = await API.get('/leave-requests');\r\n          \r\n          // Cập nhật state với dữ liệu mới từ API\r\n          setLeaveRequests({\r\n            asEmployee: res.data.asEmployee || [],\r\n            asSupervisor: res.data.asSupervisor || [],\r\n            allRequests: res.data.allRequests || []\r\n          });\r\n          \r\n          // Cập nhật groupedRequests\r\n          const grouped = groupLeaveRequests(res.data.asEmployee || []);\r\n          setGroupedRequests(grouped);\r\n        } catch (err) {\r\n          console.error('Lỗi khi làm mới dữ liệu:', err);\r\n        }\r\n      }, 500);\r\n    } catch (err) {\r\n      console.error('Lỗi khi cập nhật đơn xin nghỉ phép:', err);\r\n      setError('Không thể cập nhật đơn xin nghỉ phép. Vui lòng thử lại sau.');\r\n      // Hiển thị thông báo lỗi chi tiết\r\n      alert(`Lỗi: ${err.response?.data?.msg || err.message}`);\r\n    }\r\n  };\r\n  \r\n  // Hàm mở modal chỉnh sửa đơn\r\n  const handleShowEditModal = (request) => {\r\n    // Chuyển đổi định dạng ngày tháng từ ISO sang YYYY-MM-DD cho input date\r\n    const formatDateForInput = (dateString) => {\r\n      if (!dateString) return '';\r\n      const date = new Date(dateString);\r\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\r\n    };\r\n    \r\n    // Kiểm tra quyền chỉnh sửa\r\n    const isAdmin = user && user.role === 'SUPER_ADMIN';\r\n    \r\n    // Kiểm tra quyền sở hữu đơn - xử lý nhiều trường hợp khác nhau\r\n    let isOwner = false;\r\n    \r\n    // Trường hợp 1: request có thuộc tính employee với _id\r\n    if (request.employee && request.employee._id === user._id) {\r\n      isOwner = true;\r\n    }\r\n    // Trường hợp 2: request có thuộc tính employeeId\r\n    else if (request.employeeId === user._id) {\r\n      isOwner = true;\r\n    }\r\n    // Trường hợp 3: request có thuộc tính employee với id\r\n    else if (request.employee && request.employee.id === user._id) {\r\n      isOwner = true;\r\n    }\r\n    // Trường hợp 4: Đơn được tạo bởi người dùng hiện tại (dựa vào createdBy)\r\n    else if (request.createdBy === user._id) {\r\n      isOwner = true;\r\n    }\r\n    \r\n    // Debug: Ghi log thông tin để kiểm tra\r\n    console.log('User ID:', user?._id);\r\n    console.log('Request:', request);\r\n    console.log('Is owner:', isOwner);\r\n    \r\n    const isPending = request.status === 'pending';\r\n    \r\n    // Kiểm tra quyền chỉnh sửa:\r\n    // 1. Admin có thể chỉnh sửa bất kỳ đơn nào\r\n    // 2. Nhân viên chỉ có thể chỉnh sửa đơn của mình và đơn phải ở trạng thái chờ duyệt\r\n    if (isAdmin) {\r\n      // Admin có thể chỉnh sửa bất kỳ đơn nào\r\n    } else if (isOwner && isPending) {\r\n      // Nhân viên có thể chỉnh sửa đơn của mình nếu đơn đang ở trạng thái chờ duyệt\r\n    } else {\r\n      // Các trường hợp khác không được phép chỉnh sửa\r\n      if (!isOwner) {\r\n        alert('Bạn không có quyền chỉnh sửa đơn này vì đây không phải đơn của bạn');\r\n      } else if (!isPending) {\r\n        alert('Bạn không thể chỉnh sửa đơn này vì đơn đã được duyệt/từ chối');\r\n      } else {\r\n        alert('Bạn không có quyền chỉnh sửa đơn này');\r\n      }\r\n      return;\r\n    }\r\n    \r\n    setSelectedRequest(request);\r\n    setEditData({\r\n      startDate: formatDateForInput(request.startDate),\r\n      startPeriod: request.startPeriod || 'full_day',\r\n      endDate: formatDateForInput(request.endDate),\r\n      endPeriod: request.endPeriod || 'full_day',\r\n      reason: request.reason,\r\n      status: request.status,\r\n      supervisor: request.supervisor?._id || request.supervisor || '', // Lưu ID của cấp trên\r\n      visibility: request.visibility || 'all',\r\n      visibleTo: request.visibleTo || [],\r\n      hiddenFrom: request.hiddenFrom || []\r\n    });\r\n    setShowEditModal(true);\r\n    \r\n    // Lấy danh sách file đính kèm\r\n    fetchAttachments(request._id);\r\n  };\r\n  \r\n  // Hàm đóng modal chỉnh sửa\r\n  const handleCloseEditModal = () => {\r\n    setShowEditModal(false);\r\n    setSelectedRequest(null);\r\n  };\r\n  \r\n  // Hàm xử lý thay đổi dữ liệu chỉnh sửa\r\n  const handleEditChange = (e) => {\r\n    const { name, value } = e.target;\r\n    \r\n    setEditData(prev => {\r\n      const newState = { ...prev, [name]: value };\r\n      \r\n      // Xử lý logic đặc biệt cho ngày tháng\r\n      if (name === 'startDate' || name === 'endDate') {\r\n        // Kiểm tra nếu ngày bắt đầu và kết thúc là cùng một ngày\r\n        const startDate = name === 'startDate' ? value : prev.startDate;\r\n        const endDate = name === 'endDate' ? value : prev.endDate;\r\n        const isSameDay = new Date(startDate).setHours(0,0,0,0) === new Date(endDate).setHours(0,0,0,0);\r\n        \r\n        // Nếu cùng một ngày, áp dụng các quy tắc thời gian kết thúc\r\n        if (isSameDay) {\r\n          if (prev.startPeriod === 'full_day') {\r\n            newState.endPeriod = 'full_day';\r\n          } else if (prev.startPeriod === 'afternoon') {\r\n            newState.endPeriod = 'afternoon';\r\n          }\r\n        }\r\n      }\r\n      \r\n      return newState;\r\n    });\r\n  };\r\n  \r\n  // Hàm gửi dữ liệu chỉnh sửa\r\n  const handleSubmitEdit = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    try {\r\n      // Tạo dữ liệu để gửi lên server\r\n      const dataToSubmit = { ...editData };\r\n      \r\n      // Nếu không phải admin, không cho phép thay đổi trạng thái và visibility\r\n      if (user.role !== 'SUPER_ADMIN') {\r\n        // Xóa các trường không được phép thay đổi\r\n        delete dataToSubmit.status;\r\n        delete dataToSubmit.visibility;\r\n        delete dataToSubmit.visibleTo;\r\n        delete dataToSubmit.hiddenFrom;\r\n      }\r\n      \r\n      console.log(`Đang cập nhật đơn nghỉ phép với ID: ${selectedRequest._id}`, dataToSubmit);\r\n      \r\n      // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js\r\n      const response = await API.put(\r\n        `/leave-requests/${selectedRequest._id}`,\r\n        dataToSubmit\r\n      );\r\n      \r\n      // Cập nhật state với dữ liệu mới\r\n      const updatedRequest = response.data;\r\n      \r\n      // Cập nhật các danh sách đơn\r\n      setLeaveRequests(prev => {\r\n        // Cập nhật trong danh sách asEmployee\r\n        const updatedAsEmployee = prev.asEmployee.map(req => \r\n          req._id === updatedRequest._id ? updatedRequest : req\r\n        );\r\n        \r\n        // Cập nhật trong danh sách asSupervisor\r\n        const updatedAsSupervisor = prev.asSupervisor.map(req => \r\n          req._id === updatedRequest._id ? updatedRequest : req\r\n        );\r\n        \r\n        // Cập nhật trong danh sách allRequests\r\n        const updatedAllRequests = prev.allRequests.map(req => \r\n          req._id === updatedRequest._id ? updatedRequest : req\r\n        );\r\n        \r\n        return {\r\n          asEmployee: updatedAsEmployee,\r\n          asSupervisor: updatedAsSupervisor,\r\n          allRequests: updatedAllRequests\r\n        };\r\n      });\r\n      \r\n      // Cập nhật groupedRequests\r\n      const updatedGrouped = groupLeaveRequests(leaveRequests.asEmployee.map(req => \r\n        req._id === updatedRequest._id ? updatedRequest : req\r\n      ));\r\n      setGroupedRequests(updatedGrouped);\r\n      \r\n      // Hiển thị thông báo thành công\r\n      setSuccessMessage('Đã cập nhật đơn nghỉ phép thành công');\r\n      setShowSuccessAlert(true);\r\n      setTimeout(() => setShowSuccessAlert(false), 3000);\r\n      \r\n      handleCloseEditModal();\r\n    } catch (err) {\r\n      console.error('Lỗi khi cập nhật đơn xin nghỉ phép:', err);\r\n      setError('Không thể cập nhật đơn xin nghỉ phép. Vui lòng thử lại sau.');\r\n      // Hiển thị thông báo lỗi chi tiết\r\n      alert(`Lỗi: ${err.response?.data?.msg || err.message}`);\r\n    }\r\n  };\r\n  \r\n  // Hàm mở modal xác nhận xóa\r\n  const handleShowDeleteModal = (request) => {\r\n    // Kiểm tra quyền xóa\r\n    const isAdmin = user && user.role === 'SUPER_ADMIN';\r\n    \r\n    // Kiểm tra quyền sở hữu đơn - xử lý nhiều trường hợp khác nhau\r\n    let isOwner = false;\r\n    \r\n    // Trường hợp 1: request có thuộc tính employee với _id\r\n    if (request.employee && request.employee._id === user._id) {\r\n      isOwner = true;\r\n    }\r\n    // Trường hợp 2: request có thuộc tính employeeId\r\n    else if (request.employeeId === user._id) {\r\n      isOwner = true;\r\n    }\r\n    // Trường hợp 3: request có thuộc tính employee với id\r\n    else if (request.employee && request.employee.id === user._id) {\r\n      isOwner = true;\r\n    }\r\n    // Trường hợp 4: Đơn được tạo bởi người dùng hiện tại (dựa vào createdBy)\r\n    else if (request.createdBy === user._id) {\r\n      isOwner = true;\r\n    }\r\n    \r\n    // Debug: Ghi log thông tin để kiểm tra\r\n    console.log('Delete - User ID:', user?._id);\r\n    console.log('Delete - Request:', request);\r\n    console.log('Delete - Is owner:', isOwner);\r\n    \r\n    const isPending = request.status === 'pending';\r\n    \r\n    // Kiểm tra quyền xóa:\r\n    // 1. Admin có thể xóa bất kỳ đơn nào\r\n    // 2. Nhân viên chỉ có thể xóa đơn của mình và đơn phải ở trạng thái chờ duyệt\r\n    if (isAdmin) {\r\n      // Admin có thể xóa bất kỳ đơn nào\r\n    } else if (isOwner && isPending) {\r\n      // Nhân viên có thể xóa đơn của mình nếu đơn đang ở trạng thái chờ duyệt\r\n    } else {\r\n      // Các trường hợp khác không được phép xóa\r\n      if (!isOwner) {\r\n        alert('Bạn không có quyền xóa đơn này vì đây không phải đơn của bạn');\r\n      } else if (!isPending) {\r\n        alert('Bạn không thể xóa đơn này vì đơn đã được duyệt/từ chối');\r\n      } else {\r\n        alert('Bạn không có quyền xóa đơn này');\r\n      }\r\n      return;\r\n    }\r\n    \r\n    setRequestToDelete(request);\r\n    setShowDeleteModal(true);\r\n  };\r\n  \r\n  // Hàm đóng modal xác nhận xóa\r\n  const handleCloseDeleteModal = () => {\r\n    setShowDeleteModal(false);\r\n    setRequestToDelete(null);\r\n  };\r\n  \r\n  // Hàm xóa đơn nghỉ phép\r\n  const handleDeleteRequest = async () => {\r\n    try {\r\n      console.log(`Đang xóa đơn nghỉ phép với ID: ${requestToDelete._id}`);\r\n      \r\n      // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js\r\n      await API.delete(`/leave-requests/${requestToDelete._id}`);\r\n      \r\n      // Cập nhật state sau khi xóa\r\n      setLeaveRequests(prev => ({\r\n        asEmployee: prev.asEmployee.filter(req => req._id !== requestToDelete._id),\r\n        asSupervisor: prev.asSupervisor.filter(req => req._id !== requestToDelete._id),\r\n        allRequests: prev.allRequests.filter(req => req._id !== requestToDelete._id)\r\n      }));\r\n      \r\n      // Cập nhật groupedRequests\r\n      const updatedGrouped = groupLeaveRequests(\r\n        leaveRequests.asEmployee.filter(req => req._id !== requestToDelete._id)\r\n      );\r\n      setGroupedRequests(updatedGrouped);\r\n      \r\n      // Hiển thị thông báo thành công\r\n      setSuccessMessage('Đã xóa đơn nghỉ phép thành công');\r\n      setShowSuccessAlert(true);\r\n      setTimeout(() => setShowSuccessAlert(false), 3000);\r\n      \r\n      handleCloseDeleteModal();\r\n    } catch (err) {\r\n      console.error('Lỗi khi xóa đơn xin nghỉ phép:', err);\r\n      \r\n      // Kiểm tra nếu lỗi là 500 nhưng đơn vẫn được xóa (lỗi khi tạo thông báo)\r\n      if (err.response?.status === 500 && err.response?.data?.error?.includes('Notification validation failed')) {\r\n        // Vẫn cập nhật UI như đã xóa thành công\r\n        setLeaveRequests(prev => ({\r\n          asEmployee: prev.asEmployee.filter(req => req._id !== requestToDelete._id),\r\n          asSupervisor: prev.asSupervisor.filter(req => req._id !== requestToDelete._id),\r\n          allRequests: prev.allRequests.filter(req => req._id !== requestToDelete._id)\r\n        }));\r\n        \r\n        // Cập nhật groupedRequests\r\n        const updatedGrouped = groupLeaveRequests(\r\n          leaveRequests.asEmployee.filter(req => req._id !== requestToDelete._id)\r\n        );\r\n        setGroupedRequests(updatedGrouped);\r\n        \r\n        // Hiển thị thông báo thành công\r\n        setSuccessMessage('Đã xóa đơn nghỉ phép thành công (có lỗi thông báo)');\r\n        setShowSuccessAlert(true);\r\n        setTimeout(() => setShowSuccessAlert(false), 3000);\r\n        \r\n        handleCloseDeleteModal();\r\n      } else {\r\n        setError('Không thể xóa đơn xin nghỉ phép. Vui lòng thử lại sau.');\r\n        // Hiển thị thông báo lỗi chi tiết\r\n        alert(`Lỗi: ${err.response?.data?.msg || err.message}`);\r\n      }\r\n    }\r\n  };\r\n\r\n  const getStatusBadge = (status) => {\r\n    switch (status) {\r\n      case 'pending':\r\n        return <Badge bg=\"warning\">Chờ duyệt</Badge>;\r\n      case 'approved':\r\n        return <Badge bg=\"success\">Đã duyệt</Badge>;\r\n      case 'rejected':\r\n        return <Badge bg=\"danger\">Đã từ chối</Badge>;\r\n      case 'cancelled':\r\n        return <Badge bg=\"secondary\">Đã hủy</Badge>;\r\n      default:\r\n        return <Badge bg=\"secondary\">Không xác định</Badge>;\r\n    }\r\n  };\r\n  \r\n  // Hiển thị biểu tượng file đính kèm\r\n  const getAttachmentBadge = (request) => {\r\n    if (request && request.hasAttachments) {\r\n      // Kiểm tra xem request có ID hợp lệ không\r\n      const hasValidId = request._id || (request.requests && request.requests.length > 0 && request.requests[0]._id);\r\n      \r\n      if (!hasValidId) {\r\n        console.warn('Đơn nghỉ phép không có ID hợp lệ:', request);\r\n        return (\r\n          <Badge bg=\"secondary\" title=\"Không thể xem file đính kèm\">\r\n            <FaFile className=\"me-1\" /> {request.attachmentCount}\r\n          </Badge>\r\n        );\r\n      }\r\n      \r\n      return (\r\n        <OverlayTrigger\r\n          placement=\"top\"\r\n          overlay={<Tooltip>Xem {request.attachmentCount} file đính kèm</Tooltip>}\r\n        >\r\n          <Button \r\n            variant=\"outline-info\" \r\n            size=\"sm\"\r\n            className=\"d-flex align-items-center file-attachment-btn\"\r\n            onClick={() => {\r\n              // Tìm ID hợp lệ cho đơn nghỉ phép\r\n              let validRequest = {...request};\r\n              \r\n              // Nếu request không có _id nhưng có requests array, sử dụng _id của request đầu tiên\r\n              if (!validRequest._id && validRequest.requests && validRequest.requests.length > 0) {\r\n                validRequest._id = validRequest.requests[0]._id;\r\n              }\r\n              \r\n              // Mở modal duyệt đơn\r\n              handleShowApprovalModal(validRequest);\r\n            }}\r\n          >\r\n            <FaFile className=\"me-1\" /> {request.attachmentCount}\r\n          </Button>\r\n        </OverlayTrigger>\r\n      );\r\n    }\r\n    return <span className=\"text-muted\">-</span>;\r\n  };\r\n  \r\n  // Hiển thị biểu tượng file đính kèm cho tab \"Đơn xin nghỉ phép của tôi\"\r\n  const getMyRequestAttachmentBadge = (request) => {\r\n    if (request && request.hasAttachments) {\r\n      // Kiểm tra xem request có ID hợp lệ không\r\n      const hasValidId = request._id || (request.requests && request.requests.length > 0 && request.requests[0]._id);\r\n      \r\n      if (!hasValidId) {\r\n        console.warn('Đơn nghỉ phép không có ID hợp lệ:', request);\r\n        return (\r\n          <Badge bg=\"secondary\" title=\"Không thể xem file đính kèm\">\r\n            <FaFile className=\"me-1\" /> {request.attachmentCount}\r\n          </Badge>\r\n        );\r\n      }\r\n      \r\n      return (\r\n        <OverlayTrigger\r\n          placement=\"top\"\r\n          overlay={<Tooltip>Xem {request.attachmentCount} file đính kèm</Tooltip>}\r\n        >\r\n          <Button \r\n            variant=\"outline-info\" \r\n            size=\"sm\"\r\n            className=\"d-flex align-items-center file-attachment-btn\"\r\n            onClick={() => {\r\n              // Tìm ID hợp lệ cho đơn nghỉ phép\r\n              let validRequest = {...request};\r\n              \r\n              // Nếu request không có _id nhưng có requests array, sử dụng _id của request đầu tiên\r\n              if (!validRequest._id && validRequest.requests && validRequest.requests.length > 0) {\r\n                validRequest._id = validRequest.requests[0]._id;\r\n              }\r\n              \r\n              // Mở modal xem file đính kèm\r\n              handleShowAttachmentModal(validRequest);\r\n            }}\r\n          >\r\n            <FaFile className=\"me-1\" /> {request.attachmentCount}\r\n          </Button>\r\n        </OverlayTrigger>\r\n      );\r\n    }\r\n    return <span className=\"text-muted\">-</span>;\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return '';\r\n    \r\n    try {\r\n      // Xử lý timezone để đảm bảo hiển thị đúng ngày theo giờ Việt Nam\r\n      const date = new Date(dateString);\r\n      \r\n      // Tạo một đối tượng Date mới với giờ là 12:00 giờ Việt Nam để tránh vấn đề timezone\r\n      // Lấy các thành phần ngày tháng năm từ đối tượng Date gốc\r\n      const year = date.getFullYear();\r\n      const month = date.getMonth();\r\n      const day = date.getDate();\r\n      \r\n      // Tạo đối tượng Date mới với giờ cố định là 12:00\r\n      const adjustedDate = new Date(year, month, day, 12, 0, 0);\r\n      \r\n      console.log(`Ngày gốc: ${dateString}, Ngày hiển thị: ${adjustedDate.toISOString()}`);\r\n      \r\n      // Format ngày theo định dạng Việt Nam\r\n      return format(adjustedDate, 'dd/MM/yyyy', { locale: vi });\r\n    } catch (error) {\r\n      console.error('Lỗi khi format ngày:', error, dateString);\r\n      return 'Ngày không hợp lệ';\r\n    }\r\n  };\r\n  \r\n  // Hàm định dạng thời gian nghỉ với period\r\n  const formatLeaveTime = (startDate, startPeriod, endDate, endPeriod) => {\r\n    const start = formatDate(startDate);\r\n    const end = formatDate(endDate);\r\n    \r\n    const startText = startPeriod && startPeriod !== 'full_day' \r\n      ? `${start} (${startPeriod === 'morning' ? 'Sáng' : 'Chiều'})` \r\n      : start;\r\n      \r\n    const endText = endPeriod && endPeriod !== 'full_day'\r\n      ? `${end} (${endPeriod === 'morning' ? 'Sáng' : 'Chiều'})` \r\n      : end;\r\n      \r\n    return `${startText} - ${endText}`;\r\n  };\r\n\r\n  // Render danh sách quản lý\r\n  const renderSupervisors = (supervisors) => {\r\n    if (!supervisors || supervisors.length === 0) return 'Chưa xác định';\r\n    \r\n    if (supervisors.length === 1) {\r\n      return supervisors[0].fullName || 'Chưa xác định';\r\n    }\r\n    \r\n    return (\r\n      <OverlayTrigger\r\n        placement=\"right\"\r\n        overlay={\r\n          <Tooltip id=\"supervisors-tooltip\">\r\n            {supervisors.map(sup => sup.fullName).join(', ')}\r\n          </Tooltip>\r\n        }\r\n      >\r\n        <span>\r\n          {supervisors[0].fullName} và {supervisors.length - 1} khác\r\n        </span>\r\n      </OverlayTrigger>\r\n    );\r\n  };\r\n\r\n  const handleTableFilterChange = (e) => {\r\n    setTableFilters({\r\n      ...tableFilters,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  const resetTableFilters = () => {\r\n    setTableFilters({\r\n      employeeName: '',\r\n      supervisorName: '',\r\n      status: ''\r\n    });\r\n  };\r\n\r\n  // Calculate leave request statistics for all employees\r\n  const calculateLeaveStatistics = () => {\r\n    if (!leaveRequests.allRequests || leaveRequests.allRequests.length === 0) {\r\n      return [];\r\n    }\r\n\r\n    const currentYear = new Date().getFullYear();\r\n    const employeeStats = {};\r\n    const processedRequests = new Set(); // Track processed request IDs to avoid duplicates\r\n\r\n    leaveRequests.allRequests.forEach(request => {\r\n      const requestYear = new Date(request.createdAt).getFullYear();\r\n      const requestMonth = new Date(request.createdAt).getMonth();\r\n      const employeeId = request.employee?._id;\r\n      const employeeName = request.employee?.fullName || 'Chưa xác định';\r\n\r\n      if (!employeeId || requestYear !== currentYear) return;\r\n\r\n      // Create unique key based on employee + dates only (ignore reason)\r\n      // This treats requests on same dates as one request regardless of reason\r\n      const uniqueKey = `${employeeId}-${request.startDate}-${request.endDate}-${request.startPeriod || ''}-${request.endPeriod || ''}`;\r\n\r\n      if (processedRequests.has(uniqueKey)) {\r\n        return; // Skip duplicate request (same employee, same dates, same reason)\r\n      }\r\n      processedRequests.add(uniqueKey);\r\n\r\n      if (!employeeStats[employeeId]) {\r\n        employeeStats[employeeId] = {\r\n          employeeName,\r\n          totalRequests: 0,\r\n          totalDays: 0,\r\n          monthlyRequests: {},\r\n          approvedRequests: 0,\r\n          pendingRequests: 0,\r\n          rejectedRequests: 0\r\n        };\r\n      }\r\n\r\n      const stats = employeeStats[employeeId];\r\n      stats.totalRequests++;\r\n\r\n      // Calculate leave days\r\n      const leaveDays = calculateLeaveDuration(\r\n        request.startDate,\r\n        request.startPeriod,\r\n        request.endDate,\r\n        request.endPeriod\r\n      );\r\n      stats.totalDays += leaveDays;\r\n\r\n      // Track monthly requests\r\n      const monthKey = `${requestYear}-${requestMonth}`;\r\n      stats.monthlyRequests[monthKey] = (stats.monthlyRequests[monthKey] || 0) + 1;\r\n\r\n      // Track by status\r\n      if (request.status === 'approved') stats.approvedRequests++;\r\n      else if (request.status === 'pending') stats.pendingRequests++;\r\n      else if (request.status === 'rejected') stats.rejectedRequests++;\r\n    });\r\n\r\n    return Object.values(employeeStats).sort((a, b) => a.employeeName.localeCompare(b.employeeName));\r\n  };\r\n\r\n  // Check if employee has exceeded monthly limit\r\n  const checkMonthlyLimit = (employeeStats) => {\r\n    const currentMonth = `${new Date().getFullYear()}-${new Date().getMonth()}`;\r\n    return (employeeStats.monthlyRequests[currentMonth] || 0) >= 1;\r\n  };\r\n\r\n  // Check if employee has exceeded annual limit\r\n  const checkAnnualLimit = (employeeStats) => {\r\n    return employeeStats.totalRequests >= 12;\r\n  };\r\n\r\n  const filterTableRecords = (filters) => {\r\n    // Kiểm tra nếu allRequests là undefined hoặc null\r\n    if (!leaveRequests.allRequests || leaveRequests.allRequests.length === 0) {\r\n      console.log('Không có đơn nghỉ phép nào để lọc');\r\n      return [];\r\n    }\r\n    \r\n    return leaveRequests.allRequests.filter(request => {\r\n      // Kiểm tra nếu employee hoặc supervisor là undefined hoặc null\r\n      const employeeFullName = request.employee?.fullName || '';\r\n      const supervisorFullName = request.supervisor?.fullName || '';\r\n      \r\n      const matchesEmployee = filters.employeeName ? \r\n        employeeFullName.toLowerCase().includes(filters.employeeName.toLowerCase()) : true;\r\n      \r\n      const matchesSupervisor = filters.supervisorName ? \r\n        supervisorFullName.toLowerCase().includes(filters.supervisorName.toLowerCase()) : true;\r\n      \r\n      const matchesStatus = filters.status ? request.status === filters.status : true;\r\n      \r\n      return matchesEmployee && matchesSupervisor && matchesStatus;\r\n    });\r\n  };\r\n\r\n  const getTablePaginatedRecords = (records) => {\r\n    // Kiểm tra nếu records là undefined hoặc null\r\n    if (!records || records.length === 0) {\r\n      console.log('Không có đơn nghỉ phép nào để hiển thị');\r\n      return [];\r\n    }\r\n    \r\n    const filteredRecords = filterTableRecords(tableFilters);\r\n    console.log(`Số lượng đơn sau khi lọc: ${filteredRecords.length}`);\r\n    \r\n    const startIndex = (tableCurrentPage - 1) * tableRecordsPerPage;\r\n    const endIndex = startIndex + tableRecordsPerPage;\r\n    return filteredRecords.slice(startIndex, endIndex);\r\n  };\r\n\r\n  if (!user) {\r\n    return (\r\n      <Container className=\"mt-4\">\r\n        <Card>\r\n          <Card.Body className=\"text-center\">\r\n            <p>Bạn cần đăng nhập để xem đơn xin nghỉ phép</p>\r\n            <Button variant=\"primary\" onClick={() => navigate('/login')}>\r\n              Đăng nhập\r\n            </Button>\r\n          </Card.Body>\r\n        </Card>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <Container className=\"mt-4\">\r\n        <Card>\r\n          <Card.Body className=\"text-center\">\r\n            <p>Đang tải dữ liệu...</p>\r\n          </Card.Body>\r\n        </Card>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Container className=\"mt-4\">\r\n        <Card>\r\n          <Card.Body className=\"text-center\">\r\n            <p className=\"text-danger\">{error}</p>\r\n            <Button variant=\"primary\" onClick={() => window.location.reload()}>\r\n              Thử lại\r\n            </Button>\r\n          </Card.Body>\r\n        </Card>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container className=\"leave-requests-container\">\r\n      {/* Hiển thị thông báo thành công */}\r\n      {showSuccessAlert && (\r\n        <Alert \r\n          variant=\"success\" \r\n          className=\"position-fixed top-0 start-50 translate-middle-x mt-3\"\r\n          style={{ zIndex: 1050 }}\r\n          onClose={() => setShowSuccessAlert(false)} \r\n          dismissible\r\n        >\r\n          {successMessage}\r\n        </Alert>\r\n      )}\r\n      <Row className=\"mb-4\">\r\n        <Col>\r\n          <h2>Quản lý nghỉ phép</h2>\r\n        </Col>\r\n        <Col xs=\"auto\">\r\n          <Button variant=\"primary\" onClick={handleCreateRequest}>\r\n            Tạo đơn xin nghỉ phép\r\n          </Button>\r\n        </Col>\r\n      </Row>\r\n\r\n      <Tabs defaultActiveKey=\"my-requests\" className=\"mb-3\">\r\n        <Tab eventKey=\"my-requests\" title=\"Đơn xin nghỉ phép của tôi\">\r\n          <div className=\"employee-simple-container\">\r\n            <div className=\"employee-header\">\r\n              <h4 style={{ margin: '0', color: '#333', display: 'flex', alignItems: 'center' }}>\r\n                <i className=\"fas fa-calendar-alt\" style={{ marginRight: '8px' }}></i>\r\n                Đơn xin nghỉ phép của tôi\r\n              </h4>\r\n              <span className=\"employee-count\">\r\n                {groupedRequests.length} đơn xin nghỉ phép\r\n              </span>\r\n            </div>\r\n            \r\n            {/* Danh sách đơn nghỉ phép với infinite scroll */}\r\n            <div className=\"employee-list-simple\" id=\"myRequestsContainer\">\r\n              {displayedMyRequests.length === 0 ? (\r\n                <div className=\"no-employees\">\r\n                  <div className=\"no-employees-icon\">\r\n                    <i className=\"fas fa-calendar-times\"></i>\r\n                  </div>\r\n                  <p>Bạn chưa có đơn xin nghỉ phép nào</p>\r\n                </div>\r\n              ) : (\r\n                displayedMyRequests.map((request, index) => (\r\n                  <div key={request.id} className=\"employee-card-simple\">\r\n                    <div className=\"employee-info-main\">\r\n                      <div className=\"employee-avatar\">\r\n                        <i className=\"fas fa-calendar-check\"></i>\r\n                      </div>\r\n                      <div className=\"employee-details\">\r\n                        <h4>Đơn nghỉ phép #{index + 1}</h4>\r\n                        <p><i className=\"fas fa-calendar\"></i> {formatDate(request.createdAt)}</p>\r\n                        <p><i className=\"fas fa-clock\"></i> {formatLeaveTime(request.startDate, request.startPeriod, request.endDate, request.endPeriod)}</p>\r\n                        <p><strong>{formatDuration(calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod))}</strong></p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"employee-contact\">\r\n                      <p><i className=\"fas fa-user-tie\"></i> <strong>Cấp trên:</strong> {renderSupervisors(request.supervisors)}</p>\r\n                      <p><i className=\"fas fa-comment\"></i> <strong>Lý do:</strong> {request.reason}</p>\r\n                      {request.comments && (\r\n                        <p><i className=\"fas fa-sticky-note\"></i> <strong>Nhận xét:</strong> {request.comments}</p>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div className=\"employee-work-info\">\r\n                      <p>\r\n                        <i className=\"fas fa-info-circle\"></i>\r\n                        <strong>Trạng thái:</strong> {getStatusBadge(request.status)}\r\n                      </p>\r\n                      <p>\r\n                        <i className=\"fas fa-paperclip\"></i>\r\n                        <strong>File đính kèm:</strong> {getMyRequestAttachmentBadge(request)}\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"employee-actions\">\r\n                      {request.status === 'pending' && request.requests && request.requests.length > 0 && (\r\n                        <>\r\n                          <button \r\n                            className=\"btn-view\"\r\n                            onClick={() => {\r\n                              const enhancedRequest = {\r\n                                ...request.requests[0],\r\n                                employee: { _id: user._id, fullName: user.fullName },\r\n                                employeeId: user._id,\r\n                                createdBy: user._id\r\n                              };\r\n                              handleShowEditModal(enhancedRequest);\r\n                            }}\r\n                            title=\"Chỉnh sửa đơn\"\r\n                          >\r\n                            <i className=\"fas fa-edit\"></i> Chỉnh sửa\r\n                          </button>\r\n                          <button \r\n                            className=\"btn-delete\"\r\n                            onClick={() => {\r\n                              const enhancedRequest = {\r\n                                ...request.requests[0],\r\n                                employee: { _id: user._id, fullName: user.fullName },\r\n                                employeeId: user._id,\r\n                                createdBy: user._id\r\n                              };\r\n                              handleShowDeleteModal(enhancedRequest);\r\n                            }}\r\n                            title=\"Xóa đơn\"\r\n                            style={{ background: '#dc3545', color: 'white' }}\r\n                          >\r\n                            <i className=\"fas fa-trash-alt\"></i> Xóa\r\n                          </button>\r\n                        </>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              )}\r\n              \r\n              {/* Loading more indicator */}\r\n              {isMyRequestsLoading && (\r\n                <div className=\"loading-more\">\r\n                  <div className=\"spinner\"></div>\r\n                  <span>Đang tải thêm...</span>\r\n                </div>\r\n              )}\r\n              \r\n              {/* End of results */}\r\n              {displayedMyRequests.length > 0 && displayedMyRequests.length >= groupedRequests.length && (\r\n                <div className=\"end-of-results\">\r\n                  <p>Đã hiển thị tất cả {groupedRequests.length} đơn xin nghỉ phép</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </Tab>\r\n\r\n        <Tab eventKey=\"approval-requests\" title=\"Đơn cần duyệt\">\r\n          <div className=\"employee-simple-container\">\r\n            <div className=\"employee-header\">\r\n              <h4 style={{ margin: '0', color: '#333', display: 'flex', alignItems: 'center' }}>\r\n                <i className=\"fas fa-user-check\" style={{ marginRight: '8px' }}></i>\r\n                Đơn cần duyệt\r\n              </h4>\r\n              <span className=\"employee-count\">\r\n                {leaveRequests.asSupervisor.length} đơn cần duyệt\r\n              </span>\r\n            </div>\r\n            \r\n            {/* Danh sách đơn cần duyệt với infinite scroll */}\r\n            <div className=\"employee-list-simple\" id=\"approvalRequestsContainer\">\r\n              {displayedApprovalRequests.length === 0 ? (\r\n                <div className=\"no-employees\">\r\n                  <div className=\"no-employees-icon\">\r\n                    <i className=\"fas fa-calendar-check\"></i>\r\n                  </div>\r\n                  <p>Không có đơn xin nghỉ phép nào cần duyệt</p>\r\n                </div>\r\n              ) : (\r\n                displayedApprovalRequests.map((request, index) => (\r\n                  <div key={request._id} className=\"employee-card-simple\">\r\n                    <div className=\"employee-info-main\">\r\n                      <div className=\"employee-avatar\">\r\n                        {(request.employee?.fullName || request.employeeName || 'N/A').charAt(0).toUpperCase()}\r\n                      </div>\r\n                      <div className=\"employee-details\">\r\n                        <h4>{request.employee?.fullName || request.employeeName || 'Chưa xác định'}</h4>\r\n                        <p><i className=\"fas fa-calendar\"></i> {formatDate(request.createdAt)}</p>\r\n                        <p><i className=\"fas fa-clock\"></i> {formatLeaveTime(request.startDate, request.startPeriod, request.endDate, request.endPeriod)}</p>\r\n                        <p><strong>{formatDuration(calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod))}</strong></p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"employee-contact\">\r\n                      <p><i className=\"fas fa-comment\"></i> <strong>Lý do:</strong> {request.reason}</p>\r\n                    </div>\r\n\r\n                    <div className=\"employee-work-info\">\r\n                      <p>\r\n                        <i className=\"fas fa-info-circle\"></i>\r\n                        <strong>Trạng thái:</strong> {getStatusBadge(request.status)}\r\n                      </p>\r\n                      <p>\r\n                        <i className=\"fas fa-paperclip\"></i>\r\n                        <strong>File đính kèm:</strong> {getAttachmentBadge(request)}\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"employee-actions\">\r\n                      {request.status === 'pending' && (\r\n                        <button \r\n                          className=\"btn-view\"\r\n                          onClick={() => handleShowApprovalModal(request)}\r\n                          style={{ background: '#28a745', color: 'white' }}\r\n                        >\r\n                          <i className=\"fas fa-check-circle\"></i> Duyệt/Từ chối\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              )}\r\n              \r\n              {/* Loading more indicator */}\r\n              {isApprovalRequestsLoading && (\r\n                <div className=\"loading-more\">\r\n                  <div className=\"spinner\"></div>\r\n                  <span>Đang tải thêm...</span>\r\n                </div>\r\n              )}\r\n              \r\n              {/* End of results */}\r\n              {displayedApprovalRequests.length > 0 && displayedApprovalRequests.length >= leaveRequests.asSupervisor.length && (\r\n                <div className=\"end-of-results\">\r\n                  <p>Đã hiển thị tất cả {leaveRequests.asSupervisor.length} đơn cần duyệt</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </Tab>\r\n        \r\n        {/* Tab hiển thị cho SUPER_ADMIN và LEVEL_II_MANAGER */}\r\n        {user && (user.role === 'SUPER_ADMIN' || user.role === 'LEVEL_II_MANAGER') && (\r\n          <Tab eventKey=\"all-requests\" title=\"Tất cả đơn nghỉ phép\">\r\n            <div className=\"employee-table-container\">\r\n              {/* Leave Request Statistics Summary */}\r\n              <div className=\"leave-statistics-banner\" style={{\r\n                background: 'linear-gradient(135deg, #3498db, #2980b9)',\r\n                color: 'white',\r\n                padding: '20px',\r\n                borderRadius: '10px',\r\n                marginBottom: '20px',\r\n                boxShadow: '0 4px 15px rgba(0,0,0,0.1)'\r\n              }}>\r\n                <h3 style={{ margin: '0 0 15px 0', display: 'flex', alignItems: 'center' }}>\r\n                  <i className=\"fas fa-chart-bar\" style={{ marginRight: '10px' }}></i>\r\n                  Tóm tắt đơn nghỉ phép năm {new Date().getFullYear()}\r\n                </h3>\r\n\r\n                {(() => {\r\n                  const stats = calculateLeaveStatistics();\r\n                  const totalEmployees = stats.length;\r\n                  const totalRequests = stats.reduce((sum, emp) => sum + emp.totalRequests, 0);\r\n                  const totalDays = stats.reduce((sum, emp) => sum + emp.totalDays, 0);\r\n                  const employeesAtLimit = stats.filter(emp => checkAnnualLimit(emp)).length;\r\n                  const employeesAtMonthlyLimit = stats.filter(emp => checkMonthlyLimit(emp)).length;\r\n\r\n                  return (\r\n                    <div className=\"row\">\r\n                      <div className=\"col-md-3\">\r\n                        <div className=\"stat-card\" style={{ background: 'rgba(255,255,255,0.2)', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>\r\n                          <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalEmployees}</div>\r\n                          <div style={{ fontSize: '14px', opacity: '0.9' }}>Nhân viên có đơn</div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-md-3\">\r\n                        <div className=\"stat-card\" style={{ background: 'rgba(255,255,255,0.2)', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>\r\n                          <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalRequests}</div>\r\n                          <div style={{ fontSize: '14px', opacity: '0.9' }}>Tổng số đơn</div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-md-3\">\r\n                        <div className=\"stat-card\" style={{ background: 'rgba(255,255,255,0.2)', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>\r\n                          <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalDays}</div>\r\n                          <div style={{ fontSize: '14px', opacity: '0.9' }}>Tổng ngày nghỉ</div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-md-3\">\r\n                        <div className=\"stat-card\" style={{ background: 'rgba(255,255,255,0.2)', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>\r\n                          <div style={{ fontSize: '24px', fontWeight: 'bold', color: employeesAtLimit > 0 ? '#ffeb3b' : '#fff' }}>\r\n                            {employeesAtLimit}\r\n                          </div>\r\n                          <div style={{ fontSize: '14px', opacity: '0.9' }}>Đạt giới hạn năm</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })()}\r\n              </div>\r\n\r\n              {/* Employee Statistics Detail Table */}\r\n              {(() => {\r\n                const stats = calculateLeaveStatistics();\r\n                return stats.length > 0 && (\r\n                  <div className=\"employee-stats-section\" style={{ marginBottom: '20px' }}>\r\n                    <h4 style={{ marginBottom: '15px', color: '#333' }}>\r\n                      <i className=\"fas fa-users\" style={{ marginRight: '8px' }}></i>\r\n                      Chi tiết theo nhân viên\r\n                    </h4>\r\n                    <div className=\"table-responsive\">\r\n                      <table className=\"table table-striped table-hover\">\r\n                        <thead className=\"table-dark\">\r\n                          <tr>\r\n                            <th>Nhân viên</th>\r\n                            <th>Số đơn năm</th>\r\n                            <th>Tổng ngày nghỉ</th>\r\n                            <th>Đã duyệt</th>\r\n                            <th>Chờ duyệt</th>\r\n                            <th>Từ chối</th>\r\n                            <th>Trạng thái</th>\r\n                          </tr>\r\n                        </thead>\r\n                        <tbody>\r\n                          {stats.map((empStat, index) => (\r\n                            <tr key={index}>\r\n                              <td>\r\n                                <strong>{empStat.employeeName}</strong>\r\n                              </td>\r\n                              <td>\r\n                                <span className={`badge ${checkAnnualLimit(empStat) ? 'bg-danger' : empStat.totalRequests >= 8 ? 'bg-warning' : 'bg-success'}`}>\r\n                                  {empStat.totalRequests}/12\r\n                                </span>\r\n                              </td>\r\n                              <td>\r\n                                <strong>{empStat.totalDays}</strong> ngày\r\n                              </td>\r\n                              <td>\r\n                                <span className=\"badge bg-success\">{empStat.approvedRequests}</span>\r\n                              </td>\r\n                              <td>\r\n                                <span className=\"badge bg-warning\">{empStat.pendingRequests}</span>\r\n                              </td>\r\n                              <td>\r\n                                <span className=\"badge bg-danger\">{empStat.rejectedRequests}</span>\r\n                              </td>\r\n                              <td>\r\n                                {checkAnnualLimit(empStat) && (\r\n                                  <span className=\"badge bg-danger me-1\">\r\n                                    <i className=\"fas fa-exclamation-triangle\"></i> Đạt giới hạn năm\r\n                                  </span>\r\n                                )}\r\n                                {checkMonthlyLimit(empStat) && (\r\n                                  <span className=\"badge bg-warning\">\r\n                                    <i className=\"fas fa-calendar-times\"></i> Đạt giới hạn tháng\r\n                                  </span>\r\n                                )}\r\n                                {!checkAnnualLimit(empStat) && !checkMonthlyLimit(empStat) && (\r\n                                  <span className=\"badge bg-success\">\r\n                                    <i className=\"fas fa-check\"></i> Bình thường\r\n                                  </span>\r\n                                )}\r\n                              </td>\r\n                            </tr>\r\n                          ))}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })()}\r\n\r\n              {/* Debug info */}\r\n              {leaveRequests.allRequests.length === 0 && (\r\n                <div className=\"alert alert-warning\">\r\n                  <strong>Thông báo:</strong> Không tìm thấy đơn nghỉ phép nào. Vui lòng kiểm tra quyền truy cập của bạn.\r\n                </div>\r\n              )}\r\n              \r\n              <div className=\"table-filters\">\r\n                <h3>Lọc dữ liệu</h3>\r\n                <div className=\"filter-row\">\r\n                  <div className=\"filter-group\">\r\n                    <label>Nhân viên:</label>\r\n                    <input \r\n                      type=\"text\" \r\n                      name=\"employeeName\" \r\n                      value={tableFilters.employeeName || ''} \r\n                      onChange={handleTableFilterChange}\r\n                      placeholder=\"Nhập tên nhân viên...\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"filter-group\">\r\n                    <label>Quản lý:</label>\r\n                    <input \r\n                      type=\"text\" \r\n                      name=\"supervisorName\" \r\n                      value={tableFilters.supervisorName || ''} \r\n                      onChange={handleTableFilterChange}\r\n                      placeholder=\"Nhập tên quản lý...\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"filter-group\">\r\n                    <label>Trạng thái:</label>\r\n                    <select name=\"status\" value={tableFilters.status} onChange={handleTableFilterChange}>\r\n                      <option value=\"\">Tất cả trạng thái</option>\r\n                      <option value=\"pending\">Đang chờ duyệt</option>\r\n                      <option value=\"approved\">Đã duyệt</option>\r\n                      <option value=\"rejected\">Đã từ chối</option>\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n                <div className=\"filter-actions\">\r\n                  <button \r\n                    className=\"clear-filters-btn\"\r\n                    onClick={resetTableFilters}\r\n                  >\r\n                    Xóa bộ lọc\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"table-summary\">\r\n                <p>Tổng số đơn: <strong>{filterTableRecords(tableFilters).length}</strong> / {leaveRequests.allRequests.length}</p>\r\n                \r\n                {tableFilters.status && (\r\n                  <p>Trạng thái <strong>{getStatusBadge(tableFilters.status).props.children}</strong>: {filterTableRecords(tableFilters).length} đơn</p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Danh sách tất cả đơn nghỉ phép với infinite scroll */}\r\n              <div className=\"employee-list-simple\" id=\"allRequestsContainer\">\r\n                {displayedAllRequests.length === 0 ? (\r\n                  <div className=\"no-employees\">\r\n                    <div className=\"no-employees-icon\">\r\n                      <i className=\"fas fa-calendar-times\"></i>\r\n                    </div>\r\n                    <p>Không tìm thấy đơn nghỉ phép nào phù hợp với bộ lọc</p>\r\n                  </div>\r\n                ) : (\r\n                  displayedAllRequests.map((request, index) => (\r\n                    <div key={request._id} className=\"employee-card-simple\">\r\n                      <div className=\"employee-info-main\">\r\n                        <div className=\"employee-avatar\">\r\n                          {(request.employee?.fullName || 'N/A').charAt(0).toUpperCase()}\r\n                        </div>\r\n                        <div className=\"employee-details\">\r\n                          <h4>{request.employee?.fullName || 'Chưa xác định'}</h4>\r\n                          <p><i className=\"fas fa-calendar\"></i> {formatDate(request.createdAt)}</p>\r\n                          <p><i className=\"fas fa-clock\"></i> {formatLeaveTime(request.startDate, request.startPeriod, request.endDate, request.endPeriod)}</p>\r\n                          <p><strong>{formatDuration(calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod))}</strong></p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"employee-contact\">\r\n                        <p><i className=\"fas fa-user-tie\"></i> <strong>Quản lý:</strong> {request.supervisor?.fullName || 'Chưa xác định'}</p>\r\n                        <p><i className=\"fas fa-comment\"></i> <strong>Lý do:</strong> {request.reason}</p>\r\n                        {request.comments && (\r\n                          <p><i className=\"fas fa-sticky-note\"></i> <strong>Nhận xét:</strong> {request.comments}</p>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"employee-work-info\">\r\n                        <p>\r\n                          <i className=\"fas fa-info-circle\"></i>\r\n                          <strong>Trạng thái:</strong> {getStatusBadge(request.status)}\r\n                        </p>\r\n                        <p>\r\n                          <i className=\"fas fa-paperclip\"></i>\r\n                          <strong>File đính kèm:</strong> {getAttachmentBadge(request)}\r\n                        </p>\r\n                      </div>\r\n\r\n                      <div className=\"employee-actions\">\r\n                        {/* SUPER_ADMIN có thể chỉnh sửa/xóa tất cả đơn */}\r\n                        {/* Người nộp đơn chỉ có thể chỉnh sửa/xóa đơn của họ khi đơn còn ở trạng thái pending */}\r\n                        {/* LEVEL_II_MANAGER chỉ có thể xem, không thể chỉnh sửa/xóa */}\r\n                        {(user.role === 'SUPER_ADMIN' || \r\n                          (request.employee?._id === user._id && request.status === 'pending')) && \r\n                          user.role !== 'LEVEL_II_MANAGER' && (\r\n                          <>\r\n                            <button \r\n                              className=\"btn-view\"\r\n                              onClick={() => handleShowEditModal(request)}\r\n                              title=\"Chỉnh sửa đơn\"\r\n                            >\r\n                              <i className=\"fas fa-edit\"></i> Chỉnh sửa\r\n                            </button>\r\n                            <button \r\n                              className=\"btn-delete\"\r\n                              onClick={() => handleShowDeleteModal(request)}\r\n                              title=\"Xóa đơn\"\r\n                              style={{ background: '#dc3545', color: 'white' }}\r\n                            >\r\n                              <i className=\"fas fa-trash-alt\"></i> Xóa\r\n                            </button>\r\n                          </>\r\n                        )}\r\n                        {/* Hiển thị thông báo \"Chỉ xem\" cho LEVEL_II_MANAGER */}\r\n                        {user.role === 'LEVEL_II_MANAGER' && (\r\n                          <span className=\"text-muted fst-italic\" style={{ padding: '6px 12px', background: '#f8f9fa', borderRadius: '4px' }}>\r\n                            <i className=\"fas fa-eye\"></i> Chỉ xem\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                )}\r\n                \r\n                {/* Loading more indicator */}\r\n                {isAllRequestsLoading && (\r\n                  <div className=\"loading-more\">\r\n                    <div className=\"spinner\"></div>\r\n                    <span>Đang tải thêm...</span>\r\n                  </div>\r\n                )}\r\n                \r\n                {/* End of results */}\r\n                {displayedAllRequests.length > 0 && displayedAllRequests.length >= filterTableRecords(tableFilters).length && (\r\n                  <div className=\"end-of-results\">\r\n                    <p>Đã hiển thị tất cả {filterTableRecords(tableFilters).length} đơn nghỉ phép</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </Tab>\r\n        )}\r\n      </Tabs>\r\n\r\n      {/* Thông báo thành công */}\r\n      {showSuccessAlert && (\r\n        <div className=\"success-alert\">\r\n          <div className=\"success-alert-content\">\r\n            <i className=\"fas fa-check-circle\"></i>\r\n            <span>{successMessage}</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Modal duyệt đơn */}\r\n      <Modal show={showModal} onHide={handleCloseModal} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Duyệt đơn xin nghỉ phép</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {selectedRequest && (\r\n            <>\r\n              <p>\r\n                <strong>Nhân viên:</strong> {selectedRequest.employee?.fullName || 'Không xác định'}\r\n              </p>\r\n              <p>\r\n                <strong>Thời gian nghỉ:</strong> {formatLeaveTime(selectedRequest.startDate, selectedRequest.startPeriod, selectedRequest.endDate, selectedRequest.endPeriod)}\r\n              </p>\r\n              <p>\r\n                <strong>Tổng ngày nghỉ:</strong> {formatDuration(calculateLeaveDuration(selectedRequest.startDate, selectedRequest.startPeriod, selectedRequest.endDate, selectedRequest.endPeriod))}\r\n              </p>\r\n              <p>\r\n                <strong>Lý do:</strong> {selectedRequest.reason}\r\n              </p>\r\n              \r\n              {/* Hiển thị file đính kèm */}\r\n              <div className=\"mt-4 mb-4\">\r\n                <h5>File đính kèm</h5>\r\n                {loadingAttachments ? (\r\n                  <div className=\"text-center\">\r\n                    <Spinner animation=\"border\" size=\"sm\" /> Đang tải...\r\n                  </div>\r\n                ) : attachmentError ? (\r\n                  <Alert variant=\"danger\">{attachmentError}</Alert>\r\n                ) : attachments.length === 0 ? (\r\n                  <p className=\"text-muted\">Không có file đính kèm</p>\r\n                ) : (\r\n                  <ListGroup>\r\n                    {attachments.map(file => (\r\n                      <ListGroup.Item key={file._id} className=\"d-flex justify-content-between align-items-center\">\r\n                        <div className=\"d-flex align-items-center\">\r\n                          <span className=\"me-2\">{getFileIcon(file.fileType)}</span>\r\n                          <span>{file.originalName}</span>\r\n                          <span className=\"text-muted ms-2\">({(file.fileSize / 1024 / 1024).toFixed(2)} MB)</span>\r\n                        </div>\r\n                        <div>\r\n                          <Button \r\n                            variant=\"outline-primary\" \r\n                            size=\"sm\"\r\n                            className=\"me-2\"\r\n                            onClick={() => handleDownloadFile(file._id)}\r\n                          >\r\n                            <FaDownload />\r\n                          </Button>\r\n                          {(user.role === 'SUPER_ADMIN' || user._id === file.uploadedBy) && (\r\n                            <Button \r\n                              variant=\"outline-danger\" \r\n                              size=\"sm\"\r\n                              onClick={() => handleDeleteFile(file._id)}\r\n                            >\r\n                              <FaTrashAlt />\r\n                            </Button>\r\n                          )}\r\n                        </div>\r\n                      </ListGroup.Item>\r\n                    ))}\r\n                  </ListGroup>\r\n                )}\r\n              </div>\r\n\r\n              <Form onSubmit={handleSubmitApproval}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Quyết định</Form.Label>\r\n                  <Form.Select\r\n                    name=\"status\"\r\n                    value={approvalData.status}\r\n                    onChange={handleApprovalChange}\r\n                  >\r\n                    <option value=\"approved\">Duyệt đơn</option>\r\n                    <option value=\"rejected\">Từ chối đơn</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Nhận xét (không bắt buộc)</Form.Label>\r\n                  <Form.Control\r\n                    as=\"textarea\"\r\n                    rows={3}\r\n                    name=\"comments\"\r\n                    value={approvalData.comments}\r\n                    onChange={handleApprovalChange}\r\n                    placeholder=\"Nhập nhận xét của bạn\"\r\n                  />\r\n                </Form.Group>\r\n\r\n                <div className=\"d-flex justify-content-end\">\r\n                  <Button variant=\"secondary\" className=\"me-2\" onClick={handleCloseModal}>\r\n                    Hủy\r\n                  </Button>\r\n                  <Button variant=\"primary\" type=\"submit\">\r\n                    Xác nhận\r\n                  </Button>\r\n                </div>\r\n              </Form>\r\n            </>\r\n          )}\r\n        </Modal.Body>\r\n      </Modal>\r\n\r\n      {/* Modal chỉnh sửa đơn */}\r\n      <Modal show={showEditModal} onHide={handleCloseEditModal} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Chỉnh sửa đơn xin nghỉ phép</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {selectedRequest && (\r\n            <Form onSubmit={handleSubmitEdit}>\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Nhân viên</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  value={selectedRequest.employee?.fullName || 'Không xác định'}\r\n                  disabled\r\n                />\r\n              </Form.Group>\r\n              \r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Cấp trên</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  value={selectedRequest.supervisor?.fullName || 'Không xác định'}\r\n                  disabled\r\n                />\r\n              </Form.Group>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Ngày bắt đầu</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      name=\"startDate\"\r\n                      value={editData.startDate}\r\n                      onChange={handleEditChange}\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Thời gian bắt đầu</Form.Label>\r\n                    <Form.Select \r\n                      name=\"startPeriod\"\r\n                      value={editData.startPeriod || 'full_day'}\r\n                      onChange={(e) => {\r\n                        const newStartPeriod = e.target.value;\r\n                        \r\n                        // Cập nhật thời gian bắt đầu\r\n                        handleEditChange({\r\n                          target: { name: 'startPeriod', value: newStartPeriod }\r\n                        });\r\n                        \r\n                        // Kiểm tra nếu ngày bắt đầu và kết thúc là cùng một ngày\r\n                        const isSameDay = new Date(editData.startDate).setHours(0,0,0,0) === \r\n                                         new Date(editData.endDate).setHours(0,0,0,0);\r\n                        \r\n                        // Nếu cùng một ngày, áp dụng các quy tắc thời gian kết thúc\r\n                        if (isSameDay) {\r\n                          if (newStartPeriod === 'full_day') {\r\n                            // Nếu chọn cả ngày, tự động đặt thời gian kết thúc cũng là cả ngày\r\n                            handleEditChange({\r\n                              target: { name: 'endPeriod', value: 'full_day' }\r\n                            });\r\n                          } else if (newStartPeriod === 'afternoon') {\r\n                            // Nếu chọn buổi chiều, thời gian kết thúc chỉ có thể là buổi chiều\r\n                            handleEditChange({\r\n                              target: { name: 'endPeriod', value: 'afternoon' }\r\n                            });\r\n                          } else if (newStartPeriod === 'morning' && editData.endPeriod === 'full_day') {\r\n                            // Nếu chọn buổi sáng và thời gian kết thúc đang là cả ngày, đổi thành buổi chiều\r\n                            handleEditChange({\r\n                              target: { name: 'endPeriod', value: 'afternoon' }\r\n                            });\r\n                          }\r\n                        }\r\n                      }}\r\n                    >\r\n                      <option value=\"full_day\">Cả ngày</option>\r\n                      <option value=\"morning\">Buổi sáng</option>\r\n                      <option value=\"afternoon\">Buổi chiều</option>\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Ngày kết thúc</Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      name=\"endDate\"\r\n                      value={editData.endDate}\r\n                      onChange={handleEditChange}\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Thời gian kết thúc</Form.Label>\r\n                    <Form.Select \r\n                      name=\"endPeriod\"\r\n                      value={editData.endPeriod || 'full_day'}\r\n                      onChange={handleEditChange}\r\n                      disabled={\r\n                        // Chỉ disable khi cùng một ngày và thời gian bắt đầu là cả ngày hoặc buổi chiều\r\n                        new Date(editData.startDate).setHours(0,0,0,0) === new Date(editData.endDate).setHours(0,0,0,0) && \r\n                        (editData.startPeriod === 'full_day' || editData.startPeriod === 'afternoon')\r\n                      }\r\n                    >\r\n                      {/* Nếu ngày bắt đầu và kết thúc khác nhau, hiển thị tất cả các tùy chọn */}\r\n                      {new Date(editData.startDate).setHours(0,0,0,0) !== new Date(editData.endDate).setHours(0,0,0,0) ? (\r\n                        <>\r\n                          <option value=\"full_day\">Cả ngày</option>\r\n                          <option value=\"morning\">Buổi sáng</option>\r\n                          <option value=\"afternoon\">Buổi chiều</option>\r\n                        </>\r\n                      ) : (\r\n                        /* Nếu cùng một ngày, hiển thị tùy chọn dựa trên thời gian bắt đầu */\r\n                        <>\r\n                          {editData.startPeriod === 'full_day' && (\r\n                            <option value=\"full_day\">Cả ngày</option>\r\n                          )}\r\n                          \r\n                          {editData.startPeriod === 'morning' && (\r\n                            <>\r\n                              <option value=\"morning\">Buổi sáng</option>\r\n                              <option value=\"afternoon\">Buổi chiều</option>\r\n                            </>\r\n                          )}\r\n                          \r\n                          {editData.startPeriod === 'afternoon' && (\r\n                            <option value=\"afternoon\">Buổi chiều</option>\r\n                          )}\r\n                        </>\r\n                      )}\r\n                    </Form.Select>\r\n                    <Form.Text className=\"text-muted\">\r\n                      {new Date(editData.startDate).setHours(0,0,0,0) === new Date(editData.endDate).setHours(0,0,0,0) ? (\r\n                        // Nếu cùng một ngày\r\n                        editData.startPeriod === 'full_day' \r\n                          ? 'Khi chọn thời gian bắt đầu là cả ngày, thời gian kết thúc cũng sẽ là cả ngày'\r\n                          : editData.startPeriod === 'afternoon'\r\n                            ? 'Khi chọn thời gian bắt đầu là buổi chiều, thời gian kết thúc cũng sẽ là buổi chiều'\r\n                            : 'Sáng: 8:00 - 12:00, Chiều: 13:30 - 17:30'\r\n                      ) : (\r\n                        // Nếu khác ngày\r\n                        'Sáng: 8:00 - 12:00, Chiều: 13:30 - 17:30, Cả ngày: 8:00 - 17:30'\r\n                      )}\r\n                    </Form.Text>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Lý do</Form.Label>\r\n                <Form.Control\r\n                  as=\"textarea\"\r\n                  rows={3}\r\n                  name=\"reason\"\r\n                  value={editData.reason}\r\n                  onChange={handleEditChange}\r\n                  placeholder=\"Nhập lý do nghỉ phép\"\r\n                  required\r\n                />\r\n              </Form.Group>\r\n              \r\n              {/* Hiển thị file đính kèm */}\r\n              <div className=\"mt-4 mb-4\">\r\n                <h5>File đính kèm</h5>\r\n                {loadingAttachments ? (\r\n                  <div className=\"text-center\">\r\n                    <Spinner animation=\"border\" size=\"sm\" /> Đang tải...\r\n                  </div>\r\n                ) : attachmentError ? (\r\n                  <Alert variant=\"danger\">{attachmentError}</Alert>\r\n                ) : (\r\n                  <>\r\n                    {/* Danh sách file hiện có */}\r\n                    {attachments.length === 0 ? (\r\n                      <p className=\"text-muted\">Không có file đính kèm</p>\r\n                    ) : (\r\n                      <ListGroup className=\"mb-3\">\r\n                        {attachments.map(file => (\r\n                          <ListGroup.Item key={file._id} className=\"d-flex justify-content-between align-items-center\">\r\n                            <div className=\"d-flex align-items-center\">\r\n                              <span className=\"me-2\">{getFileIcon(file.fileType)}</span>\r\n                              <span>{file.originalName}</span>\r\n                              <span className=\"text-muted ms-2\">({(file.fileSize / 1024 / 1024).toFixed(2)} MB)</span>\r\n                            </div>\r\n                            <div>\r\n                              <Button \r\n                                variant=\"outline-primary\" \r\n                                size=\"sm\"\r\n                                className=\"me-2\"\r\n                                onClick={() => handleDownloadFile(file._id)}\r\n                              >\r\n                                <FaDownload />\r\n                              </Button>\r\n                              {(user.role === 'SUPER_ADMIN' || user._id === file.uploadedBy) && (\r\n                                <Button \r\n                                  variant=\"outline-danger\" \r\n                                  size=\"sm\"\r\n                                  onClick={() => handleDeleteFile(file._id)}\r\n                                >\r\n                                  <FaTrashAlt />\r\n                                </Button>\r\n                              )}\r\n                            </div>\r\n                          </ListGroup.Item>\r\n                        ))}\r\n                      </ListGroup>\r\n                    )}\r\n                    \r\n                    {/* Form upload file mới */}\r\n                    <div className=\"mt-3\">\r\n                      <div className=\"d-flex align-items-center mb-2\">\r\n                        <Button \r\n                          variant=\"outline-primary\" \r\n                          onClick={() => fileInputRef.current.click()}\r\n                          className=\"d-flex align-items-center\"\r\n                          disabled={uploadingFiles}\r\n                        >\r\n                          <FaFileUpload className=\"me-2\" /> Chọn file\r\n                        </Button>\r\n                        <Form.Text className=\"text-muted ms-3\">\r\n                          Hỗ trợ PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF, TXT (tối đa 5 file, mỗi file không quá 10MB)\r\n                        </Form.Text>\r\n                        <input\r\n                          type=\"file\"\r\n                          ref={fileInputRef}\r\n                          onChange={handleFileChange}\r\n                          style={{ display: 'none' }}\r\n                          multiple\r\n                        />\r\n                      </div>\r\n                      \r\n                      {uploadError && (\r\n                        <Alert variant=\"danger\" className=\"mt-2 mb-2\">\r\n                          {uploadError}\r\n                        </Alert>\r\n                      )}\r\n                      \r\n                      {selectedFiles.length > 0 && (\r\n                        <>\r\n                          <ListGroup className=\"mt-2\">\r\n                            {selectedFiles.map((file, index) => (\r\n                              <ListGroup.Item key={index} className=\"d-flex justify-content-between align-items-center\">\r\n                                <div className=\"d-flex align-items-center\">\r\n                                  <span className=\"me-2\">{getFileIcon(file.type)}</span>\r\n                                  <span>{file.name}</span>\r\n                                  <span className=\"text-muted ms-2\">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>\r\n                                </div>\r\n                                <Button \r\n                                  variant=\"outline-danger\" \r\n                                  size=\"sm\"\r\n                                  onClick={() => handleRemoveFile(index)}\r\n                                  disabled={uploadingFiles}\r\n                                >\r\n                                  <FaTrashAlt />\r\n                                </Button>\r\n                              </ListGroup.Item>\r\n                            ))}\r\n                          </ListGroup>\r\n                          \r\n                          <div className=\"d-flex justify-content-end mt-2\">\r\n                            <Button \r\n                              variant=\"success\" \r\n                              size=\"sm\"\r\n                              onClick={() => uploadFiles(selectedRequest._id)}\r\n                              disabled={uploadingFiles}\r\n                            >\r\n                              {uploadingFiles ? (\r\n                                <>\r\n                                  <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                                  Đang tải lên...\r\n                                </>\r\n                              ) : (\r\n                                'Tải lên'\r\n                              )}\r\n                            </Button>\r\n                          </div>\r\n                        </>\r\n                      )}\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </div>\r\n\r\n              {/* Chỉ hiển thị trường trạng thái cho admin */}\r\n              {user && user.role === 'SUPER_ADMIN' ? (\r\n                <>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Trạng thái</Form.Label>\r\n                    <Form.Select\r\n                      name=\"status\"\r\n                      value={editData.status}\r\n                      onChange={handleEditChange}\r\n                    >\r\n                      <option value=\"pending\">Chờ duyệt</option>\r\n                      <option value=\"approved\">Đã duyệt</option>\r\n                      <option value=\"rejected\">Đã từ chối</option>\r\n                      <option value=\"cancelled\">Đã hủy</option>\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                  \r\n                  {/* Phần thiết lập quyền xem đơn - chỉ dành cho SUPER_ADMIN */}\r\n                  <hr />\r\n                  <h5>Thiết lập quyền xem đơn</h5>\r\n                  \r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Chế độ hiển thị</Form.Label>\r\n                    <Form.Select\r\n                      name=\"visibility\"\r\n                      value={editData.visibility || 'all'}\r\n                      onChange={handleEditChange}\r\n                    >\r\n                      <option value=\"all\">Tất cả (mặc định)</option>\r\n                      <option value=\"restricted\">Hạn chế</option>\r\n                    </Form.Select>\r\n                    <Form.Text className=\"text-muted\">\r\n                      Chế độ \"Tất cả\" cho phép mọi người có quyền xem đơn nghỉ phép. Chế độ \"Hạn chế\" chỉ cho phép những người được chỉ định xem đơn.\r\n                    </Form.Text>\r\n                  </Form.Group>\r\n                  \r\n                  {editData.visibility === 'restricted' && (\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label>Người dùng được phép xem</Form.Label>\r\n                      <Form.Control\r\n                        as=\"select\"\r\n                        multiple\r\n                        value={editData.visibleTo || []}\r\n                        onChange={(e) => {\r\n                          const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);\r\n                          setEditData({\r\n                            ...editData,\r\n                            visibleTo: selectedOptions\r\n                          });\r\n                        }}\r\n                        style={{ height: '150px' }}\r\n                      >\r\n                        {Array.isArray(allUsers) ? allUsers\r\n                          .filter(user => ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'SUPER_ADMIN'].includes(user.role))\r\n                          .map(user => (\r\n                            <option key={user._id} value={user._id}>\r\n                              {user.fullName || user.name || user.email}\r\n                            </option>\r\n                          )) : []}\r\n                      </Form.Control>\r\n                      <Form.Text className=\"text-muted\">\r\n                        Chỉ quản lý và admin được hiển thị trong danh sách này. Nhân viên thường không được thêm vào.\r\n                        Giữ Ctrl (hoặc Cmd trên Mac) để chọn nhiều người dùng.\r\n                      </Form.Text>\r\n                    </Form.Group>\r\n                  )}\r\n                  \r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Ẩn đơn khỏi người dùng</Form.Label>\r\n                    <Form.Control\r\n                      as=\"select\"\r\n                      multiple\r\n                      value={editData.hiddenFrom || []}\r\n                      onChange={(e) => {\r\n                        const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);\r\n                        setEditData({\r\n                          ...editData,\r\n                          hiddenFrom: selectedOptions\r\n                        });\r\n                      }}\r\n                      style={{ height: '150px' }}\r\n                    >\r\n                      {Array.isArray(allUsers) ? allUsers\r\n                        .filter(user => ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'SUPER_ADMIN'].includes(user.role))\r\n                        .map(user => (\r\n                          <option key={user._id} value={user._id}>\r\n                            {user.fullName || user.name || user.email}\r\n                          </option>\r\n                        )) : []}\r\n                    </Form.Control>\r\n                    <Form.Text className=\"text-muted\">\r\n                      Những quản lý/admin được chọn sẽ không thể xem đơn này, ngay cả khi họ có quyền quản lý đơn nghỉ phép.\r\n                      Chỉ quản lý và admin được hiển thị trong danh sách này. Nhân viên thường không được thêm vào.\r\n                      Giữ Ctrl (hoặc Cmd trên Mac) để chọn nhiều người dùng.\r\n                    </Form.Text>\r\n                  </Form.Group>\r\n                </>\r\n              ) : (\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Trạng thái</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    value={getStatusBadge(editData.status).props.children}\r\n                    disabled\r\n                  />\r\n                  <Form.Text className=\"text-muted\">\r\n                    Chỉ quản trị viên mới có thể thay đổi trạng thái đơn\r\n                  </Form.Text>\r\n                </Form.Group>\r\n              )}\r\n\r\n              <div className=\"d-flex justify-content-end\">\r\n                <Button variant=\"secondary\" className=\"me-2\" onClick={handleCloseEditModal}>\r\n                  Hủy\r\n                </Button>\r\n                <Button variant=\"primary\" type=\"submit\">\r\n                  Lưu thay đổi\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          )}\r\n        </Modal.Body>\r\n      </Modal>\r\n\r\n      {/* Modal xác nhận xóa */}\r\n      <Modal show={showDeleteModal} onHide={handleCloseDeleteModal}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Xác nhận xóa</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {requestToDelete && (\r\n            <>\r\n              <p>Bạn có chắc chắn muốn xóa đơn xin nghỉ phép này?</p>\r\n              <p>\r\n                <strong>Nhân viên:</strong> {requestToDelete.employee?.fullName || 'Không xác định'}\r\n              </p>\r\n              <p>\r\n                <strong>Thời gian nghỉ:</strong> {formatLeaveTime(requestToDelete.startDate, requestToDelete.startPeriod, requestToDelete.endDate, requestToDelete.endPeriod)}\r\n              </p>\r\n              <p>\r\n                <strong>Tổng ngày nghỉ:</strong> {formatDuration(calculateLeaveDuration(requestToDelete.startDate, requestToDelete.startPeriod, requestToDelete.endDate, requestToDelete.endPeriod))}\r\n              </p>\r\n              <p>\r\n                <strong>Lý do:</strong> {requestToDelete.reason}\r\n              </p>\r\n              <p>\r\n                <strong>Trạng thái:</strong> {getStatusBadge(requestToDelete.status)}\r\n              </p>\r\n              <div className=\"alert alert-warning\">\r\n                <i className=\"fas fa-exclamation-triangle me-2\"></i>\r\n                Hành động này không thể hoàn tác. Đơn xin nghỉ phép sẽ bị xóa vĩnh viễn.\r\n              </div>\r\n            </>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={handleCloseDeleteModal}>\r\n            Hủy\r\n          </Button>\r\n          <Button variant=\"danger\" onClick={handleDeleteRequest}>\r\n            Xóa\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Modal xem file đính kèm */}\r\n      <Modal show={showAttachmentModal} onHide={handleCloseAttachmentModal} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Xem file đính kèm</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {selectedRequest && (\r\n            <div>\r\n              <div className=\"mb-4\">\r\n                <h5>Thông tin đơn nghỉ phép</h5>\r\n                <Table bordered>\r\n                  <tbody>\r\n                    <tr>\r\n                      <td width=\"30%\"><strong>Thời gian nghỉ</strong></td>\r\n                      <td>{formatLeaveTime(selectedRequest.startDate, selectedRequest.startPeriod, selectedRequest.endDate, selectedRequest.endPeriod)}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td><strong>Lý do</strong></td>\r\n                      <td>{selectedRequest.reason}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td><strong>Trạng thái</strong></td>\r\n                      <td>{getStatusBadge(selectedRequest.status)}</td>\r\n                    </tr>\r\n                  </tbody>\r\n                </Table>\r\n              </div>\r\n\r\n              <div>\r\n                <h5>File đính kèm</h5>\r\n                {loadingAttachments ? (\r\n                  <div className=\"text-center py-3\">\r\n                    <Spinner animation=\"border\" variant=\"primary\" />\r\n                    <p className=\"mt-2\">Đang tải danh sách file...</p>\r\n                  </div>\r\n                ) : attachmentError ? (\r\n                  <Alert variant=\"danger\">{attachmentError}</Alert>\r\n                ) : attachments.length === 0 ? (\r\n                  <Alert variant=\"info\">Không có file đính kèm nào</Alert>\r\n                ) : (\r\n                  <ListGroup>\r\n                    {attachments.map(file => (\r\n                      <ListGroup.Item key={file._id} className=\"d-flex justify-content-between align-items-center\">\r\n                        <div className=\"d-flex align-items-center\">\r\n                          <span className=\"me-2\">{getFileIcon(file.fileType)}</span>\r\n                          <span>{file.originalName}</span>\r\n                          <span className=\"text-muted ms-2\">({(file.fileSize / 1024 / 1024).toFixed(2)} MB)</span>\r\n                          <span className=\"text-muted ms-2\">- Tải lên bởi {file.uploadedBy?.fullName || 'Không xác định'}</span>\r\n                        </div>\r\n                        <div>\r\n                          <Button \r\n                            variant=\"outline-primary\" \r\n                            size=\"sm\"\r\n                            className=\"me-2\"\r\n                            onClick={() => handleDownloadFile(file._id)}\r\n                          >\r\n                            <FaDownload />\r\n                          </Button>\r\n                          {(user.role === 'SUPER_ADMIN' || user._id === file.uploadedBy) && (\r\n                            <Button \r\n                              variant=\"outline-danger\" \r\n                              size=\"sm\"\r\n                              onClick={() => handleDeleteFile(file._id)}\r\n                            >\r\n                              <FaTrashAlt />\r\n                            </Button>\r\n                          )}\r\n                        </div>\r\n                      </ListGroup.Item>\r\n                    ))}\r\n                  </ListGroup>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={handleCloseAttachmentModal}>\r\n            Đóng\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default LeaveRequests;\r\n\r\n// Additional CSS styles for leave statistics - inject into document head\r\nif (typeof document !== 'undefined') {\r\n  const additionalStyles = `\r\n    .leave-statistics-banner .stat-card {\r\n      transition: transform 0.2s ease, box-shadow 0.2s ease;\r\n    }\r\n\r\n    .leave-statistics-banner .stat-card:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 6px 20px rgba(0,0,0,0.15);\r\n    }\r\n\r\n    .employee-stats-section .table th {\r\n      border-top: none;\r\n      font-weight: 600;\r\n      font-size: 14px;\r\n      background-color: #343a40;\r\n      color: white;\r\n    }\r\n\r\n    .employee-stats-section .table td {\r\n      vertical-align: middle;\r\n      font-size: 13px;\r\n      padding: 12px 8px;\r\n    }\r\n\r\n    .employee-stats-section .badge {\r\n      font-size: 11px;\r\n      padding: 4px 8px;\r\n      margin-right: 4px;\r\n    }\r\n\r\n    .leave-statistics-banner h3 {\r\n      text-shadow: 0 1px 3px rgba(0,0,0,0.3);\r\n      font-weight: 600;\r\n    }\r\n\r\n    .leave-statistics-banner .stat-card div:first-child {\r\n      font-family: 'Arial', sans-serif;\r\n      font-weight: 700;\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      .leave-statistics-banner .row > div {\r\n        margin-bottom: 15px;\r\n      }\r\n\r\n      .employee-stats-section {\r\n        overflow-x: auto;\r\n      }\r\n\r\n      .leave-statistics-banner {\r\n        padding: 15px;\r\n      }\r\n\r\n      .leave-statistics-banner h3 {\r\n        font-size: 18px;\r\n      }\r\n    }\r\n  `;\r\n\r\n  // Check if styles already exist to avoid duplicates\r\n  if (!document.querySelector('#leave-stats-styles')) {\r\n    const styleSheet = document.createElement('style');\r\n    styleSheet.id = 'leave-stats-styles';\r\n    styleSheet.type = 'text/css';\r\n    styleSheet.innerText = additionalStyles;\r\n    document.head.appendChild(styleSheet);\r\n  }\r\n}"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAAEC,cAAc,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AAC7J,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAO,yBAAyB;AAChC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,OAAO,yBAAyB;AAChC,OAAO,4BAA4B;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;;AAE9H;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,sBAAsB,GAAGA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,KAAK;EAC7E;EACA,IAAI,CAACH,SAAS,IAAI,CAACE,OAAO,EAAE;IAC1B,OAAO,CAAC;EACV;;EAEA;EACA,MAAME,iBAAiB,GAAGH,WAAW,IAAI,UAAU;EACnD,MAAMI,eAAe,GAAGF,SAAS,IAAI,UAAU;;EAE/C;EACA,MAAMG,KAAK,GAAG,IAAIC,IAAI,CAACP,SAAS,CAAC;EACjC,MAAMQ,GAAG,GAAG,IAAID,IAAI,CAACL,OAAO,CAAC;;EAE7B;EACAI,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BD,GAAG,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;EAExB;EACA,MAAMC,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOA,IAAI,CAACC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIF,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAACd,SAAS,EAAEE,OAAO,KAAK;IAC5C,IAAIa,KAAK,GAAG,CAAC;IACb,MAAMC,WAAW,GAAG,IAAIT,IAAI,CAACP,SAAS,CAAC;;IAEvC;IACA,OAAOgB,WAAW,IAAId,OAAO,EAAE;MAC7B;MACA,IAAI,CAACQ,QAAQ,CAACM,WAAW,CAAC,EAAE;QAC1B;QACA,IAAIH,UAAU,CAACG,WAAW,CAAC,EAAE;UAC3BD,KAAK,IAAI,GAAG;QACd,CAAC,MAAM;UACLA,KAAK,IAAI,GAAG;QACd;MACF;MACA;MACAC,WAAW,CAACC,OAAO,CAACD,WAAW,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAChD;IAEA,OAAOH,KAAK;EACd,CAAC;;EAED;EACA,IAAIT,KAAK,CAACa,OAAO,CAAC,CAAC,KAAKX,GAAG,CAACW,OAAO,CAAC,CAAC,EAAE;IACrC;IACA,IAAIT,QAAQ,CAACJ,KAAK,CAAC,EAAE;MACnB,OAAO,CAAC;IACV;;IAEA;IACA,IAAIO,UAAU,CAACP,KAAK,CAAC,EAAE;MACrB;MACA,OAAO,GAAG;IACZ;IAEA,IAAIF,iBAAiB,KAAK,UAAU,IAAIC,eAAe,KAAK,UAAU,EAAE;MACtE,OAAO,CAAC;IACV,CAAC,MAAM,IAAID,iBAAiB,KAAKC,eAAe,IAAID,iBAAiB,KAAK,UAAU,EAAE;MACpF,OAAO,GAAG;IACZ,CAAC,MAAM,IAAIA,iBAAiB,KAAK,SAAS,IAAIC,eAAe,KAAK,WAAW,EAAE;MAC7E,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,GAAG;IACZ;EACF,CAAC,MAAM;IACL;IACA,MAAMe,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACd,GAAG,GAAGF,KAAK,CAAC;IACtC,MAAMiB,QAAQ,GAAGF,IAAI,CAACG,KAAK,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;;IAE7D;IACA,MAAMK,aAAa,GAAGf,QAAQ,CAACJ,KAAK,CAAC;IACrC,MAAMoB,WAAW,GAAGhB,QAAQ,CAACF,GAAG,CAAC;IACjC,MAAMmB,eAAe,GAAGd,UAAU,CAACP,KAAK,CAAC;IACzC,MAAMsB,aAAa,GAAGf,UAAU,CAACL,GAAG,CAAC;;IAErC;IACA,IAAIe,QAAQ,KAAK,CAAC,EAAE;MAClB;MACA,IAAI,CAACI,eAAe,IAAI,CAACF,aAAa,IAAIC,WAAW,EAAE;QACrD;QACA,IAAItB,iBAAiB,KAAK,UAAU,EAAE;UACpC,OAAO,GAAG;QACZ;QACA;QAAA,KACK,IAAIA,iBAAiB,KAAK,WAAW,EAAE;UAC1C,OAAO,CAAC;QACV;QACA;QAAA,KACK;UACH,OAAO,CAAC;QACV;MACF;;MAEA;MACA,IAAI,CAACuB,eAAe,IAAI,CAACF,aAAa,IAAIG,aAAa,EAAE;QACvD;QACA,IAAIxB,iBAAiB,KAAK,UAAU,EAAE;UACpC,OAAO,GAAG;QACZ,CAAC,MAAM,IAAIA,iBAAiB,KAAK,WAAW,EAAE;UAC5C,OAAO,CAAC;QACV,CAAC,MAAM;UACL,OAAO,CAAC;QACV;MACF;;MAEA;MACA,IAAIqB,aAAa,IAAIC,WAAW,EAAE;QAChC,OAAO,CAAC;MACV;;MAEA;MACA,IAAID,aAAa,EAAE;QACjB;QACA,IAAIG,aAAa,EAAE;UACjB;UACA,OAAO,GAAG;QACZ,CAAC,MAAM,IAAIvB,eAAe,KAAK,UAAU,EAAE;UACzC,OAAO,CAAC;QACV,CAAC,MAAM;UACL,OAAO,GAAG;QACZ;MACF;;MAEA;MACA,IAAIqB,WAAW,EAAE;QACf;QACA,IAAIC,eAAe,EAAE;UACnB,OAAO,GAAG;QACZ;;QAEA;QACA,IAAIvB,iBAAiB,KAAK,UAAU,EAAE;UACpC,OAAO,CAAC;QACV,CAAC,MAAM;UACL,OAAO,GAAG;QACZ;MACF;;MAEA;MACA,IAAIuB,eAAe,EAAE;QACnB;QACA,OAAO,GAAG;MACZ;;MAEA;MACA,IAAIC,aAAa,EAAE;QACjB;QACA,IAAIxB,iBAAiB,KAAK,UAAU,EAAE;UACpC,OAAO,GAAG;QACZ,CAAC,MAAM,IAAIA,iBAAiB,KAAK,WAAW,EAAE;UAC5C,OAAO,CAAC;QACV,CAAC,MAAM,IAAIA,iBAAiB,KAAK,SAAS,EAAE;UAC1C,OAAO,CAAC;QACV;MACF;;MAEA;MACA,IAAIA,iBAAiB,KAAK,WAAW,IAAIC,eAAe,KAAK,SAAS,EAAE;QACtE,OAAO,CAAC;MACV,CAAC,MAAM,IAAID,iBAAiB,KAAK,WAAW,KAAKC,eAAe,KAAK,UAAU,IAAIA,eAAe,KAAK,WAAW,CAAC,EAAE;QACnH,OAAO,GAAG;MACZ,CAAC,MAAM,IAAI,CAACD,iBAAiB,KAAK,UAAU,IAAIA,iBAAiB,KAAK,SAAS,KAAKC,eAAe,KAAK,SAAS,EAAE;QACjH,OAAO,GAAG;MACZ,CAAC,MAAM;QACL,OAAO,CAAC;MACV;IACF,CAAC,MAAM,IAAIkB,QAAQ,GAAG,CAAC,EAAE;MACvB;MACA,IAAI,CAACI,eAAe,IAAI,CAACF,aAAa,IAAIG,aAAa,EAAE;QACvD;QACA,MAAMC,gBAAgB,GAAG,IAAItB,IAAI,CAACC,GAAG,CAAC;QACtCqB,gBAAgB,CAACZ,OAAO,CAACY,gBAAgB,CAACX,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;QAE1D;QACA,MAAMY,sBAAsB,GAAGP,QAAQ,CAAC,CAAC;;QAEzC;QACA,IAAIQ,YAAY,GAAGD,sBAAsB;QACzC,IAAI,CAACL,aAAa,IAAI,CAACE,eAAe,IAAIvB,iBAAiB,KAAK,WAAW,EAAE;UAC3E2B,YAAY,IAAI,GAAG;QACrB;;QAEA;QACA,OAAOA,YAAY,GAAG,GAAG;MAC3B;;MAEA;MACA,IAAI,CAACJ,eAAe,IAAI,CAACF,aAAa,IAAIC,WAAW,EAAE;QACrD;QACA,MAAMG,gBAAgB,GAAG,IAAItB,IAAI,CAACC,GAAG,CAAC;QACtCqB,gBAAgB,CAACZ,OAAO,CAACY,gBAAgB,CAACX,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;QAE1D;QACA,MAAMY,sBAAsB,GAAGP,QAAQ,GAAG,CAAC,CAAC,CAAC;;QAE7C;QACA,IAAIQ,YAAY,GAAGD,sBAAsB;QACzC,IAAI,CAACL,aAAa,IAAI,CAACE,eAAe,IAAIvB,iBAAiB,KAAK,WAAW,EAAE;UAC3E2B,YAAY,IAAI,GAAG;QACrB;;QAEA;QACA,OAAOA,YAAY,GAAG,GAAG;MAC3B;;MAEA;MACA,IAAIC,QAAQ,GAAG,CAAC;;MAEhB;MACA,MAAMhB,WAAW,GAAG,IAAIT,IAAI,CAACD,KAAK,CAAC;MACnC,OAAOU,WAAW,IAAIR,GAAG,EAAE;QACzB;QACA,IAAI,CAACE,QAAQ,CAACM,WAAW,CAAC,EAAE;UAC1B;UACA,IAAIH,UAAU,CAACG,WAAW,CAAC,EAAE;YAC3BgB,QAAQ,IAAI,GAAG;UACjB,CAAC,MAAM;YACLA,QAAQ,IAAI,CAAC;UACf;QACF;QACA;QACAhB,WAAW,CAACC,OAAO,CAACD,WAAW,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MAChD;;MAEA;MACA,IAAIa,YAAY,GAAGC,QAAQ;;MAE3B;MACA,IAAI,CAACP,aAAa,IAAI,CAACE,eAAe,IAAIvB,iBAAiB,KAAK,WAAW,EAAE;QAC3E2B,YAAY,IAAI,GAAG;MACrB;;MAEA;MACA,IAAI,CAACL,WAAW,IAAI,CAACE,aAAa,IAAIvB,eAAe,KAAK,SAAS,EAAE;QACnE0B,YAAY,IAAI,GAAG;MACrB,CAAC,MAAM,IAAIH,aAAa,IAAIvB,eAAe,KAAK,SAAS,EAAE;QACzD;MAAA;MAGF,OAAO0B,YAAY;IACrB;EACF;EAEA,OAAO,CAAC;AACV,CAAC;;AAED;AACA,MAAME,cAAc,GAAIC,QAAQ,IAAK;EACnC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,QAAQ;EACnC,IAAIA,QAAQ,GAAG,CAAC,KAAK,CAAC,EAAE,OAAO,GAAGA,QAAQ,OAAO;EACjD,OAAO,GAAGb,IAAI,CAACG,KAAK,CAACU,QAAQ,CAAC,SAAS;AACzC,CAAC;;AAED;AACA,MAAMC,eAAe,GAAGA,CAACnC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,KAAK;EACtE;EACA,IAAI,CAACH,SAAS,IAAI,CAACE,OAAO,EAAE;IAC1B,OAAO,kBAAkB;EAC3B;;EAEA;EACA,MAAME,iBAAiB,GAAGH,WAAW,IAAI,UAAU;EACnD,MAAMI,eAAe,GAAGF,SAAS,IAAI,UAAU;;EAE/C;EACA,MAAMG,KAAK,GAAG,IAAIC,IAAI,CAACP,SAAS,CAAC;EACjC,MAAMQ,GAAG,GAAG,IAAID,IAAI,CAACL,OAAO,CAAC;;EAE7B;EACAI,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BD,GAAG,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;EAExB;EACA,MAAM2B,UAAU,GAAIzB,IAAI,IAAK;IAC3B,OAAO1B,MAAM,CAAC,IAAIsB,IAAI,CAACI,IAAI,CAAC,EAAE,YAAY,EAAE;MAAE0B,MAAM,EAAEnD;IAAG,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMoD,YAAY,GAAIC,MAAM,IAAK;IAC/B,QAAOA,MAAM;MACX,KAAK,SAAS;QAAE,OAAO,MAAM;MAC7B,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,MAAMC,IAAI,GAAGzC,sBAAsB,CAACC,SAAS,EAAEI,iBAAiB,EAAEF,OAAO,EAAEG,eAAe,CAAC;;EAE3F;EACA,IAAI6B,QAAQ,GAAG,EAAE;EACjB,IAAIM,IAAI,KAAK,CAAC,EAAE;IACdN,QAAQ,GAAG,UAAU;EACvB,CAAC,MAAM,IAAIM,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE;IACzBN,QAAQ,GAAG,IAAIM,IAAI,QAAQ;EAC7B,CAAC,MAAM;IACLN,QAAQ,GAAG,IAAIb,IAAI,CAACG,KAAK,CAACgB,IAAI,CAAC,UAAU;EAC3C;EAEA,OAAO,GAAGJ,UAAU,CAACpC,SAAS,CAAC,IAAIsC,YAAY,CAAClC,iBAAiB,CAAC,MAAMgC,UAAU,CAAClC,OAAO,CAAC,IAAIoC,YAAY,CAACjC,eAAe,CAAC,IAAI6B,QAAQ,EAAE;AAC5I,CAAC;;AAED;AACA,MAAMO,eAAe,GAAGA,CAAC;EAAEC,YAAY;EAAEC,cAAc;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EACvF,MAAMC,WAAW,GAAG,EAAE;EACtB,MAAMC,UAAU,GAAG1B,IAAI,CAAC2B,IAAI,CAACN,YAAY,GAAGC,cAAc,CAAC;EAC3D,IAAIM,SAAS,GAAG5B,IAAI,CAAC6B,GAAG,CAAC,CAAC,EAAEN,WAAW,GAAG,CAAC,CAAC;EAC5C,IAAIO,OAAO,GAAG9B,IAAI,CAAC+B,GAAG,CAACL,UAAU,EAAEE,SAAS,GAAG,CAAC,CAAC;EAEjD,IAAIE,OAAO,GAAGF,SAAS,GAAG,CAAC,EAAE;IAC3BA,SAAS,GAAG5B,IAAI,CAAC6B,GAAG,CAAC,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC;EACtC;EAEA,KAAK,IAAIE,CAAC,GAAGJ,SAAS,EAAEI,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;IACzCP,WAAW,CAACQ,IAAI,CAACD,CAAC,CAAC;EACrB;EAEA,IAAIN,UAAU,IAAI,CAAC,EAAE,OAAO,IAAI;EAEhC,oBACEnD,OAAA;IAAK2D,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B5D,OAAA;MACE6D,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAAC,CAAC,CAAE;MAC/Ba,QAAQ,EAAEd,WAAW,KAAK,CAAE;MAC5BW,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC9B;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTlE,OAAA;MACE6D,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACD,WAAW,GAAG,CAAC,CAAE;MAC7Cc,QAAQ,EAAEd,WAAW,KAAK,CAAE;MAC5BW,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAE5B;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACRhB,WAAW,CAACiB,GAAG,CAAEC,MAAM,iBACtBpE,OAAA;MAEE6D,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACmB,MAAM,CAAE;MACpCT,SAAS,EAAE,qBAAqBX,WAAW,KAAKoB,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAAR,QAAA,EAExEQ;IAAM,GAJFA,MAAM;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKL,CACT,CAAC,eACFlE,OAAA;MACE6D,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACD,WAAW,GAAG,CAAC,CAAE;MAC7Cc,QAAQ,EAAEd,WAAW,KAAKG,UAAW;MACrCQ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAE5B;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACTlE,OAAA;MACE6D,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACE,UAAU,CAAE;MACxCW,QAAQ,EAAEd,WAAW,KAAKG,UAAW;MACrCQ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC9B;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AACA,MAAMG,YAAY,GAAG,CAAC,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,aAAa,CAAC;AAEtG,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC1B,MAAM;IAAEC;EAAK,CAAC,GAAGzF,OAAO,CAAC,CAAC;EAC1B,MAAM0F,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,MAAMC,QAAQ,GAAG3F,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4F,aAAa,EAAEC,gBAAgB,CAAC,GAAGlH,QAAQ,CAAC;IACjDmH,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwH,OAAO,EAAEC,UAAU,CAAC,GAAGzH,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0H,KAAK,EAAEC,QAAQ,CAAC,GAAG3H,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4H,SAAS,EAAEC,YAAY,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAAC8H,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/H,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACgI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnI,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAM,CAACoI,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGrI,QAAQ,CAAC,EAAE,CAAC;EAC9E,MAAM,CAACsI,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAACwI,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGzI,QAAQ,CAAC,KAAK,CAAC;EAEjF,MAAM,CAAC0I,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC4I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7I,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8I,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/I,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgJ,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjJ,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvE,MAAM,CAACkJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGnJ,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoJ,YAAY,EAAEC,eAAe,CAAC,GAAGrJ,QAAQ,CAAC;IAC/CsJ,MAAM,EAAE,UAAU;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzJ,QAAQ,CAAC;IAC/C0J,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBL,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACM,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7J,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC8J,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/J,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAACgK,aAAa,EAAEC,gBAAgB,CAAC,GAAGjK,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkK,QAAQ,EAAEC,WAAW,CAAC,GAAGnK,QAAQ,CAAC;IACvCqC,SAAS,EAAE,EAAE;IACbE,OAAO,EAAE,EAAE;IACX6H,MAAM,EAAE,EAAE;IACVd,MAAM,EAAE,EAAE;IACVe,UAAU,EAAE,EAAE;IAAE;IAChBC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1K,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM,CAAC2K,eAAe,EAAEC,kBAAkB,CAAC,GAAG5K,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6K,eAAe,EAAEC,kBAAkB,CAAC,GAAG9K,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAAC+K,cAAc,EAAEC,iBAAiB,CAAC,GAAGhL,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiL,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlL,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACmL,WAAW,EAAEC,cAAc,CAAC,GAAGpL,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqL,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtL,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuL,eAAe,EAAEC,kBAAkB,CAAC,GAAGxL,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyL,aAAa,EAAEC,gBAAgB,CAAC,GAAG1L,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2L,WAAW,EAAEC,cAAc,CAAC,GAAG5L,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6L,cAAc,EAAEC,iBAAiB,CAAC,GAAG9L,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM+L,YAAY,GAAG7L,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4G,KAAK,EAAE;MACVY,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAA,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMuE,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF;QACA,MAAMC,GAAG,GAAG,MAAM7K,GAAG,CAAC8K,GAAG,CAAC,iBAAiB,CAAC;QAC5CC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEH,GAAG,CAACI,IAAI,CAAC;;QAEpC;QACA,IAAIzF,IAAI,KAAKA,IAAI,CAAC0F,IAAI,KAAK,aAAa,IAAI1F,IAAI,CAAC0F,IAAI,KAAK,kBAAkB,CAAC,EAAE;UAAA,IAAAC,qBAAA;UAC7EJ,OAAO,CAACC,GAAG,CAAC,iBAAiBxF,IAAI,CAAC0F,IAAI,EAAE,CAAC;UACzCH,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,EAAAG,qBAAA,GAAAN,GAAG,CAACI,IAAI,CAAChF,WAAW,cAAAkF,qBAAA,uBAApBA,qBAAA,CAAsBC,MAAM,KAAI,CAAC,CAAC;QACnF;;QAEA;QACAtF,gBAAgB,CAAC;UACfC,UAAU,EAAE8E,GAAG,CAACI,IAAI,CAAClF,UAAU,IAAI,EAAE;UACrCC,YAAY,EAAE6E,GAAG,CAACI,IAAI,CAACjF,YAAY,IAAI,EAAE;UACzCC,WAAW,EAAE4E,GAAG,CAACI,IAAI,CAAChF,WAAW,IAAI;QACvC,CAAC,CAAC;;QAEF;QACA,MAAMoF,OAAO,GAAGC,kBAAkB,CAACT,GAAG,CAACI,IAAI,CAAClF,UAAU,IAAI,EAAE,CAAC;QAC7DI,kBAAkB,CAACkF,OAAO,CAAC;MAC7B,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZR,OAAO,CAACzE,KAAK,CAAC,mBAAmB,EAAEiF,GAAG,CAAC;QACvChF,QAAQ,CAAC,gDAAgD,CAAC;MAC5D,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDuE,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACnF,KAAK,CAAC,CAAC;;EAEX;EACA5G,SAAS,CAAC,MAAM;IACd,IAAI2G,IAAI,KAAKA,IAAI,CAAC0F,IAAI,KAAK,aAAa,IAAI1F,IAAI,CAAC0F,IAAI,KAAK,kBAAkB,CAAC,EAAE;MAC7E,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACFT,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/C;UACA,MAAMH,GAAG,GAAG,MAAM7K,GAAG,CAAC8K,GAAG,CAAC,QAAQ,CAAC;UACnCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEH,GAAG,CAAC;;UAEhC;UACA,IAAIA,GAAG,CAACI,IAAI,IAAIJ,GAAG,CAACI,IAAI,CAACQ,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACd,GAAG,CAACI,IAAI,CAACQ,KAAK,CAAC,EAAE;YAC/DnC,WAAW,CAACuB,GAAG,CAACI,IAAI,CAACQ,KAAK,CAAC;YAC3BV,OAAO,CAACC,GAAG,CAAC,oBAAoBH,GAAG,CAACI,IAAI,CAACQ,KAAK,CAACL,MAAM,yCAAyC,CAAC;UACjG,CAAC,MAAM;YACLL,OAAO,CAACzE,KAAK,CAAC,0CAA0C,EAAEuE,GAAG,CAACI,IAAI,CAAC;YACnE3B,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;UACnB;QACF,CAAC,CAAC,OAAOiC,GAAG,EAAE;UACZR,OAAO,CAACzE,KAAK,CAAC,+BAA+B,EAAEiF,GAAG,CAACK,OAAO,CAAC;UAC3Db,OAAO,CAACzE,KAAK,CAAC,eAAe,EAAEiF,GAAG,CAAC;UACnCjC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB;MACF,CAAC;MAEDkC,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACL;MACAlC,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC,EAAE,CAAC9D,IAAI,CAAC,CAAC;;EAEV;EACA3G,SAAS,CAAC,MAAM;IACd,MAAMgN,sBAAsB,GAAGA,CAAA,KAAM;MACnC,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;MAChE,IAAI,CAACF,SAAS,EAAE;MAEhB,MAAM;QAAEG,SAAS;QAAEC,YAAY;QAAEC;MAAa,CAAC,GAAGL,SAAS;MAE3D,IAAIG,SAAS,GAAGE,YAAY,IAAID,YAAY,GAAG,EAAE,IAAI,CAACpF,mBAAmB,EAAE;QACzEsF,kBAAkB,CAAC,CAAC;MACtB;IACF,CAAC;IAED,MAAMN,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;IAChE,IAAIF,SAAS,EAAE;MACbA,SAAS,CAACO,gBAAgB,CAAC,QAAQ,EAAER,sBAAsB,CAAC;MAC5D,OAAO,MAAMC,SAAS,CAACQ,mBAAmB,CAAC,QAAQ,EAAET,sBAAsB,CAAC;IAC9E;EACF,CAAC,EAAE,CAAC/E,mBAAmB,EAAEF,gBAAgB,EAAEV,eAAe,CAACkF,MAAM,CAAC,CAAC;;EAEnE;EACAvM,SAAS,CAAC,MAAM;IACd,MAAM0N,4BAA4B,GAAGA,CAAA,KAAM;MACzC,MAAMT,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,2BAA2B,CAAC;MACtE,IAAI,CAACF,SAAS,EAAE;MAEhB,MAAM;QAAEG,SAAS;QAAEC,YAAY;QAAEC;MAAa,CAAC,GAAGL,SAAS;MAE3D,IAAIG,SAAS,GAAGE,YAAY,IAAID,YAAY,GAAG,EAAE,IAAI,CAAC9E,yBAAyB,EAAE;QAC/EoF,wBAAwB,CAAC,CAAC;MAC5B;IACF,CAAC;IAED,MAAMV,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,2BAA2B,CAAC;IACtE,IAAIF,SAAS,EAAE;MACbA,SAAS,CAACO,gBAAgB,CAAC,QAAQ,EAAEE,4BAA4B,CAAC;MAClE,OAAO,MAAMT,SAAS,CAACQ,mBAAmB,CAAC,QAAQ,EAAEC,4BAA4B,CAAC;IACpF;EACF,CAAC,EAAE,CAACnF,yBAAyB,EAAEF,sBAAsB,EAAErB,aAAa,CAACG,YAAY,CAACoF,MAAM,CAAC,CAAC;;EAE1F;EACAvM,SAAS,CAAC,MAAM;IACd,MAAM4N,uBAAuB,GAAGA,CAAA,KAAM;MACpC,MAAMX,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC;MACjE,IAAI,CAACF,SAAS,EAAE;MAEhB,MAAM;QAAEG,SAAS;QAAEC,YAAY;QAAEC;MAAa,CAAC,GAAGL,SAAS;MAE3D,IAAIG,SAAS,GAAGE,YAAY,IAAID,YAAY,GAAG,EAAE,IAAI,CAACxE,oBAAoB,EAAE;QAC1EgF,mBAAmB,CAAC,CAAC;MACvB;IACF,CAAC;IAED,MAAMZ,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC;IACjE,IAAIF,SAAS,EAAE;MACbA,SAAS,CAACO,gBAAgB,CAAC,QAAQ,EAAEI,uBAAuB,CAAC;MAC7D,OAAO,MAAMX,SAAS,CAACQ,mBAAmB,CAAC,QAAQ,EAAEG,uBAAuB,CAAC;IAC/E;EACF,CAAC,EAAE,CAAC/E,oBAAoB,EAAEF,iBAAiB,EAAE3B,aAAa,CAACI,WAAW,CAACmF,MAAM,CAAC,CAAC;;EAE/E;EACAvM,SAAS,CAAC,MAAM;IACd8H,sBAAsB,CAACT,eAAe,CAACyG,KAAK,CAAC,CAAC,EAAE/F,gBAAgB,CAAC,CAAC;EACpE,CAAC,EAAE,CAACV,eAAe,EAAEU,gBAAgB,CAAC,CAAC;EAEvC/H,SAAS,CAAC,MAAM;IACdoI,4BAA4B,CAACpB,aAAa,CAACG,YAAY,CAAC2G,KAAK,CAAC,CAAC,EAAEzF,sBAAsB,CAAC,CAAC;EAC3F,CAAC,EAAE,CAACrB,aAAa,CAACG,YAAY,EAAEkB,sBAAsB,CAAC,CAAC;EAExDrI,SAAS,CAAC,MAAM;IACd,MAAM+N,gBAAgB,GAAGC,kBAAkB,CAACzE,YAAY,CAAC;IACzDb,uBAAuB,CAACqF,gBAAgB,CAACD,KAAK,CAAC,CAAC,EAAEnF,iBAAiB,CAAC,CAAC;EACvE,CAAC,EAAE,CAAC3B,aAAa,CAACI,WAAW,EAAEmC,YAAY,EAAEZ,iBAAiB,CAAC,CAAC;;EAEhE;EACA,MAAM4E,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAItF,mBAAmB,IAAIJ,mBAAmB,CAAC0E,MAAM,IAAIlF,eAAe,CAACkF,MAAM,EAAE;IAEjFrE,sBAAsB,CAAC,IAAI,CAAC;IAC5B+F,UAAU,CAAC,MAAM;MACf,MAAMC,kBAAkB,GAAGzK,IAAI,CAAC+B,GAAG,CAACuC,gBAAgB,GAAG,EAAE,EAAEV,eAAe,CAACkF,MAAM,CAAC;MAClFvE,mBAAmB,CAACkG,kBAAkB,CAAC;MACvChG,sBAAsB,CAAC,KAAK,CAAC;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMyF,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIpF,yBAAyB,IAAIJ,yBAAyB,CAACoE,MAAM,IAAIvF,aAAa,CAACG,YAAY,CAACoF,MAAM,EAAE;IAExG/D,4BAA4B,CAAC,IAAI,CAAC;IAClCyF,UAAU,CAAC,MAAM;MACf,MAAMC,kBAAkB,GAAGzK,IAAI,CAAC+B,GAAG,CAAC6C,sBAAsB,GAAG,EAAE,EAAErB,aAAa,CAACG,YAAY,CAACoF,MAAM,CAAC;MACnGjE,yBAAyB,CAAC4F,kBAAkB,CAAC;MAC7C1F,4BAA4B,CAAC,KAAK,CAAC;IACrC,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMqF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAME,gBAAgB,GAAGC,kBAAkB,CAACzE,YAAY,CAAC;IACzD,IAAIV,oBAAoB,IAAIJ,oBAAoB,CAAC8D,MAAM,IAAIwB,gBAAgB,CAACxB,MAAM,EAAE;IAEpFzD,uBAAuB,CAAC,IAAI,CAAC;IAC7BmF,UAAU,CAAC,MAAM;MACf,MAAMC,kBAAkB,GAAGzK,IAAI,CAAC+B,GAAG,CAACmD,iBAAiB,GAAG,EAAE,EAAEoF,gBAAgB,CAACxB,MAAM,CAAC;MACpF3D,oBAAoB,CAACsF,kBAAkB,CAAC;MACxCpF,uBAAuB,CAAC,KAAK,CAAC;IAChC,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAGD;EACA,MAAM2D,kBAAkB,GAAI0B,QAAQ,IAAK;IACvC,MAAMC,aAAa,GAAG,CAAC,CAAC;IAExBD,QAAQ,CAACE,OAAO,CAACC,OAAO,IAAI;MAC1B;MACA,MAAMjM,WAAW,GAAGiM,OAAO,CAACjM,WAAW,IAAI,UAAU;MACrD,MAAME,SAAS,GAAG+L,OAAO,CAAC/L,SAAS,IAAI,UAAU;MACjD,MAAMgM,GAAG,GAAG,GAAGD,OAAO,CAAClM,SAAS,IAAIC,WAAW,IAAIiM,OAAO,CAAChM,OAAO,IAAIC,SAAS,IAAI+L,OAAO,CAACnE,MAAM,EAAE;MAEnG,IAAI,CAACiE,aAAa,CAACG,GAAG,CAAC,EAAE;QACvBH,aAAa,CAACG,GAAG,CAAC,GAAG;UACnBC,EAAE,EAAED,GAAG;UACPE,GAAG,EAAEH,OAAO,CAACG,GAAG;UAAE;UAClBrM,SAAS,EAAEkM,OAAO,CAAClM,SAAS;UAC5BC,WAAW,EAAEiM,OAAO,CAACjM,WAAW,IAAI,UAAU;UAAE;UAChDC,OAAO,EAAEgM,OAAO,CAAChM,OAAO;UACxBC,SAAS,EAAE+L,OAAO,CAAC/L,SAAS,IAAI,UAAU;UAAE;UAC5C4H,MAAM,EAAEmE,OAAO,CAACnE,MAAM;UACtBuE,SAAS,EAAEJ,OAAO,CAACI,SAAS;UAC5BC,WAAW,EAAE,EAAE;UACftF,MAAM,EAAEiF,OAAO,CAACjF,MAAM;UACtBC,QAAQ,EAAEgF,OAAO,CAAChF,QAAQ,IAAI,EAAE;UAChCsF,cAAc,EAAEN,OAAO,CAACM,cAAc,IAAI,KAAK;UAC/CC,eAAe,EAAEP,OAAO,CAACO,eAAe,IAAI,CAAC;UAC7CV,QAAQ,EAAE;QACZ,CAAC;MACH;;MAEA;MACA,IAAIG,OAAO,CAAClE,UAAU,IAAI,CAACgE,aAAa,CAACG,GAAG,CAAC,CAACI,WAAW,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,GAAG,KAAKH,OAAO,CAAClE,UAAU,CAACqE,GAAG,CAAC,EAAE;QACrGL,aAAa,CAACG,GAAG,CAAC,CAACI,WAAW,CAACjJ,IAAI,CAAC4I,OAAO,CAAClE,UAAU,CAAC;MACzD;;MAEA;MACAgE,aAAa,CAACG,GAAG,CAAC,CAACJ,QAAQ,CAACzI,IAAI,CAAC4I,OAAO,CAAC;;MAEzC;MACA,IAAIA,OAAO,CAACjF,MAAM,KAAK,SAAS,EAAE;QAChC+E,aAAa,CAACG,GAAG,CAAC,CAAClF,MAAM,GAAG,SAAS;MACvC;MACA;MAAA,KACK,IAAIiF,OAAO,CAACjF,MAAM,KAAK,UAAU,IAAI+E,aAAa,CAACG,GAAG,CAAC,CAAClF,MAAM,KAAK,SAAS,EAAE;QACjF+E,aAAa,CAACG,GAAG,CAAC,CAAClF,MAAM,GAAG,UAAU;MACxC;;MAEA;MACA,IAAIiF,OAAO,CAAChF,QAAQ,IAAI,CAAC8E,aAAa,CAACG,GAAG,CAAC,CAACjF,QAAQ,CAAC0F,QAAQ,CAACV,OAAO,CAAChF,QAAQ,CAAC,EAAE;QAAA,IAAA2F,mBAAA;QAC/Eb,aAAa,CAACG,GAAG,CAAC,CAACjF,QAAQ,IAAI,CAAC8E,aAAa,CAACG,GAAG,CAAC,CAACjF,QAAQ,GAAG,IAAI,GAAG,EAAE,IACrE,GAAG,EAAA2F,mBAAA,GAAAX,OAAO,CAAClE,UAAU,cAAA6E,mBAAA,uBAAlBA,mBAAA,CAAoBC,QAAQ,KAAI,SAAS,KAAKZ,OAAO,CAAChF,QAAQ,EAAE;MACvE;;MAEA;MACA,IAAIgF,OAAO,CAACM,cAAc,EAAE;QAC1BR,aAAa,CAACG,GAAG,CAAC,CAACK,cAAc,GAAG,IAAI;QACxCR,aAAa,CAACG,GAAG,CAAC,CAACM,eAAe,GAAGpL,IAAI,CAAC6B,GAAG,CAAC8I,aAAa,CAACG,GAAG,CAAC,CAACM,eAAe,IAAI,CAAC,EAAEP,OAAO,CAACO,eAAe,IAAI,CAAC,CAAC;MACtH;IACF,CAAC,CAAC;IAEF,OAAOM,MAAM,CAACC,MAAM,CAAChB,aAAa,CAAC;EACrC,CAAC;EAED,MAAMiB,mBAAmB,GAAGA,CAAA,KAAM;IAChCtI,QAAQ,CAAC,uBAAuB,CAAC;EACnC,CAAC;;EAED;EACA,MAAMuI,gBAAgB,GAAG,MAAOC,cAAc,IAAK;IACjD;IACA,IAAI,CAACA,cAAc,EAAE;MACnBrD,OAAO,CAACzE,KAAK,CAAC,+BAA+B,CAAC;MAC9C8D,kBAAkB,CAAC,uCAAuC,CAAC;MAC3DF,qBAAqB,CAAC,KAAK,CAAC;MAC5BF,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;IACF;;IAEA;IACA,IAAIoE,cAAc,KAAK,WAAW,EAAE;MAClCrD,OAAO,CAACzE,KAAK,CAAC,uCAAuC,CAAC;MACtD8D,kBAAkB,CAAC,uCAAuC,CAAC;MAC3DF,qBAAqB,CAAC,KAAK,CAAC;MAC5BF,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;IACF;IAEAE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACFW,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEoD,cAAc,CAAC;MAC9E,MAAMC,QAAQ,GAAG,MAAMrO,GAAG,CAAC8K,GAAG,CAAC,qBAAqBsD,cAAc,QAAQ,EAAE;QAC1EE,OAAO,EAAE;UAAE,cAAc,EAAE7I;QAAM;MACnC,CAAC,CAAC;MAEFuE,cAAc,CAACqE,QAAQ,CAACpD,IAAI,CAAC;MAC7BF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEqD,QAAQ,CAACpD,IAAI,CAAC;IAC/D,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZR,OAAO,CAACzE,KAAK,CAAC,sCAAsC,EAAEiF,GAAG,CAAC;MAC1DnB,kBAAkB,CAAC,uCAAuC,CAAC;MAC3DJ,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC,SAAS;MACRE,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMqE,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,KAAK,GAAG/C,KAAK,CAACgD,IAAI,CAACF,CAAC,CAACG,MAAM,CAACF,KAAK,CAAC;;IAExC;IACA,IAAIpE,aAAa,CAACe,MAAM,GAAGqD,KAAK,CAACrD,MAAM,GAAG,CAAC,EAAE;MAC3CZ,cAAc,CAAC,qCAAqC,CAAC;MACrD;IACF;;IAEA;IACA,MAAMoE,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,MAAMC,cAAc,GAAGJ,KAAK,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,GAAGJ,OAAO,CAAC;IAChE,IAAIC,cAAc,CAACzD,MAAM,GAAG,CAAC,EAAE;MAC7BZ,cAAc,CAAC,QAAQqE,cAAc,CAAC7J,GAAG,CAACiK,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,kCAAkC,CAAC;MACpG;IACF;;IAEA;IACA,MAAMC,YAAY,GAAG,CACnB,iBAAiB,EACjB,oBAAoB,EACpB,yEAAyE,EACzE,0BAA0B,EAC1B,mEAAmE,EACnE,YAAY,EACZ,WAAW,EACX,WAAW,EACX,YAAY,CACb;IAED,MAAMC,YAAY,GAAGZ,KAAK,CAACK,MAAM,CAACC,IAAI,IAAI,CAACK,YAAY,CAACvB,QAAQ,CAACkB,IAAI,CAACO,IAAI,CAAC,CAAC;IAC5E,IAAID,YAAY,CAACjE,MAAM,GAAG,CAAC,EAAE;MAC3BZ,cAAc,CAAC,QAAQ6E,YAAY,CAACrK,GAAG,CAACiK,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,oFAAoF,CAAC;MACpJ;IACF;;IAEA;IACA7E,gBAAgB,CAACiF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGd,KAAK,CAAC,CAAC;IAC7CjE,cAAc,CAAC,IAAI,CAAC;;IAEpB;IACAgE,CAAC,CAACG,MAAM,CAACa,KAAK,GAAG,IAAI;EACvB,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClCpF,gBAAgB,CAACiF,IAAI,IAAIA,IAAI,CAACT,MAAM,CAAC,CAACa,CAAC,EAAErL,CAAC,KAAKA,CAAC,KAAKoL,KAAK,CAAC,CAAC;EAC9D,CAAC;;EAED;EACA,MAAME,WAAW,GAAIC,QAAQ,IAAK;IAChC;IACA,IAAI,CAACA,QAAQ,EAAE,oBAAOhP,OAAA,CAACP,MAAM;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;;IAEhC;IACA,MAAMuK,IAAI,GAAGQ,MAAM,CAACD,QAAQ,CAAC,CAACE,WAAW,CAAC,CAAC;IAE3C,IAAIT,IAAI,CAACzB,QAAQ,CAAC,KAAK,CAAC,EAAE,oBAAOhN,OAAA,CAACN,SAAS;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC9C,IAAIuK,IAAI,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,IAAI,CAACzB,QAAQ,CAAC,QAAQ,CAAC,IAAIyB,IAAI,CAACzB,QAAQ,CAAC,KAAK,CAAC,EAAE,oBAAOhN,OAAA,CAACL,UAAU;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnG,IAAIuK,IAAI,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,IAAI,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,IAAI,CAACzB,QAAQ,CAAC,KAAK,CAAC,EAAE,oBAAOhN,OAAA,CAACJ,WAAW;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpG,IAAIuK,IAAI,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,IAAI,CAACzB,QAAQ,CAAC,KAAK,CAAC,IAAIyB,IAAI,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,IAAI,CAACzB,QAAQ,CAAC,KAAK,CAAC,IAAIyB,IAAI,CAACzB,QAAQ,CAAC,KAAK,CAAC,EAAE,oBAAOhN,OAAA,CAACH,WAAW;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnJ,oBAAOlE,OAAA,CAACP,MAAM;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB,CAAC;;EAED;EACA,MAAMiL,WAAW,GAAG,MAAO5B,cAAc,IAAK;IAC5C,IAAI/D,aAAa,CAACe,MAAM,KAAK,CAAC,EAAE;;IAEhC;IACA,IAAI,CAACgD,cAAc,EAAE;MACnBrD,OAAO,CAACzE,KAAK,CAAC,+BAA+B,CAAC;MAC9CkE,cAAc,CAAC,+CAA+C,CAAC;MAC/D;IACF;IAEAE,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,IAAI,CAAC;;IAEpB;IACAZ,iBAAiB,CAAC,sBAAsB,CAAC;IACzCE,mBAAmB,CAAC,IAAI,CAAC;IAEzB,IAAI;MACF,MAAMmG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/B7F,aAAa,CAAC6C,OAAO,CAAC6B,IAAI,IAAI;QAC5BkB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEpB,IAAI,CAAC;MAChC,CAAC,CAAC;MAEF,MAAM/O,GAAG,CAACoQ,IAAI,CACZ,qBAAqBhC,cAAc,SAAS,EAC5C6B,QAAQ,EACR;QACE3B,OAAO,EAAE;UACP,cAAc,EAAE7I,KAAK;UACrB,cAAc,EAAE;QAClB;MACF,CACF,CAAC;;MAED;MACA,MAAM0I,gBAAgB,CAACC,cAAc,CAAC;;MAEtC;MACA9D,gBAAgB,CAAC,EAAE,CAAC;;MAEpB;MACAV,iBAAiB,CAAC,yBAAyB,CAAC;MAC5CE,mBAAmB,CAAC,IAAI,CAAC;MACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACpD,CAAC,CAAC,OAAOyB,GAAG,EAAE;MAAA,IAAA8E,aAAA,EAAAC,kBAAA;MACZvF,OAAO,CAACzE,KAAK,CAAC,uBAAuB,EAAEiF,GAAG,CAAC;MAC3Cf,cAAc,CAAC,wBAAwB,IAAI,EAAA6F,aAAA,GAAA9E,GAAG,CAAC8C,QAAQ,cAAAgC,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcpF,IAAI,cAAAqF,kBAAA,uBAAlBA,kBAAA,CAAoB1E,OAAO,KAAIL,GAAG,CAACK,OAAO,CAAC,CAAC;IACzF,CAAC,SAAS;MACRlB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAM6F,kBAAkB,GAAG,MAAOC,MAAM,IAAK;IAC3C,IAAI;MACF;MACA,IAAI,CAAC1I,eAAe,IAAI,CAACA,eAAe,CAACwF,GAAG,EAAE;QAC5CvC,OAAO,CAACzE,KAAK,CAAC,kCAAkC,CAAC;QACjDmK,KAAK,CAAC,2CAA2C,CAAC;QAClD;MACF;MAEA,IAAI,CAACD,MAAM,EAAE;QACXzF,OAAO,CAACzE,KAAK,CAAC,sBAAsB,CAAC;QACrCmK,KAAK,CAAC,2CAA2C,CAAC;QAClD;MACF;MAEA,MAAMpC,QAAQ,GAAG,MAAMrO,GAAG,CAAC8K,GAAG,CAAC,qBAAqBhD,eAAe,CAACwF,GAAG,UAAUkD,MAAM,WAAW,EAAE;QAClGlC,OAAO,EAAE;UAAE,cAAc,EAAE7I;QAAM,CAAC;QAClCiL,YAAY,EAAE;MAChB,CAAC,CAAC;;MAEF;MACA,MAAMC,QAAQ,GAAG5G,WAAW,CAAC6G,IAAI,CAAC7B,IAAI,IAAIA,IAAI,CAACzB,GAAG,KAAKkD,MAAM,CAAC;;MAE9D;MACA,MAAMK,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC5C,QAAQ,CAACpD,IAAI,CAAC,CAAC,CAAC;MACjE,MAAMiG,IAAI,GAAGnF,QAAQ,CAACoF,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGP,GAAG;MACfK,IAAI,CAACG,YAAY,CAAC,UAAU,EAAEV,QAAQ,CAACW,YAAY,CAAC;MACpDvF,QAAQ,CAACwF,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;;MAEZ;MACAP,IAAI,CAACQ,UAAU,CAACC,WAAW,CAACT,IAAI,CAAC;MACjCJ,MAAM,CAACC,GAAG,CAACa,eAAe,CAACf,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOtF,GAAG,EAAE;MACZR,OAAO,CAACzE,KAAK,CAAC,mBAAmB,EAAEiF,GAAG,CAAC;MACvCkF,KAAK,CAAC,2CAA2C,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMoB,gBAAgB,GAAG,MAAOrB,MAAM,IAAK;IACzC,IAAI,CAACsB,OAAO,CAAC,2CAA2C,CAAC,EAAE;MACzD;IACF;;IAEA;IACA,IAAI,CAAChK,eAAe,IAAI,CAACA,eAAe,CAACwF,GAAG,EAAE;MAC5CvC,OAAO,CAACzE,KAAK,CAAC,kCAAkC,CAAC;MACjDmK,KAAK,CAAC,2CAA2C,CAAC;MAClD;IACF;IAEA,IAAI,CAACD,MAAM,EAAE;MACXzF,OAAO,CAACzE,KAAK,CAAC,sBAAsB,CAAC;MACrCmK,KAAK,CAAC,2CAA2C,CAAC;MAClD;IACF;IAEA,IAAI;MACF,MAAMzQ,GAAG,CAAC+R,MAAM,CAAC,qBAAqBjK,eAAe,CAACwF,GAAG,UAAUkD,MAAM,EAAE,EAAE;QAC3ElC,OAAO,EAAE;UAAE,cAAc,EAAE7I;QAAM;MACnC,CAAC,CAAC;;MAEF;MACA,MAAM0I,gBAAgB,CAACrG,eAAe,CAACwF,GAAG,CAAC;;MAE3C;MACA1D,iBAAiB,CAAC,qBAAqB,CAAC;MACxCE,mBAAmB,CAAC,IAAI,CAAC;MACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACpD,CAAC,CAAC,OAAOyB,GAAG,EAAE;MACZR,OAAO,CAACzE,KAAK,CAAC,mBAAmB,EAAEiF,GAAG,CAAC;MACvCkF,KAAK,CAAC,2CAA2C,CAAC;IACpD;EACF,CAAC;EAED,MAAMuB,uBAAuB,GAAG,MAAO7E,OAAO,IAAK;IACjD;IACA,IAAI,CAACA,OAAO,EAAE;MACZpC,OAAO,CAACzE,KAAK,CAAC,sBAAsB,CAAC;MACrCsD,iBAAiB,CAAC,wDAAwD,CAAC;MAC3EE,mBAAmB,CAAC,IAAI,CAAC;MACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAClD;IACF;;IAEA;IACA,IAAI,CAACqD,OAAO,CAACG,GAAG,EAAE;MAChBvC,OAAO,CAACzE,KAAK,CAAC,6BAA6B,EAAE6G,OAAO,CAAC;MACrDvD,iBAAiB,CAAC,wDAAwD,CAAC;MAC3EE,mBAAmB,CAAC,IAAI,CAAC;MACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAClD;IACF;IAEA/B,kBAAkB,CAACoF,OAAO,CAAC;IAC3BlF,eAAe,CAAC;MACdC,MAAM,EAAE,UAAU;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF1B,YAAY,CAAC,IAAI,CAAC;;IAElB;IACA,IAAI;MACF,MAAM0H,gBAAgB,CAAChB,OAAO,CAACG,GAAG,CAAC;IACrC,CAAC,CAAC,OAAO/B,GAAG,EAAE;MACZR,OAAO,CAACzE,KAAK,CAAC,sCAAsC,EAAEiF,GAAG,CAAC;MAC1D;IACF;EACF,CAAC;EAED,MAAM0G,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxL,YAAY,CAAC,KAAK,CAAC;IACnBsB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMmK,yBAAyB,GAAG,MAAO/E,OAAO,IAAK;IACnD;IACA,IAAI,CAACA,OAAO,EAAE;MACZpC,OAAO,CAACzE,KAAK,CAAC,sBAAsB,CAAC;MACrCsD,iBAAiB,CAAC,wDAAwD,CAAC;MAC3EE,mBAAmB,CAAC,IAAI,CAAC;MACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAClD;IACF;;IAEA;IACA,IAAI,CAACqD,OAAO,CAACG,GAAG,EAAE;MAChBvC,OAAO,CAACzE,KAAK,CAAC,6BAA6B,EAAE6G,OAAO,CAAC;MACrDvD,iBAAiB,CAAC,wDAAwD,CAAC;MAC3EE,mBAAmB,CAAC,IAAI,CAAC;MACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAClD;IACF;IAEA/B,kBAAkB,CAACoF,OAAO,CAAC;IAC3BtF,sBAAsB,CAAC,IAAI,CAAC;;IAE5B;IACA,IAAI;MACF,MAAMsG,gBAAgB,CAAChB,OAAO,CAACG,GAAG,CAAC;IACrC,CAAC,CAAC,OAAO/B,GAAG,EAAE;MACZR,OAAO,CAACzE,KAAK,CAAC,sCAAsC,EAAEiF,GAAG,CAAC;MAC1D;IACF;EACF,CAAC;;EAED;EACA,MAAM4G,0BAA0B,GAAGA,CAAA,KAAM;IACvCtK,sBAAsB,CAAC,KAAK,CAAC;IAC7BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMqK,oBAAoB,GAAI5D,CAAC,IAAK;IAClCvG,eAAe,CAAC;MACd,GAAGD,YAAY;MACf,CAACwG,CAAC,CAACG,MAAM,CAACO,IAAI,GAAGV,CAAC,CAACG,MAAM,CAACa;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6C,oBAAoB,GAAG,MAAO7D,CAAC,IAAK;IACxCA,CAAC,CAAC8D,cAAc,CAAC,CAAC;IAElB,IAAI;MACFvH,OAAO,CAACC,GAAG,CAAC,oCAAoClD,eAAe,CAACwF,GAAG,EAAE,EAAEtF,YAAY,CAAC;;MAEpF;MACA,MAAMhI,GAAG,CAACuS,GAAG,CACX,mBAAmBzK,eAAe,CAACwF,GAAG,EAAE,EACxCtF,YACF,CAAC;;MAED;MACAlC,gBAAgB,CAACyJ,IAAI,IAAI;QACvB;QACA,MAAMiD,mBAAmB,GAAGjD,IAAI,CAACvJ,YAAY,CAAChB,GAAG,CAACyN,GAAG,IACnDA,GAAG,CAACnF,GAAG,KAAKxF,eAAe,CAACwF,GAAG,GAC3B;UAAE,GAAGmF,GAAG;UAAEvK,MAAM,EAAEF,YAAY,CAACE,MAAM;UAAEC,QAAQ,EAAEH,YAAY,CAACG;QAAS,CAAC,GACxEsK,GACN,CAAC;;QAED;QACA,MAAMC,kBAAkB,GAAGnD,IAAI,CAACtJ,WAAW,CAACjB,GAAG,CAACyN,GAAG,IACjDA,GAAG,CAACnF,GAAG,KAAKxF,eAAe,CAACwF,GAAG,GAC3B;UAAE,GAAGmF,GAAG;UAAEvK,MAAM,EAAEF,YAAY,CAACE,MAAM;UAAEC,QAAQ,EAAEH,YAAY,CAACG;QAAS,CAAC,GACxEsK,GACN,CAAC;;QAED;QACA,MAAME,iBAAiB,GAAGpD,IAAI,CAACxJ,UAAU,CAACf,GAAG,CAACyN,GAAG,IAC/CA,GAAG,CAACnF,GAAG,KAAKxF,eAAe,CAACwF,GAAG,GAC3B;UAAE,GAAGmF,GAAG;UAAEvK,MAAM,EAAEF,YAAY,CAACE,MAAM;UAAEC,QAAQ,EAAEH,YAAY,CAACG;QAAS,CAAC,GACxEsK,GACN,CAAC;QAED,OAAO;UACL1M,UAAU,EAAE4M,iBAAiB;UAC7B3M,YAAY,EAAEwM,mBAAmB;UACjCvM,WAAW,EAAEyM;QACf,CAAC;MACH,CAAC,CAAC;;MAEF;MACA,MAAME,aAAa,GAAG/M,aAAa,CAACE,UAAU,CAAC4H,IAAI,CAAC8E,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAKxF,eAAe,CAACwF,GAAG,CAAC;MAC3F,IAAIsF,aAAa,EAAE;QACjB,MAAMC,cAAc,GAAGvH,kBAAkB,CACvCzF,aAAa,CAACE,UAAU,CAACf,GAAG,CAACyN,GAAG,IAC9BA,GAAG,CAACnF,GAAG,KAAKxF,eAAe,CAACwF,GAAG,GAC3B;UAAE,GAAGmF,GAAG;UAAEvK,MAAM,EAAEF,YAAY,CAACE,MAAM;UAAEC,QAAQ,EAAEH,YAAY,CAACG;QAAS,CAAC,GACxEsK,GACN,CACF,CAAC;QACDtM,kBAAkB,CAAC0M,cAAc,CAAC;MACpC;;MAEA;MACAjJ,iBAAiB,CAAC,uCAAuC,CAAC;MAC1DE,mBAAmB,CAAC,IAAI,CAAC;MACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;;MAElD;MACAmI,gBAAgB,CAAC,CAAC;;MAElB;MACAnF,UAAU,CAAC,YAAY;QACrB,IAAI;UACF,MAAMjC,GAAG,GAAG,MAAM7K,GAAG,CAAC8K,GAAG,CAAC,iBAAiB,CAAC;;UAE5C;UACAhF,gBAAgB,CAAC;YACfC,UAAU,EAAE8E,GAAG,CAACI,IAAI,CAAClF,UAAU,IAAI,EAAE;YACrCC,YAAY,EAAE6E,GAAG,CAACI,IAAI,CAACjF,YAAY,IAAI,EAAE;YACzCC,WAAW,EAAE4E,GAAG,CAACI,IAAI,CAAChF,WAAW,IAAI;UACvC,CAAC,CAAC;;UAEF;UACA,MAAMoF,OAAO,GAAGC,kBAAkB,CAACT,GAAG,CAACI,IAAI,CAAClF,UAAU,IAAI,EAAE,CAAC;UAC7DI,kBAAkB,CAACkF,OAAO,CAAC;QAC7B,CAAC,CAAC,OAAOE,GAAG,EAAE;UACZR,OAAO,CAACzE,KAAK,CAAC,0BAA0B,EAAEiF,GAAG,CAAC;QAChD;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOA,GAAG,EAAE;MAAA,IAAAuH,cAAA,EAAAC,mBAAA;MACZhI,OAAO,CAACzE,KAAK,CAAC,qCAAqC,EAAEiF,GAAG,CAAC;MACzDhF,QAAQ,CAAC,6DAA6D,CAAC;MACvE;MACAkK,KAAK,CAAC,QAAQ,EAAAqC,cAAA,GAAAvH,GAAG,CAAC8C,QAAQ,cAAAyE,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7H,IAAI,cAAA8H,mBAAA,uBAAlBA,mBAAA,CAAoBC,GAAG,KAAIzH,GAAG,CAACK,OAAO,EAAE,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMqH,mBAAmB,GAAI9F,OAAO,IAAK;IAAA,IAAA+F,oBAAA;IACvC;IACA,MAAMC,kBAAkB,GAAIC,UAAU,IAAK;MACzC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMxR,IAAI,GAAG,IAAIJ,IAAI,CAAC4R,UAAU,CAAC;MACjC,OAAO,GAAGxR,IAAI,CAACyR,WAAW,CAAC,CAAC,IAAIvD,MAAM,CAAClO,IAAI,CAAC0R,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIzD,MAAM,CAAClO,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACoR,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC3H,CAAC;;IAED;IACA,MAAMC,OAAO,GAAGhO,IAAI,IAAIA,IAAI,CAAC0F,IAAI,KAAK,aAAa;;IAEnD;IACA,IAAIuI,OAAO,GAAG,KAAK;;IAEnB;IACA,IAAItG,OAAO,CAACuG,QAAQ,IAAIvG,OAAO,CAACuG,QAAQ,CAACpG,GAAG,KAAK9H,IAAI,CAAC8H,GAAG,EAAE;MACzDmG,OAAO,GAAG,IAAI;IAChB;IACA;IAAA,KACK,IAAItG,OAAO,CAACwG,UAAU,KAAKnO,IAAI,CAAC8H,GAAG,EAAE;MACxCmG,OAAO,GAAG,IAAI;IAChB;IACA;IAAA,KACK,IAAItG,OAAO,CAACuG,QAAQ,IAAIvG,OAAO,CAACuG,QAAQ,CAACrG,EAAE,KAAK7H,IAAI,CAAC8H,GAAG,EAAE;MAC7DmG,OAAO,GAAG,IAAI;IAChB;IACA;IAAA,KACK,IAAItG,OAAO,CAACyG,SAAS,KAAKpO,IAAI,CAAC8H,GAAG,EAAE;MACvCmG,OAAO,GAAG,IAAI;IAChB;;IAEA;IACA1I,OAAO,CAACC,GAAG,CAAC,UAAU,EAAExF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8H,GAAG,CAAC;IAClCvC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEmC,OAAO,CAAC;IAChCpC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEyI,OAAO,CAAC;IAEjC,MAAMI,SAAS,GAAG1G,OAAO,CAACjF,MAAM,KAAK,SAAS;;IAE9C;IACA;IACA;IACA,IAAIsL,OAAO,EAAE;MACX;IAAA,CACD,MAAM,IAAIC,OAAO,IAAII,SAAS,EAAE;MAC/B;IAAA,CACD,MAAM;MACL;MACA,IAAI,CAACJ,OAAO,EAAE;QACZhD,KAAK,CAAC,oEAAoE,CAAC;MAC7E,CAAC,MAAM,IAAI,CAACoD,SAAS,EAAE;QACrBpD,KAAK,CAAC,8DAA8D,CAAC;MACvE,CAAC,MAAM;QACLA,KAAK,CAAC,sCAAsC,CAAC;MAC/C;MACA;IACF;IAEA1I,kBAAkB,CAACoF,OAAO,CAAC;IAC3BpE,WAAW,CAAC;MACV9H,SAAS,EAAEkS,kBAAkB,CAAChG,OAAO,CAAClM,SAAS,CAAC;MAChDC,WAAW,EAAEiM,OAAO,CAACjM,WAAW,IAAI,UAAU;MAC9CC,OAAO,EAAEgS,kBAAkB,CAAChG,OAAO,CAAChM,OAAO,CAAC;MAC5CC,SAAS,EAAE+L,OAAO,CAAC/L,SAAS,IAAI,UAAU;MAC1C4H,MAAM,EAAEmE,OAAO,CAACnE,MAAM;MACtBd,MAAM,EAAEiF,OAAO,CAACjF,MAAM;MACtBe,UAAU,EAAE,EAAAiK,oBAAA,GAAA/F,OAAO,CAAClE,UAAU,cAAAiK,oBAAA,uBAAlBA,oBAAA,CAAoB5F,GAAG,KAAIH,OAAO,CAAClE,UAAU,IAAI,EAAE;MAAE;MACjEC,UAAU,EAAEiE,OAAO,CAACjE,UAAU,IAAI,KAAK;MACvCC,SAAS,EAAEgE,OAAO,CAAChE,SAAS,IAAI,EAAE;MAClCC,UAAU,EAAE+D,OAAO,CAAC/D,UAAU,IAAI;IACpC,CAAC,CAAC;IACFP,gBAAgB,CAAC,IAAI,CAAC;;IAEtB;IACAsF,gBAAgB,CAAChB,OAAO,CAACG,GAAG,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMwG,oBAAoB,GAAGA,CAAA,KAAM;IACjCjL,gBAAgB,CAAC,KAAK,CAAC;IACvBd,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMgM,gBAAgB,GAAIvF,CAAC,IAAK;IAC9B,MAAM;MAAEU,IAAI;MAAEM;IAAM,CAAC,GAAGhB,CAAC,CAACG,MAAM;IAEhC5F,WAAW,CAACwG,IAAI,IAAI;MAClB,MAAMyE,QAAQ,GAAG;QAAE,GAAGzE,IAAI;QAAE,CAACL,IAAI,GAAGM;MAAM,CAAC;;MAE3C;MACA,IAAIN,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,SAAS,EAAE;QAC9C;QACA,MAAMjO,SAAS,GAAGiO,IAAI,KAAK,WAAW,GAAGM,KAAK,GAAGD,IAAI,CAACtO,SAAS;QAC/D,MAAME,OAAO,GAAG+N,IAAI,KAAK,SAAS,GAAGM,KAAK,GAAGD,IAAI,CAACpO,OAAO;QACzD,MAAM8S,SAAS,GAAG,IAAIzS,IAAI,CAACP,SAAS,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,IAAIF,IAAI,CAACL,OAAO,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;;QAE/F;QACA,IAAIuS,SAAS,EAAE;UACb,IAAI1E,IAAI,CAACrO,WAAW,KAAK,UAAU,EAAE;YACnC8S,QAAQ,CAAC5S,SAAS,GAAG,UAAU;UACjC,CAAC,MAAM,IAAImO,IAAI,CAACrO,WAAW,KAAK,WAAW,EAAE;YAC3C8S,QAAQ,CAAC5S,SAAS,GAAG,WAAW;UAClC;QACF;MACF;MAEA,OAAO4S,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAG,MAAO1F,CAAC,IAAK;IACpCA,CAAC,CAAC8D,cAAc,CAAC,CAAC;IAElB,IAAI;MACF;MACA,MAAM6B,YAAY,GAAG;QAAE,GAAGrL;MAAS,CAAC;;MAEpC;MACA,IAAItD,IAAI,CAAC0F,IAAI,KAAK,aAAa,EAAE;QAC/B;QACA,OAAOiJ,YAAY,CAACjM,MAAM;QAC1B,OAAOiM,YAAY,CAACjL,UAAU;QAC9B,OAAOiL,YAAY,CAAChL,SAAS;QAC7B,OAAOgL,YAAY,CAAC/K,UAAU;MAChC;MAEA2B,OAAO,CAACC,GAAG,CAAC,uCAAuClD,eAAe,CAACwF,GAAG,EAAE,EAAE6G,YAAY,CAAC;;MAEvF;MACA,MAAM9F,QAAQ,GAAG,MAAMrO,GAAG,CAACuS,GAAG,CAC5B,mBAAmBzK,eAAe,CAACwF,GAAG,EAAE,EACxC6G,YACF,CAAC;;MAED;MACA,MAAMC,cAAc,GAAG/F,QAAQ,CAACpD,IAAI;;MAEpC;MACAnF,gBAAgB,CAACyJ,IAAI,IAAI;QACvB;QACA,MAAMoD,iBAAiB,GAAGpD,IAAI,CAACxJ,UAAU,CAACf,GAAG,CAACyN,GAAG,IAC/CA,GAAG,CAACnF,GAAG,KAAK8G,cAAc,CAAC9G,GAAG,GAAG8G,cAAc,GAAG3B,GACpD,CAAC;;QAED;QACA,MAAMD,mBAAmB,GAAGjD,IAAI,CAACvJ,YAAY,CAAChB,GAAG,CAACyN,GAAG,IACnDA,GAAG,CAACnF,GAAG,KAAK8G,cAAc,CAAC9G,GAAG,GAAG8G,cAAc,GAAG3B,GACpD,CAAC;;QAED;QACA,MAAMC,kBAAkB,GAAGnD,IAAI,CAACtJ,WAAW,CAACjB,GAAG,CAACyN,GAAG,IACjDA,GAAG,CAACnF,GAAG,KAAK8G,cAAc,CAAC9G,GAAG,GAAG8G,cAAc,GAAG3B,GACpD,CAAC;QAED,OAAO;UACL1M,UAAU,EAAE4M,iBAAiB;UAC7B3M,YAAY,EAAEwM,mBAAmB;UACjCvM,WAAW,EAAEyM;QACf,CAAC;MACH,CAAC,CAAC;;MAEF;MACA,MAAMG,cAAc,GAAGvH,kBAAkB,CAACzF,aAAa,CAACE,UAAU,CAACf,GAAG,CAACyN,GAAG,IACxEA,GAAG,CAACnF,GAAG,KAAK8G,cAAc,CAAC9G,GAAG,GAAG8G,cAAc,GAAG3B,GACpD,CAAC,CAAC;MACFtM,kBAAkB,CAAC0M,cAAc,CAAC;;MAElC;MACAjJ,iBAAiB,CAAC,sCAAsC,CAAC;MACzDE,mBAAmB,CAAC,IAAI,CAAC;MACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAElDgK,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOvI,GAAG,EAAE;MAAA,IAAA8I,cAAA,EAAAC,mBAAA;MACZvJ,OAAO,CAACzE,KAAK,CAAC,qCAAqC,EAAEiF,GAAG,CAAC;MACzDhF,QAAQ,CAAC,6DAA6D,CAAC;MACvE;MACAkK,KAAK,CAAC,QAAQ,EAAA4D,cAAA,GAAA9I,GAAG,CAAC8C,QAAQ,cAAAgG,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcpJ,IAAI,cAAAqJ,mBAAA,uBAAlBA,mBAAA,CAAoBtB,GAAG,KAAIzH,GAAG,CAACK,OAAO,EAAE,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAM2I,qBAAqB,GAAIpH,OAAO,IAAK;IACzC;IACA,MAAMqG,OAAO,GAAGhO,IAAI,IAAIA,IAAI,CAAC0F,IAAI,KAAK,aAAa;;IAEnD;IACA,IAAIuI,OAAO,GAAG,KAAK;;IAEnB;IACA,IAAItG,OAAO,CAACuG,QAAQ,IAAIvG,OAAO,CAACuG,QAAQ,CAACpG,GAAG,KAAK9H,IAAI,CAAC8H,GAAG,EAAE;MACzDmG,OAAO,GAAG,IAAI;IAChB;IACA;IAAA,KACK,IAAItG,OAAO,CAACwG,UAAU,KAAKnO,IAAI,CAAC8H,GAAG,EAAE;MACxCmG,OAAO,GAAG,IAAI;IAChB;IACA;IAAA,KACK,IAAItG,OAAO,CAACuG,QAAQ,IAAIvG,OAAO,CAACuG,QAAQ,CAACrG,EAAE,KAAK7H,IAAI,CAAC8H,GAAG,EAAE;MAC7DmG,OAAO,GAAG,IAAI;IAChB;IACA;IAAA,KACK,IAAItG,OAAO,CAACyG,SAAS,KAAKpO,IAAI,CAAC8H,GAAG,EAAE;MACvCmG,OAAO,GAAG,IAAI;IAChB;;IAEA;IACA1I,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAExF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8H,GAAG,CAAC;IAC3CvC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEmC,OAAO,CAAC;IACzCpC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyI,OAAO,CAAC;IAE1C,MAAMI,SAAS,GAAG1G,OAAO,CAACjF,MAAM,KAAK,SAAS;;IAE9C;IACA;IACA;IACA,IAAIsL,OAAO,EAAE;MACX;IAAA,CACD,MAAM,IAAIC,OAAO,IAAII,SAAS,EAAE;MAC/B;IAAA,CACD,MAAM;MACL;MACA,IAAI,CAACJ,OAAO,EAAE;QACZhD,KAAK,CAAC,8DAA8D,CAAC;MACvE,CAAC,MAAM,IAAI,CAACoD,SAAS,EAAE;QACrBpD,KAAK,CAAC,wDAAwD,CAAC;MACjE,CAAC,MAAM;QACLA,KAAK,CAAC,gCAAgC,CAAC;MACzC;MACA;IACF;IAEA/G,kBAAkB,CAACyD,OAAO,CAAC;IAC3B3D,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMgL,sBAAsB,GAAGA,CAAA,KAAM;IACnChL,kBAAkB,CAAC,KAAK,CAAC;IACzBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM+K,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF1J,OAAO,CAACC,GAAG,CAAC,kCAAkCvB,eAAe,CAAC6D,GAAG,EAAE,CAAC;;MAEpE;MACA,MAAMtN,GAAG,CAAC+R,MAAM,CAAC,mBAAmBtI,eAAe,CAAC6D,GAAG,EAAE,CAAC;;MAE1D;MACAxH,gBAAgB,CAACyJ,IAAI,KAAK;QACxBxJ,UAAU,EAAEwJ,IAAI,CAACxJ,UAAU,CAAC+I,MAAM,CAAC2D,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAK7D,eAAe,CAAC6D,GAAG,CAAC;QAC1EtH,YAAY,EAAEuJ,IAAI,CAACvJ,YAAY,CAAC8I,MAAM,CAAC2D,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAK7D,eAAe,CAAC6D,GAAG,CAAC;QAC9ErH,WAAW,EAAEsJ,IAAI,CAACtJ,WAAW,CAAC6I,MAAM,CAAC2D,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAK7D,eAAe,CAAC6D,GAAG;MAC7E,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMuF,cAAc,GAAGvH,kBAAkB,CACvCzF,aAAa,CAACE,UAAU,CAAC+I,MAAM,CAAC2D,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAK7D,eAAe,CAAC6D,GAAG,CACxE,CAAC;MACDnH,kBAAkB,CAAC0M,cAAc,CAAC;;MAElC;MACAjJ,iBAAiB,CAAC,iCAAiC,CAAC;MACpDE,mBAAmB,CAAC,IAAI,CAAC;MACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAElD0K,sBAAsB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOjJ,GAAG,EAAE;MAAA,IAAAmJ,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,qBAAA;MACZ9J,OAAO,CAACzE,KAAK,CAAC,gCAAgC,EAAEiF,GAAG,CAAC;;MAEpD;MACA,IAAI,EAAAmJ,cAAA,GAAAnJ,GAAG,CAAC8C,QAAQ,cAAAqG,cAAA,uBAAZA,cAAA,CAAcxM,MAAM,MAAK,GAAG,KAAAyM,cAAA,GAAIpJ,GAAG,CAAC8C,QAAQ,cAAAsG,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAc1J,IAAI,cAAA2J,mBAAA,gBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBtO,KAAK,cAAAuO,qBAAA,eAAzBA,qBAAA,CAA2BhH,QAAQ,CAAC,gCAAgC,CAAC,EAAE;QACzG;QACA/H,gBAAgB,CAACyJ,IAAI,KAAK;UACxBxJ,UAAU,EAAEwJ,IAAI,CAACxJ,UAAU,CAAC+I,MAAM,CAAC2D,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAK7D,eAAe,CAAC6D,GAAG,CAAC;UAC1EtH,YAAY,EAAEuJ,IAAI,CAACvJ,YAAY,CAAC8I,MAAM,CAAC2D,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAK7D,eAAe,CAAC6D,GAAG,CAAC;UAC9ErH,WAAW,EAAEsJ,IAAI,CAACtJ,WAAW,CAAC6I,MAAM,CAAC2D,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAK7D,eAAe,CAAC6D,GAAG;QAC7E,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMuF,cAAc,GAAGvH,kBAAkB,CACvCzF,aAAa,CAACE,UAAU,CAAC+I,MAAM,CAAC2D,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAK7D,eAAe,CAAC6D,GAAG,CACxE,CAAC;QACDnH,kBAAkB,CAAC0M,cAAc,CAAC;;QAElC;QACAjJ,iBAAiB,CAAC,oDAAoD,CAAC;QACvEE,mBAAmB,CAAC,IAAI,CAAC;QACzBgD,UAAU,CAAC,MAAMhD,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;QAElD0K,sBAAsB,CAAC,CAAC;MAC1B,CAAC,MAAM;QAAA,IAAAM,cAAA,EAAAC,mBAAA;QACLxO,QAAQ,CAAC,wDAAwD,CAAC;QAClE;QACAkK,KAAK,CAAC,QAAQ,EAAAqE,cAAA,GAAAvJ,GAAG,CAAC8C,QAAQ,cAAAyG,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7J,IAAI,cAAA8J,mBAAA,uBAAlBA,mBAAA,CAAoB/B,GAAG,KAAIzH,GAAG,CAACK,OAAO,EAAE,CAAC;MACzD;IACF;EACF,CAAC;EAED,MAAMoJ,cAAc,GAAI9M,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAOrH,OAAA,CAACzB,KAAK;UAAC6V,EAAE,EAAC,SAAS;UAAAxQ,QAAA,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAC9C,KAAK,UAAU;QACb,oBAAOlE,OAAA,CAACzB,KAAK;UAAC6V,EAAE,EAAC,SAAS;UAAAxQ,QAAA,EAAC;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAC7C,KAAK,UAAU;QACb,oBAAOlE,OAAA,CAACzB,KAAK;UAAC6V,EAAE,EAAC,QAAQ;UAAAxQ,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAC9C,KAAK,WAAW;QACd,oBAAOlE,OAAA,CAACzB,KAAK;UAAC6V,EAAE,EAAC,WAAW;UAAAxQ,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAC7C;QACE,oBAAOlE,OAAA,CAACzB,KAAK;UAAC6V,EAAE,EAAC,WAAW;UAAAxQ,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMmQ,kBAAkB,GAAI/H,OAAO,IAAK;IACtC,IAAIA,OAAO,IAAIA,OAAO,CAACM,cAAc,EAAE;MACrC;MACA,MAAM0H,UAAU,GAAGhI,OAAO,CAACG,GAAG,IAAKH,OAAO,CAACH,QAAQ,IAAIG,OAAO,CAACH,QAAQ,CAAC5B,MAAM,GAAG,CAAC,IAAI+B,OAAO,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACM,GAAI;MAE9G,IAAI,CAAC6H,UAAU,EAAE;QACfpK,OAAO,CAACqK,IAAI,CAAC,mCAAmC,EAAEjI,OAAO,CAAC;QAC1D,oBACEtM,OAAA,CAACzB,KAAK;UAAC6V,EAAE,EAAC,WAAW;UAACI,KAAK,EAAC,gDAA6B;UAAA5Q,QAAA,gBACvD5D,OAAA,CAACP,MAAM;YAACkE,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC,EAACoI,OAAO,CAACO,eAAe;QAAA;UAAA9I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAEZ;MAEA,oBACElE,OAAA,CAAClB,cAAc;QACb2V,SAAS,EAAC,KAAK;QACfC,OAAO,eAAE1U,OAAA,CAACnB,OAAO;UAAA+E,QAAA,GAAC,MAAI,EAAC0I,OAAO,CAACO,eAAe,EAAC,2BAAc;QAAA;UAAA9I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAE;QAAAN,QAAA,eAExE5D,OAAA,CAACxB,MAAM;UACLmW,OAAO,EAAC,cAAc;UACtBxG,IAAI,EAAC,IAAI;UACTxK,SAAS,EAAC,+CAA+C;UACzDE,OAAO,EAAEA,CAAA,KAAM;YACb;YACA,IAAI+Q,YAAY,GAAG;cAAC,GAAGtI;YAAO,CAAC;;YAE/B;YACA,IAAI,CAACsI,YAAY,CAACnI,GAAG,IAAImI,YAAY,CAACzI,QAAQ,IAAIyI,YAAY,CAACzI,QAAQ,CAAC5B,MAAM,GAAG,CAAC,EAAE;cAClFqK,YAAY,CAACnI,GAAG,GAAGmI,YAAY,CAACzI,QAAQ,CAAC,CAAC,CAAC,CAACM,GAAG;YACjD;;YAEA;YACA0E,uBAAuB,CAACyD,YAAY,CAAC;UACvC,CAAE;UAAAhR,QAAA,gBAEF5D,OAAA,CAACP,MAAM;YAACkE,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC,EAACoI,OAAO,CAACO,eAAe;QAAA;UAAA9I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAErB;IACA,oBAAOlE,OAAA;MAAM2D,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC9C,CAAC;;EAED;EACA,MAAM2Q,2BAA2B,GAAIvI,OAAO,IAAK;IAC/C,IAAIA,OAAO,IAAIA,OAAO,CAACM,cAAc,EAAE;MACrC;MACA,MAAM0H,UAAU,GAAGhI,OAAO,CAACG,GAAG,IAAKH,OAAO,CAACH,QAAQ,IAAIG,OAAO,CAACH,QAAQ,CAAC5B,MAAM,GAAG,CAAC,IAAI+B,OAAO,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACM,GAAI;MAE9G,IAAI,CAAC6H,UAAU,EAAE;QACfpK,OAAO,CAACqK,IAAI,CAAC,mCAAmC,EAAEjI,OAAO,CAAC;QAC1D,oBACEtM,OAAA,CAACzB,KAAK;UAAC6V,EAAE,EAAC,WAAW;UAACI,KAAK,EAAC,gDAA6B;UAAA5Q,QAAA,gBACvD5D,OAAA,CAACP,MAAM;YAACkE,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC,EAACoI,OAAO,CAACO,eAAe;QAAA;UAAA9I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAEZ;MAEA,oBACElE,OAAA,CAAClB,cAAc;QACb2V,SAAS,EAAC,KAAK;QACfC,OAAO,eAAE1U,OAAA,CAACnB,OAAO;UAAA+E,QAAA,GAAC,MAAI,EAAC0I,OAAO,CAACO,eAAe,EAAC,2BAAc;QAAA;UAAA9I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAE;QAAAN,QAAA,eAExE5D,OAAA,CAACxB,MAAM;UACLmW,OAAO,EAAC,cAAc;UACtBxG,IAAI,EAAC,IAAI;UACTxK,SAAS,EAAC,+CAA+C;UACzDE,OAAO,EAAEA,CAAA,KAAM;YACb;YACA,IAAI+Q,YAAY,GAAG;cAAC,GAAGtI;YAAO,CAAC;;YAE/B;YACA,IAAI,CAACsI,YAAY,CAACnI,GAAG,IAAImI,YAAY,CAACzI,QAAQ,IAAIyI,YAAY,CAACzI,QAAQ,CAAC5B,MAAM,GAAG,CAAC,EAAE;cAClFqK,YAAY,CAACnI,GAAG,GAAGmI,YAAY,CAACzI,QAAQ,CAAC,CAAC,CAAC,CAACM,GAAG;YACjD;;YAEA;YACA4E,yBAAyB,CAACuD,YAAY,CAAC;UACzC,CAAE;UAAAhR,QAAA,gBAEF5D,OAAA,CAACP,MAAM;YAACkE,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC,EAACoI,OAAO,CAACO,eAAe;QAAA;UAAA9I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAErB;IACA,oBAAOlE,OAAA;MAAM2D,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC9C,CAAC;EAED,MAAM1B,UAAU,GAAI+P,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAE1B,IAAI;MACF;MACA,MAAMxR,IAAI,GAAG,IAAIJ,IAAI,CAAC4R,UAAU,CAAC;;MAEjC;MACA;MACA,MAAMuC,IAAI,GAAG/T,IAAI,CAACyR,WAAW,CAAC,CAAC;MAC/B,MAAMuC,KAAK,GAAGhU,IAAI,CAAC0R,QAAQ,CAAC,CAAC;MAC7B,MAAMuC,GAAG,GAAGjU,IAAI,CAACO,OAAO,CAAC,CAAC;;MAE1B;MACA,MAAM2T,YAAY,GAAG,IAAItU,IAAI,CAACmU,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;MAEzD9K,OAAO,CAACC,GAAG,CAAC,aAAaoI,UAAU,oBAAoB0C,YAAY,CAACC,WAAW,CAAC,CAAC,EAAE,CAAC;;MAEpF;MACA,OAAO7V,MAAM,CAAC4V,YAAY,EAAE,YAAY,EAAE;QAAExS,MAAM,EAAEnD;MAAG,CAAC,CAAC;IAC3D,CAAC,CAAC,OAAOmG,KAAK,EAAE;MACdyE,OAAO,CAACzE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAE8M,UAAU,CAAC;MACxD,OAAO,mBAAmB;IAC5B;EACF,CAAC;;EAED;EACA,MAAMhQ,eAAe,GAAGA,CAACnC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,KAAK;IACtE,MAAMG,KAAK,GAAG8B,UAAU,CAACpC,SAAS,CAAC;IACnC,MAAMQ,GAAG,GAAG4B,UAAU,CAAClC,OAAO,CAAC;IAE/B,MAAM6U,SAAS,GAAG9U,WAAW,IAAIA,WAAW,KAAK,UAAU,GACvD,GAAGK,KAAK,KAAKL,WAAW,KAAK,SAAS,GAAG,MAAM,GAAG,OAAO,GAAG,GAC5DK,KAAK;IAET,MAAM0U,OAAO,GAAG7U,SAAS,IAAIA,SAAS,KAAK,UAAU,GACjD,GAAGK,GAAG,KAAKL,SAAS,KAAK,SAAS,GAAG,MAAM,GAAG,OAAO,GAAG,GACxDK,GAAG;IAEP,OAAO,GAAGuU,SAAS,MAAMC,OAAO,EAAE;EACpC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAI1I,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,IAAIA,WAAW,CAACpC,MAAM,KAAK,CAAC,EAAE,OAAO,eAAe;IAEpE,IAAIoC,WAAW,CAACpC,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAOoC,WAAW,CAAC,CAAC,CAAC,CAACO,QAAQ,IAAI,eAAe;IACnD;IAEA,oBACElN,OAAA,CAAClB,cAAc;MACb2V,SAAS,EAAC,OAAO;MACjBC,OAAO,eACL1U,OAAA,CAACnB,OAAO;QAAC2N,EAAE,EAAC,qBAAqB;QAAA5I,QAAA,EAC9B+I,WAAW,CAACxI,GAAG,CAACmR,GAAG,IAAIA,GAAG,CAACpI,QAAQ,CAAC,CAACoB,IAAI,CAAC,IAAI;MAAC;QAAAvK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACV;MAAAN,QAAA,eAED5D,OAAA;QAAA4D,QAAA,GACG+I,WAAW,CAAC,CAAC,CAAC,CAACO,QAAQ,EAAC,SAAI,EAACP,WAAW,CAACpC,MAAM,GAAG,CAAC,EAAC,UACvD;MAAA;QAAAxG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAErB,CAAC;EAED,MAAMqR,uBAAuB,GAAI5H,CAAC,IAAK;IACrCnG,eAAe,CAAC;MACd,GAAGD,YAAY;MACf,CAACoG,CAAC,CAACG,MAAM,CAACO,IAAI,GAAGV,CAAC,CAACG,MAAM,CAACa;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6G,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhO,eAAe,CAAC;MACdC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBL,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMoO,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAACzQ,aAAa,CAACI,WAAW,IAAIJ,aAAa,CAACI,WAAW,CAACmF,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;IACX;IAEA,MAAMmL,WAAW,GAAG,IAAI/U,IAAI,CAAC,CAAC,CAAC6R,WAAW,CAAC,CAAC;IAC5C,MAAMmD,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAErC7Q,aAAa,CAACI,WAAW,CAACiH,OAAO,CAACC,OAAO,IAAI;MAAA,IAAAwJ,iBAAA,EAAAC,kBAAA;MAC3C,MAAMC,WAAW,GAAG,IAAIrV,IAAI,CAAC2L,OAAO,CAACI,SAAS,CAAC,CAAC8F,WAAW,CAAC,CAAC;MAC7D,MAAMyD,YAAY,GAAG,IAAItV,IAAI,CAAC2L,OAAO,CAACI,SAAS,CAAC,CAAC+F,QAAQ,CAAC,CAAC;MAC3D,MAAMK,UAAU,IAAAgD,iBAAA,GAAGxJ,OAAO,CAACuG,QAAQ,cAAAiD,iBAAA,uBAAhBA,iBAAA,CAAkBrJ,GAAG;MACxC,MAAMhF,YAAY,GAAG,EAAAsO,kBAAA,GAAAzJ,OAAO,CAACuG,QAAQ,cAAAkD,kBAAA,uBAAhBA,kBAAA,CAAkB7I,QAAQ,KAAI,eAAe;MAElE,IAAI,CAAC4F,UAAU,IAAIkD,WAAW,KAAKN,WAAW,EAAE;;MAEhD;MACA;MACA,MAAMQ,SAAS,GAAG,GAAGpD,UAAU,IAAIxG,OAAO,CAAClM,SAAS,IAAIkM,OAAO,CAAChM,OAAO,IAAIgM,OAAO,CAACjM,WAAW,IAAI,EAAE,IAAIiM,OAAO,CAAC/L,SAAS,IAAI,EAAE,EAAE;MAEjI,IAAIqV,iBAAiB,CAACO,GAAG,CAACD,SAAS,CAAC,EAAE;QACpC,OAAO,CAAC;MACV;MACAN,iBAAiB,CAACQ,GAAG,CAACF,SAAS,CAAC;MAEhC,IAAI,CAACP,aAAa,CAAC7C,UAAU,CAAC,EAAE;QAC9B6C,aAAa,CAAC7C,UAAU,CAAC,GAAG;UAC1BrL,YAAY;UACZ4O,aAAa,EAAE,CAAC;UAChBC,SAAS,EAAE,CAAC;UACZC,eAAe,EAAE,CAAC,CAAC;UACnBC,gBAAgB,EAAE,CAAC;UACnBC,eAAe,EAAE,CAAC;UAClBC,gBAAgB,EAAE;QACpB,CAAC;MACH;MAEA,MAAMC,KAAK,GAAGhB,aAAa,CAAC7C,UAAU,CAAC;MACvC6D,KAAK,CAACN,aAAa,EAAE;;MAErB;MACA,MAAMO,SAAS,GAAGzW,sBAAsB,CACtCmM,OAAO,CAAClM,SAAS,EACjBkM,OAAO,CAACjM,WAAW,EACnBiM,OAAO,CAAChM,OAAO,EACfgM,OAAO,CAAC/L,SACV,CAAC;MACDoW,KAAK,CAACL,SAAS,IAAIM,SAAS;;MAE5B;MACA,MAAMC,QAAQ,GAAG,GAAGb,WAAW,IAAIC,YAAY,EAAE;MACjDU,KAAK,CAACJ,eAAe,CAACM,QAAQ,CAAC,GAAG,CAACF,KAAK,CAACJ,eAAe,CAACM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;MAE5E;MACA,IAAIvK,OAAO,CAACjF,MAAM,KAAK,UAAU,EAAEsP,KAAK,CAACH,gBAAgB,EAAE,CAAC,KACvD,IAAIlK,OAAO,CAACjF,MAAM,KAAK,SAAS,EAAEsP,KAAK,CAACF,eAAe,EAAE,CAAC,KAC1D,IAAInK,OAAO,CAACjF,MAAM,KAAK,UAAU,EAAEsP,KAAK,CAACD,gBAAgB,EAAE;IAClE,CAAC,CAAC;IAEF,OAAOvJ,MAAM,CAACC,MAAM,CAACuI,aAAa,CAAC,CAACmB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACtP,YAAY,CAACwP,aAAa,CAACD,CAAC,CAACvP,YAAY,CAAC,CAAC;EAClG,CAAC;;EAED;EACA,MAAMyP,iBAAiB,GAAIvB,aAAa,IAAK;IAC3C,MAAMwB,YAAY,GAAG,GAAG,IAAIxW,IAAI,CAAC,CAAC,CAAC6R,WAAW,CAAC,CAAC,IAAI,IAAI7R,IAAI,CAAC,CAAC,CAAC8R,QAAQ,CAAC,CAAC,EAAE;IAC3E,OAAO,CAACkD,aAAa,CAACY,eAAe,CAACY,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;EAChE,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIzB,aAAa,IAAK;IAC1C,OAAOA,aAAa,CAACU,aAAa,IAAI,EAAE;EAC1C,CAAC;EAED,MAAMrK,kBAAkB,GAAIqL,OAAO,IAAK;IACtC;IACA,IAAI,CAACrS,aAAa,CAACI,WAAW,IAAIJ,aAAa,CAACI,WAAW,CAACmF,MAAM,KAAK,CAAC,EAAE;MACxEL,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,OAAO,EAAE;IACX;IAEA,OAAOnF,aAAa,CAACI,WAAW,CAAC6I,MAAM,CAAC3B,OAAO,IAAI;MAAA,IAAAgL,kBAAA,EAAAC,oBAAA;MACjD;MACA,MAAMC,gBAAgB,GAAG,EAAAF,kBAAA,GAAAhL,OAAO,CAACuG,QAAQ,cAAAyE,kBAAA,uBAAhBA,kBAAA,CAAkBpK,QAAQ,KAAI,EAAE;MACzD,MAAMuK,kBAAkB,GAAG,EAAAF,oBAAA,GAAAjL,OAAO,CAAClE,UAAU,cAAAmP,oBAAA,uBAAlBA,oBAAA,CAAoBrK,QAAQ,KAAI,EAAE;MAE7D,MAAMwK,eAAe,GAAGL,OAAO,CAAC5P,YAAY,GAC1C+P,gBAAgB,CAACtI,WAAW,CAAC,CAAC,CAAClC,QAAQ,CAACqK,OAAO,CAAC5P,YAAY,CAACyH,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;MAEpF,MAAMyI,iBAAiB,GAAGN,OAAO,CAAC3P,cAAc,GAC9C+P,kBAAkB,CAACvI,WAAW,CAAC,CAAC,CAAClC,QAAQ,CAACqK,OAAO,CAAC3P,cAAc,CAACwH,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;MAExF,MAAM0I,aAAa,GAAGP,OAAO,CAAChQ,MAAM,GAAGiF,OAAO,CAACjF,MAAM,KAAKgQ,OAAO,CAAChQ,MAAM,GAAG,IAAI;MAE/E,OAAOqQ,eAAe,IAAIC,iBAAiB,IAAIC,aAAa;IAC9D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,wBAAwB,GAAIC,OAAO,IAAK;IAC5C;IACA,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACvN,MAAM,KAAK,CAAC,EAAE;MACpCL,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,OAAO,EAAE;IACX;IAEA,MAAM4N,eAAe,GAAG/L,kBAAkB,CAACzE,YAAY,CAAC;IACxD2C,OAAO,CAACC,GAAG,CAAC,6BAA6B4N,eAAe,CAACxN,MAAM,EAAE,CAAC;IAElE,MAAMyN,UAAU,GAAG,CAACrQ,gBAAgB,GAAG,CAAC,IAAIE,mBAAmB;IAC/D,MAAMoQ,QAAQ,GAAGD,UAAU,GAAGnQ,mBAAmB;IACjD,OAAOkQ,eAAe,CAACjM,KAAK,CAACkM,UAAU,EAAEC,QAAQ,CAAC;EACpD,CAAC;EAED,IAAI,CAACtT,IAAI,EAAE;IACT,oBACE3E,OAAA,CAAC9B,SAAS;MAACyF,SAAS,EAAC,MAAM;MAAAC,QAAA,eACzB5D,OAAA,CAAC3B,IAAI;QAAAuF,QAAA,eACH5D,OAAA,CAAC3B,IAAI,CAAC6Z,IAAI;UAACvU,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAChC5D,OAAA;YAAA4D,QAAA,EAAG;UAA0C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjDlE,OAAA,CAACxB,MAAM;YAACmW,OAAO,EAAC,SAAS;YAAC9Q,OAAO,EAAEA,CAAA,KAAMkB,QAAQ,CAAC,QAAQ,CAAE;YAAAnB,QAAA,EAAC;UAE7D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEhB;EAEA,IAAIqB,OAAO,EAAE;IACX,oBACEvF,OAAA,CAAC9B,SAAS;MAACyF,SAAS,EAAC,MAAM;MAAAC,QAAA,eACzB5D,OAAA,CAAC3B,IAAI;QAAAuF,QAAA,eACH5D,OAAA,CAAC3B,IAAI,CAAC6Z,IAAI;UAACvU,SAAS,EAAC,aAAa;UAAAC,QAAA,eAChC5D,OAAA;YAAA4D,QAAA,EAAG;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEhB;EAEA,IAAIuB,KAAK,EAAE;IACT,oBACEzF,OAAA,CAAC9B,SAAS;MAACyF,SAAS,EAAC,MAAM;MAAAC,QAAA,eACzB5D,OAAA,CAAC3B,IAAI;QAAAuF,QAAA,eACH5D,OAAA,CAAC3B,IAAI,CAAC6Z,IAAI;UAACvU,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAChC5D,OAAA;YAAG2D,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE6B;UAAK;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtClE,OAAA,CAACxB,MAAM;YAACmW,OAAO,EAAC,SAAS;YAAC9Q,OAAO,EAAEA,CAAA,KAAMoM,MAAM,CAACkI,QAAQ,CAACC,MAAM,CAAC,CAAE;YAAAxU,QAAA,EAAC;UAEnE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEhB;EAEA,oBACElE,OAAA,CAAC9B,SAAS;IAACyF,SAAS,EAAC,0BAA0B;IAAAC,QAAA,GAE5CoF,gBAAgB,iBACfhJ,OAAA,CAAChB,KAAK;MACJ2V,OAAO,EAAC,SAAS;MACjBhR,SAAS,EAAC,uDAAuD;MACjE0U,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAK,CAAE;MACxBC,OAAO,EAAEA,CAAA,KAAMtP,mBAAmB,CAAC,KAAK,CAAE;MAC1CuP,WAAW;MAAA5U,QAAA,EAEVkF;IAAc;MAAA/E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACR,eACDlE,OAAA,CAAC7B,GAAG;MAACwF,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5D,OAAA,CAAC5B,GAAG;QAAAwF,QAAA,eACF5D,OAAA;UAAA4D,QAAA,EAAI;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACNlE,OAAA,CAAC5B,GAAG;QAACqa,EAAE,EAAC,MAAM;QAAA7U,QAAA,eACZ5D,OAAA,CAACxB,MAAM;UAACmW,OAAO,EAAC,SAAS;UAAC9Q,OAAO,EAAEwJ,mBAAoB;UAAAzJ,QAAA,EAAC;QAExD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlE,OAAA,CAACvB,IAAI;MAACia,gBAAgB,EAAC,aAAa;MAAC/U,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnD5D,OAAA,CAACtB,GAAG;QAACia,QAAQ,EAAC,aAAa;QAACnE,KAAK,EAAC,qDAA2B;QAAA5Q,QAAA,eAC3D5D,OAAA;UAAK2D,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC5D,OAAA;YAAK2D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B5D,OAAA;cAAIqY,KAAK,EAAE;gBAAEO,MAAM,EAAE,GAAG;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAnV,QAAA,gBAC/E5D,OAAA;gBAAG2D,SAAS,EAAC,qBAAqB;gBAAC0U,KAAK,EAAE;kBAAEW,WAAW,EAAE;gBAAM;cAAE;gBAAAjV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uDAExE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlE,OAAA;cAAM2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAC7ByB,eAAe,CAACkF,MAAM,EAAC,sCAC1B;YAAA;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNlE,OAAA;YAAK2D,SAAS,EAAC,sBAAsB;YAAC6I,EAAE,EAAC,qBAAqB;YAAA5I,QAAA,GAC3DiC,mBAAmB,CAAC0E,MAAM,KAAK,CAAC,gBAC/BvK,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5D,OAAA;gBAAK2D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChC5D,OAAA;kBAAG2D,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACNlE,OAAA;gBAAA4D,QAAA,EAAG;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,GAEN2B,mBAAmB,CAAC1B,GAAG,CAAC,CAACmI,OAAO,EAAEuC,KAAK,kBACrC7O,OAAA;cAAsB2D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACpD5D,OAAA;gBAAK2D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC5D,OAAA;kBAAK2D,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9B5D,OAAA;oBAAG2D,SAAS,EAAC;kBAAuB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACNlE,OAAA;kBAAK2D,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B5D,OAAA;oBAAA4D,QAAA,GAAI,mCAAe,EAACiL,KAAK,GAAG,CAAC;kBAAA;oBAAA9K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnClE,OAAA;oBAAA4D,QAAA,gBAAG5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAiB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,KAAC,EAAC1B,UAAU,CAAC8J,OAAO,CAACI,SAAS,CAAC;kBAAA;oBAAA3I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1ElE,OAAA;oBAAA4D,QAAA,gBAAG5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,KAAC,EAAC3B,eAAe,CAAC+J,OAAO,CAAClM,SAAS,EAAEkM,OAAO,CAACjM,WAAW,EAAEiM,OAAO,CAAChM,OAAO,EAAEgM,OAAO,CAAC/L,SAAS,CAAC;kBAAA;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrIlE,OAAA;oBAAA4D,QAAA,eAAG5D,OAAA;sBAAA4D,QAAA,EAASvB,cAAc,CAAClC,sBAAsB,CAACmM,OAAO,CAAClM,SAAS,EAAEkM,OAAO,CAACjM,WAAW,EAAEiM,OAAO,CAAChM,OAAO,EAAEgM,OAAO,CAAC/L,SAAS,CAAC;oBAAC;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlE,OAAA;gBAAK2D,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B5D,OAAA;kBAAA4D,QAAA,gBAAG5D,OAAA;oBAAG2D,SAAS,EAAC;kBAAiB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,KAAC,eAAAlE,OAAA;oBAAA4D,QAAA,EAAQ;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACmR,iBAAiB,CAAC/I,OAAO,CAACK,WAAW,CAAC;gBAAA;kBAAA5I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9GlE,OAAA;kBAAA4D,QAAA,gBAAG5D,OAAA;oBAAG2D,SAAS,EAAC;kBAAgB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,KAAC,eAAAlE,OAAA;oBAAA4D,QAAA,EAAQ;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACoI,OAAO,CAACnE,MAAM;gBAAA;kBAAApE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACjFoI,OAAO,CAAChF,QAAQ,iBACftH,OAAA;kBAAA4D,QAAA,gBAAG5D,OAAA;oBAAG2D,SAAS,EAAC;kBAAoB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,KAAC,eAAAlE,OAAA;oBAAA4D,QAAA,EAAQ;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACoI,OAAO,CAAChF,QAAQ;gBAAA;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC3F;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlE,OAAA;gBAAK2D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC5D,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA;oBAAG2D,SAAS,EAAC;kBAAoB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtClE,OAAA;oBAAA4D,QAAA,EAAQ;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACiQ,cAAc,CAAC7H,OAAO,CAACjF,MAAM,CAAC;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACJlE,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA;oBAAG2D,SAAS,EAAC;kBAAkB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpClE,OAAA;oBAAA4D,QAAA,EAAQ;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC2Q,2BAA2B,CAACvI,OAAO,CAAC;gBAAA;kBAAAvI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENlE,OAAA;gBAAK2D,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9B0I,OAAO,CAACjF,MAAM,KAAK,SAAS,IAAIiF,OAAO,CAACH,QAAQ,IAAIG,OAAO,CAACH,QAAQ,CAAC5B,MAAM,GAAG,CAAC,iBAC9EvK,OAAA,CAAAE,SAAA;kBAAA0D,QAAA,gBACE5D,OAAA;oBACE2D,SAAS,EAAC,UAAU;oBACpBE,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAMoV,eAAe,GAAG;wBACtB,GAAG3M,OAAO,CAACH,QAAQ,CAAC,CAAC,CAAC;wBACtB0G,QAAQ,EAAE;0BAAEpG,GAAG,EAAE9H,IAAI,CAAC8H,GAAG;0BAAES,QAAQ,EAAEvI,IAAI,CAACuI;wBAAS,CAAC;wBACpD4F,UAAU,EAAEnO,IAAI,CAAC8H,GAAG;wBACpBsG,SAAS,EAAEpO,IAAI,CAAC8H;sBAClB,CAAC;sBACD2F,mBAAmB,CAAC6G,eAAe,CAAC;oBACtC,CAAE;oBACFzE,KAAK,EAAC,mCAAe;oBAAA5Q,QAAA,gBAErB5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAa;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,wBACjC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlE,OAAA;oBACE2D,SAAS,EAAC,YAAY;oBACtBE,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAMoV,eAAe,GAAG;wBACtB,GAAG3M,OAAO,CAACH,QAAQ,CAAC,CAAC,CAAC;wBACtB0G,QAAQ,EAAE;0BAAEpG,GAAG,EAAE9H,IAAI,CAAC8H,GAAG;0BAAES,QAAQ,EAAEvI,IAAI,CAACuI;wBAAS,CAAC;wBACpD4F,UAAU,EAAEnO,IAAI,CAAC8H,GAAG;wBACpBsG,SAAS,EAAEpO,IAAI,CAAC8H;sBAClB,CAAC;sBACDiH,qBAAqB,CAACuF,eAAe,CAAC;oBACxC,CAAE;oBACFzE,KAAK,EAAC,sBAAS;oBACf6D,KAAK,EAAE;sBAAEa,UAAU,EAAE,SAAS;sBAAEL,KAAK,EAAE;oBAAQ,CAAE;oBAAAjV,QAAA,gBAEjD5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAkB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WACtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACT;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GApEEoI,OAAO,CAACE,EAAE;cAAAzI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqEf,CACN,CACF,EAGA+B,mBAAmB,iBAClBjG,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5D,OAAA;gBAAK2D,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BlE,OAAA;gBAAA4D,QAAA,EAAM;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN,EAGA2B,mBAAmB,CAAC0E,MAAM,GAAG,CAAC,IAAI1E,mBAAmB,CAAC0E,MAAM,IAAIlF,eAAe,CAACkF,MAAM,iBACrFvK,OAAA;cAAK2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B5D,OAAA;gBAAA4D,QAAA,GAAG,iDAAmB,EAACyB,eAAe,CAACkF,MAAM,EAAC,sCAAkB;cAAA;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlE,OAAA,CAACtB,GAAG;QAACia,QAAQ,EAAC,mBAAmB;QAACnE,KAAK,EAAC,mCAAe;QAAA5Q,QAAA,eACrD5D,OAAA;UAAK2D,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC5D,OAAA;YAAK2D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B5D,OAAA;cAAIqY,KAAK,EAAE;gBAAEO,MAAM,EAAE,GAAG;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAnV,QAAA,gBAC/E5D,OAAA;gBAAG2D,SAAS,EAAC,mBAAmB;gBAAC0U,KAAK,EAAE;kBAAEW,WAAW,EAAE;gBAAM;cAAE;gBAAAjV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qCAEtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlE,OAAA;cAAM2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAC7BoB,aAAa,CAACG,YAAY,CAACoF,MAAM,EAAC,oCACrC;YAAA;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNlE,OAAA;YAAK2D,SAAS,EAAC,sBAAsB;YAAC6I,EAAE,EAAC,2BAA2B;YAAA5I,QAAA,GACjEuC,yBAAyB,CAACoE,MAAM,KAAK,CAAC,gBACrCvK,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5D,OAAA;gBAAK2D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChC5D,OAAA;kBAAG2D,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACNlE,OAAA;gBAAA4D,QAAA,EAAG;cAAwC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,GAENiC,yBAAyB,CAAChC,GAAG,CAAC,CAACmI,OAAO,EAAEuC,KAAK;cAAA,IAAAsK,kBAAA,EAAAC,kBAAA;cAAA,oBAC3CpZ,OAAA;gBAAuB2D,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACrD5D,OAAA;kBAAK2D,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACjC5D,OAAA;oBAAK2D,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAC7B,CAAC,EAAAuV,kBAAA,GAAA7M,OAAO,CAACuG,QAAQ,cAAAsG,kBAAA,uBAAhBA,kBAAA,CAAkBjM,QAAQ,KAAIZ,OAAO,CAAC7E,YAAY,IAAI,KAAK,EAAE4R,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kBAAC;oBAAAvV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,eACNlE,OAAA;oBAAK2D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/B5D,OAAA;sBAAA4D,QAAA,EAAK,EAAAwV,kBAAA,GAAA9M,OAAO,CAACuG,QAAQ,cAAAuG,kBAAA,uBAAhBA,kBAAA,CAAkBlM,QAAQ,KAAIZ,OAAO,CAAC7E,YAAY,IAAI;oBAAe;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChFlE,OAAA;sBAAA4D,QAAA,gBAAG5D,OAAA;wBAAG2D,SAAS,EAAC;sBAAiB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,KAAC,EAAC1B,UAAU,CAAC8J,OAAO,CAACI,SAAS,CAAC;oBAAA;sBAAA3I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1ElE,OAAA;sBAAA4D,QAAA,gBAAG5D,OAAA;wBAAG2D,SAAS,EAAC;sBAAc;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,KAAC,EAAC3B,eAAe,CAAC+J,OAAO,CAAClM,SAAS,EAAEkM,OAAO,CAACjM,WAAW,EAAEiM,OAAO,CAAChM,OAAO,EAAEgM,OAAO,CAAC/L,SAAS,CAAC;oBAAA;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrIlE,OAAA;sBAAA4D,QAAA,eAAG5D,OAAA;wBAAA4D,QAAA,EAASvB,cAAc,CAAClC,sBAAsB,CAACmM,OAAO,CAAClM,SAAS,EAAEkM,OAAO,CAACjM,WAAW,EAAEiM,OAAO,CAAChM,OAAO,EAAEgM,OAAO,CAAC/L,SAAS,CAAC;sBAAC;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlE,OAAA;kBAAK2D,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/B5D,OAAA;oBAAA4D,QAAA,gBAAG5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAgB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,KAAC,eAAAlE,OAAA;sBAAA4D,QAAA,EAAQ;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoI,OAAO,CAACnE,MAAM;kBAAA;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eAENlE,OAAA;kBAAK2D,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACjC5D,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAoB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtClE,OAAA;sBAAA4D,QAAA,EAAQ;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACiQ,cAAc,CAAC7H,OAAO,CAACjF,MAAM,CAAC;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACJlE,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAkB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpClE,OAAA;sBAAA4D,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACmQ,kBAAkB,CAAC/H,OAAO,CAAC;kBAAA;oBAAAvI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENlE,OAAA;kBAAK2D,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC9B0I,OAAO,CAACjF,MAAM,KAAK,SAAS,iBAC3BrH,OAAA;oBACE2D,SAAS,EAAC,UAAU;oBACpBE,OAAO,EAAEA,CAAA,KAAMsN,uBAAuB,CAAC7E,OAAO,CAAE;oBAChD+L,KAAK,EAAE;sBAAEa,UAAU,EAAE,SAAS;sBAAEL,KAAK,EAAE;oBAAQ,CAAE;oBAAAjV,QAAA,gBAEjD5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAqB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,iCACzC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAtCEoI,OAAO,CAACG,GAAG;gBAAA1I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuChB,CAAC;YAAA,CACP,CACF,EAGAqC,yBAAyB,iBACxBvG,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5D,OAAA;gBAAK2D,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BlE,OAAA;gBAAA4D,QAAA,EAAM;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN,EAGAiC,yBAAyB,CAACoE,MAAM,GAAG,CAAC,IAAIpE,yBAAyB,CAACoE,MAAM,IAAIvF,aAAa,CAACG,YAAY,CAACoF,MAAM,iBAC5GvK,OAAA;cAAK2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B5D,OAAA;gBAAA4D,QAAA,GAAG,iDAAmB,EAACoB,aAAa,CAACG,YAAY,CAACoF,MAAM,EAAC,oCAAc;cAAA;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLS,IAAI,KAAKA,IAAI,CAAC0F,IAAI,KAAK,aAAa,IAAI1F,IAAI,CAAC0F,IAAI,KAAK,kBAAkB,CAAC,iBACxErK,OAAA,CAACtB,GAAG;QAACia,QAAQ,EAAC,cAAc;QAACnE,KAAK,EAAC,kDAAsB;QAAA5Q,QAAA,eACvD5D,OAAA;UAAK2D,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBAEvC5D,OAAA;YAAK2D,SAAS,EAAC,yBAAyB;YAAC0U,KAAK,EAAE;cAC9Ca,UAAU,EAAE,2CAA2C;cACvDL,KAAK,EAAE,OAAO;cACdU,OAAO,EAAE,MAAM;cACfC,YAAY,EAAE,MAAM;cACpBC,YAAY,EAAE,MAAM;cACpBC,SAAS,EAAE;YACb,CAAE;YAAA9V,QAAA,gBACA5D,OAAA;cAAIqY,KAAK,EAAE;gBAAEO,MAAM,EAAE,YAAY;gBAAEE,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAnV,QAAA,gBACzE5D,OAAA;gBAAG2D,SAAS,EAAC,kBAAkB;gBAAC0U,KAAK,EAAE;kBAAEW,WAAW,EAAE;gBAAO;cAAE;gBAAAjV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,6DAC1C,EAAC,IAAIvD,IAAI,CAAC,CAAC,CAAC6R,WAAW,CAAC,CAAC;YAAA;cAAAzO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,EAEJ,CAAC,MAAM;cACN,MAAMyS,KAAK,GAAGlB,wBAAwB,CAAC,CAAC;cACxC,MAAMkE,cAAc,GAAGhD,KAAK,CAACpM,MAAM;cACnC,MAAM8L,aAAa,GAAGM,KAAK,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACzD,aAAa,EAAE,CAAC,CAAC;cAC5E,MAAMC,SAAS,GAAGK,KAAK,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACxD,SAAS,EAAE,CAAC,CAAC;cACpE,MAAMyD,gBAAgB,GAAGpD,KAAK,CAAC1I,MAAM,CAAC6L,GAAG,IAAI1C,gBAAgB,CAAC0C,GAAG,CAAC,CAAC,CAACvP,MAAM;cAC1E,MAAMyP,uBAAuB,GAAGrD,KAAK,CAAC1I,MAAM,CAAC6L,GAAG,IAAI5C,iBAAiB,CAAC4C,GAAG,CAAC,CAAC,CAACvP,MAAM;cAElF,oBACEvK,OAAA;gBAAK2D,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB5D,OAAA;kBAAK2D,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACvB5D,OAAA;oBAAK2D,SAAS,EAAC,WAAW;oBAAC0U,KAAK,EAAE;sBAAEa,UAAU,EAAE,uBAAuB;sBAAEK,OAAO,EAAE,MAAM;sBAAEC,YAAY,EAAE,KAAK;sBAAES,SAAS,EAAE;oBAAS,CAAE;oBAAArW,QAAA,gBACnI5D,OAAA;sBAAKqY,KAAK,EAAE;wBAAE6B,QAAQ,EAAE,MAAM;wBAAEC,UAAU,EAAE;sBAAO,CAAE;sBAAAvW,QAAA,EAAE+V;oBAAc;sBAAA5V,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5ElE,OAAA;sBAAKqY,KAAK,EAAE;wBAAE6B,QAAQ,EAAE,MAAM;wBAAEE,OAAO,EAAE;sBAAM,CAAE;sBAAAxW,QAAA,EAAC;oBAAgB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlE,OAAA;kBAAK2D,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACvB5D,OAAA;oBAAK2D,SAAS,EAAC,WAAW;oBAAC0U,KAAK,EAAE;sBAAEa,UAAU,EAAE,uBAAuB;sBAAEK,OAAO,EAAE,MAAM;sBAAEC,YAAY,EAAE,KAAK;sBAAES,SAAS,EAAE;oBAAS,CAAE;oBAAArW,QAAA,gBACnI5D,OAAA;sBAAKqY,KAAK,EAAE;wBAAE6B,QAAQ,EAAE,MAAM;wBAAEC,UAAU,EAAE;sBAAO,CAAE;sBAAAvW,QAAA,EAAEyS;oBAAa;sBAAAtS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3ElE,OAAA;sBAAKqY,KAAK,EAAE;wBAAE6B,QAAQ,EAAE,MAAM;wBAAEE,OAAO,EAAE;sBAAM,CAAE;sBAAAxW,QAAA,EAAC;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlE,OAAA;kBAAK2D,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACvB5D,OAAA;oBAAK2D,SAAS,EAAC,WAAW;oBAAC0U,KAAK,EAAE;sBAAEa,UAAU,EAAE,uBAAuB;sBAAEK,OAAO,EAAE,MAAM;sBAAEC,YAAY,EAAE,KAAK;sBAAES,SAAS,EAAE;oBAAS,CAAE;oBAAArW,QAAA,gBACnI5D,OAAA;sBAAKqY,KAAK,EAAE;wBAAE6B,QAAQ,EAAE,MAAM;wBAAEC,UAAU,EAAE;sBAAO,CAAE;sBAAAvW,QAAA,EAAE0S;oBAAS;sBAAAvS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvElE,OAAA;sBAAKqY,KAAK,EAAE;wBAAE6B,QAAQ,EAAE,MAAM;wBAAEE,OAAO,EAAE;sBAAM,CAAE;sBAAAxW,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlE,OAAA;kBAAK2D,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACvB5D,OAAA;oBAAK2D,SAAS,EAAC,WAAW;oBAAC0U,KAAK,EAAE;sBAAEa,UAAU,EAAE,uBAAuB;sBAAEK,OAAO,EAAE,MAAM;sBAAEC,YAAY,EAAE,KAAK;sBAAES,SAAS,EAAE;oBAAS,CAAE;oBAAArW,QAAA,gBACnI5D,OAAA;sBAAKqY,KAAK,EAAE;wBAAE6B,QAAQ,EAAE,MAAM;wBAAEC,UAAU,EAAE,MAAM;wBAAEtB,KAAK,EAAEkB,gBAAgB,GAAG,CAAC,GAAG,SAAS,GAAG;sBAAO,CAAE;sBAAAnW,QAAA,EACpGmW;oBAAgB;sBAAAhW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC,eACNlE,OAAA;sBAAKqY,KAAK,EAAE;wBAAE6B,QAAQ,EAAE,MAAM;wBAAEE,OAAO,EAAE;sBAAM,CAAE;sBAAAxW,QAAA,EAAC;oBAAgB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEV,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAGL,CAAC,MAAM;YACN,MAAMyS,KAAK,GAAGlB,wBAAwB,CAAC,CAAC;YACxC,OAAOkB,KAAK,CAACpM,MAAM,GAAG,CAAC,iBACrBvK,OAAA;cAAK2D,SAAS,EAAC,wBAAwB;cAAC0U,KAAK,EAAE;gBAAEoB,YAAY,EAAE;cAAO,CAAE;cAAA7V,QAAA,gBACtE5D,OAAA;gBAAIqY,KAAK,EAAE;kBAAEoB,YAAY,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAO,CAAE;gBAAAjV,QAAA,gBACjD5D,OAAA;kBAAG2D,SAAS,EAAC,cAAc;kBAAC0U,KAAK,EAAE;oBAAEW,WAAW,EAAE;kBAAM;gBAAE;kBAAAjV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,sCAEjE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlE,OAAA;gBAAK2D,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/B5D,OAAA;kBAAO2D,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAChD5D,OAAA;oBAAO2D,SAAS,EAAC,YAAY;oBAAAC,QAAA,eAC3B5D,OAAA;sBAAA4D,QAAA,gBACE5D,OAAA;wBAAA4D,QAAA,EAAI;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClBlE,OAAA;wBAAA4D,QAAA,EAAI;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnBlE,OAAA;wBAAA4D,QAAA,EAAI;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvBlE,OAAA;wBAAA4D,QAAA,EAAI;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjBlE,OAAA;wBAAA4D,QAAA,EAAI;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClBlE,OAAA;wBAAA4D,QAAA,EAAI;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChBlE,OAAA;wBAAA4D,QAAA,EAAI;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRlE,OAAA;oBAAA4D,QAAA,EACG+S,KAAK,CAACxS,GAAG,CAAC,CAACkW,OAAO,EAAExL,KAAK,kBACxB7O,OAAA;sBAAA4D,QAAA,gBACE5D,OAAA;wBAAA4D,QAAA,eACE5D,OAAA;0BAAA4D,QAAA,EAASyW,OAAO,CAAC5S;wBAAY;0BAAA1D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC,eACLlE,OAAA;wBAAA4D,QAAA,eACE5D,OAAA;0BAAM2D,SAAS,EAAE,SAASyT,gBAAgB,CAACiD,OAAO,CAAC,GAAG,WAAW,GAAGA,OAAO,CAAChE,aAAa,IAAI,CAAC,GAAG,YAAY,GAAG,YAAY,EAAG;0BAAAzS,QAAA,GAC5HyW,OAAO,CAAChE,aAAa,EAAC,KACzB;wBAAA;0BAAAtS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACLlE,OAAA;wBAAA4D,QAAA,gBACE5D,OAAA;0BAAA4D,QAAA,EAASyW,OAAO,CAAC/D;wBAAS;0BAAAvS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC,YACtC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLlE,OAAA;wBAAA4D,QAAA,eACE5D,OAAA;0BAAM2D,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,EAAEyW,OAAO,CAAC7D;wBAAgB;0BAAAzS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE,CAAC,eACLlE,OAAA;wBAAA4D,QAAA,eACE5D,OAAA;0BAAM2D,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,EAAEyW,OAAO,CAAC5D;wBAAe;0BAAA1S,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE,CAAC,eACLlE,OAAA;wBAAA4D,QAAA,eACE5D,OAAA;0BAAM2D,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEyW,OAAO,CAAC3D;wBAAgB;0BAAA3S,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE,CAAC,eACLlE,OAAA;wBAAA4D,QAAA,GACGwT,gBAAgB,CAACiD,OAAO,CAAC,iBACxBra,OAAA;0BAAM2D,SAAS,EAAC,sBAAsB;0BAAAC,QAAA,gBACpC5D,OAAA;4BAAG2D,SAAS,EAAC;0BAA6B;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,8CACjD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP,EACAgT,iBAAiB,CAACmD,OAAO,CAAC,iBACzBra,OAAA;0BAAM2D,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,gBAChC5D,OAAA;4BAAG2D,SAAS,EAAC;0BAAuB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,8CAC3C;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP,EACA,CAACkT,gBAAgB,CAACiD,OAAO,CAAC,IAAI,CAACnD,iBAAiB,CAACmD,OAAO,CAAC,iBACxDra,OAAA;0BAAM2D,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,gBAChC5D,OAAA;4BAAG2D,SAAS,EAAC;0BAAc;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,6BAClC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA,GArCE2K,KAAK;sBAAA9K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAsCV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UACH,CAAC,EAAE,CAAC,EAGHc,aAAa,CAACI,WAAW,CAACmF,MAAM,KAAK,CAAC,iBACrCvK,OAAA;YAAK2D,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC5D,OAAA;cAAA4D,QAAA,EAAQ;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,4IAC7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,eAEDlE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5D,OAAA;cAAA4D,QAAA,EAAI;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBlE,OAAA;cAAK2D,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5D,OAAA;gBAAK2D,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B5D,OAAA;kBAAA4D,QAAA,EAAO;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBlE,OAAA;kBACEyO,IAAI,EAAC,MAAM;kBACXJ,IAAI,EAAC,cAAc;kBACnBM,KAAK,EAAEpH,YAAY,CAACE,YAAY,IAAI,EAAG;kBACvC6S,QAAQ,EAAE/E,uBAAwB;kBAClCgF,WAAW,EAAC;gBAAuB;kBAAAxW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlE,OAAA;gBAAK2D,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B5D,OAAA;kBAAA4D,QAAA,EAAO;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBlE,OAAA;kBACEyO,IAAI,EAAC,MAAM;kBACXJ,IAAI,EAAC,gBAAgB;kBACrBM,KAAK,EAAEpH,YAAY,CAACG,cAAc,IAAI,EAAG;kBACzC4S,QAAQ,EAAE/E,uBAAwB;kBAClCgF,WAAW,EAAC;gBAAqB;kBAAAxW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlE,OAAA;gBAAK2D,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B5D,OAAA;kBAAA4D,QAAA,EAAO;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BlE,OAAA;kBAAQqO,IAAI,EAAC,QAAQ;kBAACM,KAAK,EAAEpH,YAAY,CAACF,MAAO;kBAACiT,QAAQ,EAAE/E,uBAAwB;kBAAA3R,QAAA,gBAClF5D,OAAA;oBAAQ2O,KAAK,EAAC,EAAE;oBAAA/K,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3ClE,OAAA;oBAAQ2O,KAAK,EAAC,SAAS;oBAAA/K,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/ClE,OAAA;oBAAQ2O,KAAK,EAAC,UAAU;oBAAA/K,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1ClE,OAAA;oBAAQ2O,KAAK,EAAC,UAAU;oBAAA/K,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B5D,OAAA;gBACE2D,SAAS,EAAC,mBAAmB;gBAC7BE,OAAO,EAAE2R,iBAAkB;gBAAA5R,QAAA,EAC5B;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5D,OAAA;cAAA4D,QAAA,GAAG,mCAAa,eAAA5D,OAAA;gBAAA4D,QAAA,EAASoI,kBAAkB,CAACzE,YAAY,CAAC,CAACgD;cAAM;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,OAAG,EAACc,aAAa,CAACI,WAAW,CAACmF,MAAM;YAAA;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAElHqD,YAAY,CAACF,MAAM,iBAClBrH,OAAA;cAAA4D,QAAA,GAAG,qBAAW,eAAA5D,OAAA;gBAAA4D,QAAA,EAASuQ,cAAc,CAAC5M,YAAY,CAACF,MAAM,CAAC,CAACmT,KAAK,CAAC5W;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,MAAE,EAAC8H,kBAAkB,CAACzE,YAAY,CAAC,CAACgD,MAAM,EAAC,gBAAI;YAAA;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACtI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlE,OAAA;YAAK2D,SAAS,EAAC,sBAAsB;YAAC6I,EAAE,EAAC,sBAAsB;YAAA5I,QAAA,GAC5D6C,oBAAoB,CAAC8D,MAAM,KAAK,CAAC,gBAChCvK,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5D,OAAA;gBAAK2D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChC5D,OAAA;kBAAG2D,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACNlE,OAAA;gBAAA4D,QAAA,EAAG;cAAmD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,GAENuC,oBAAoB,CAACtC,GAAG,CAAC,CAACmI,OAAO,EAAEuC,KAAK;cAAA,IAAA4L,kBAAA,EAAAC,kBAAA,EAAAC,oBAAA,EAAAC,kBAAA;cAAA,oBACtC5a,OAAA;gBAAuB2D,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACrD5D,OAAA;kBAAK2D,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACjC5D,OAAA;oBAAK2D,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAC7B,CAAC,EAAA6W,kBAAA,GAAAnO,OAAO,CAACuG,QAAQ,cAAA4H,kBAAA,uBAAhBA,kBAAA,CAAkBvN,QAAQ,KAAI,KAAK,EAAEmM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kBAAC;oBAAAvV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACNlE,OAAA;oBAAK2D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/B5D,OAAA;sBAAA4D,QAAA,EAAK,EAAA8W,kBAAA,GAAApO,OAAO,CAACuG,QAAQ,cAAA6H,kBAAA,uBAAhBA,kBAAA,CAAkBxN,QAAQ,KAAI;oBAAe;sBAAAnJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxDlE,OAAA;sBAAA4D,QAAA,gBAAG5D,OAAA;wBAAG2D,SAAS,EAAC;sBAAiB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,KAAC,EAAC1B,UAAU,CAAC8J,OAAO,CAACI,SAAS,CAAC;oBAAA;sBAAA3I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1ElE,OAAA;sBAAA4D,QAAA,gBAAG5D,OAAA;wBAAG2D,SAAS,EAAC;sBAAc;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,KAAC,EAAC3B,eAAe,CAAC+J,OAAO,CAAClM,SAAS,EAAEkM,OAAO,CAACjM,WAAW,EAAEiM,OAAO,CAAChM,OAAO,EAAEgM,OAAO,CAAC/L,SAAS,CAAC;oBAAA;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrIlE,OAAA;sBAAA4D,QAAA,eAAG5D,OAAA;wBAAA4D,QAAA,EAASvB,cAAc,CAAClC,sBAAsB,CAACmM,OAAO,CAAClM,SAAS,EAAEkM,OAAO,CAACjM,WAAW,EAAEiM,OAAO,CAAChM,OAAO,EAAEgM,OAAO,CAAC/L,SAAS,CAAC;sBAAC;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlE,OAAA;kBAAK2D,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B5D,OAAA;oBAAA4D,QAAA,gBAAG5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAiB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,KAAC,eAAAlE,OAAA;sBAAA4D,QAAA,EAAQ;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAyW,oBAAA,GAAArO,OAAO,CAAClE,UAAU,cAAAuS,oBAAA,uBAAlBA,oBAAA,CAAoBzN,QAAQ,KAAI,eAAe;kBAAA;oBAAAnJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtHlE,OAAA;oBAAA4D,QAAA,gBAAG5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAgB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,KAAC,eAAAlE,OAAA;sBAAA4D,QAAA,EAAQ;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoI,OAAO,CAACnE,MAAM;kBAAA;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACjFoI,OAAO,CAAChF,QAAQ,iBACftH,OAAA;oBAAA4D,QAAA,gBAAG5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAoB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,KAAC,eAAAlE,OAAA;sBAAA4D,QAAA,EAAQ;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoI,OAAO,CAAChF,QAAQ;kBAAA;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAC3F;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENlE,OAAA;kBAAK2D,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACjC5D,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAoB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtClE,OAAA;sBAAA4D,QAAA,EAAQ;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACiQ,cAAc,CAAC7H,OAAO,CAACjF,MAAM,CAAC;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACJlE,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAkB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpClE,OAAA;sBAAA4D,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACmQ,kBAAkB,CAAC/H,OAAO,CAAC;kBAAA;oBAAAvI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENlE,OAAA;kBAAK2D,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAI9B,CAACe,IAAI,CAAC0F,IAAI,KAAK,aAAa,IAC1B,EAAAuQ,kBAAA,GAAAtO,OAAO,CAACuG,QAAQ,cAAA+H,kBAAA,uBAAhBA,kBAAA,CAAkBnO,GAAG,MAAK9H,IAAI,CAAC8H,GAAG,IAAIH,OAAO,CAACjF,MAAM,KAAK,SAAU,KACpE1C,IAAI,CAAC0F,IAAI,KAAK,kBAAkB,iBAChCrK,OAAA,CAAAE,SAAA;oBAAA0D,QAAA,gBACE5D,OAAA;sBACE2D,SAAS,EAAC,UAAU;sBACpBE,OAAO,EAAEA,CAAA,KAAMuO,mBAAmB,CAAC9F,OAAO,CAAE;sBAC5CkI,KAAK,EAAC,mCAAe;sBAAA5Q,QAAA,gBAErB5D,OAAA;wBAAG2D,SAAS,EAAC;sBAAa;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,wBACjC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTlE,OAAA;sBACE2D,SAAS,EAAC,YAAY;sBACtBE,OAAO,EAAEA,CAAA,KAAM6P,qBAAqB,CAACpH,OAAO,CAAE;sBAC9CkI,KAAK,EAAC,sBAAS;sBACf6D,KAAK,EAAE;wBAAEa,UAAU,EAAE,SAAS;wBAAEL,KAAK,EAAE;sBAAQ,CAAE;sBAAAjV,QAAA,gBAEjD5D,OAAA;wBAAG2D,SAAS,EAAC;sBAAkB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,WACtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT,CACH,EAEAS,IAAI,CAAC0F,IAAI,KAAK,kBAAkB,iBAC/BrK,OAAA;oBAAM2D,SAAS,EAAC,uBAAuB;oBAAC0U,KAAK,EAAE;sBAAEkB,OAAO,EAAE,UAAU;sBAAEL,UAAU,EAAE,SAAS;sBAAEM,YAAY,EAAE;oBAAM,CAAE;oBAAA5V,QAAA,gBACjH5D,OAAA;sBAAG2D,SAAS,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,iBAChC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GA/DEoI,OAAO,CAACG,GAAG;gBAAA1I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgEhB,CAAC;YAAA,CACP,CACF,EAGA2C,oBAAoB,iBACnB7G,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5D,OAAA;gBAAK2D,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BlE,OAAA;gBAAA4D,QAAA,EAAM;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN,EAGAuC,oBAAoB,CAAC8D,MAAM,GAAG,CAAC,IAAI9D,oBAAoB,CAAC8D,MAAM,IAAIyB,kBAAkB,CAACzE,YAAY,CAAC,CAACgD,MAAM,iBACxGvK,OAAA;cAAK2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B5D,OAAA;gBAAA4D,QAAA,GAAG,iDAAmB,EAACoI,kBAAkB,CAACzE,YAAY,CAAC,CAACgD,MAAM,EAAC,kCAAc;cAAA;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGN8E,gBAAgB,iBACfhJ,OAAA;MAAK2D,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B5D,OAAA;QAAK2D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC5D,OAAA;UAAG2D,SAAS,EAAC;QAAqB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvClE,OAAA;UAAA4D,QAAA,EAAOkF;QAAc;UAAA/E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDlE,OAAA,CAACrB,KAAK;MAACkc,IAAI,EAAElV,SAAU;MAACmV,MAAM,EAAE1J,gBAAiB;MAACjD,IAAI,EAAC,IAAI;MAAAvK,QAAA,gBACzD5D,OAAA,CAACrB,KAAK,CAACoc,MAAM;QAACC,WAAW;QAAApX,QAAA,eACvB5D,OAAA,CAACrB,KAAK,CAACsc,KAAK;UAAArX,QAAA,EAAC;QAAuB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACflE,OAAA,CAACrB,KAAK,CAACuZ,IAAI;QAAAtU,QAAA,EACRqD,eAAe,iBACdjH,OAAA,CAAAE,SAAA;UAAA0D,QAAA,gBACE5D,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAQ;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,EAAAK,qBAAA,GAAA0C,eAAe,CAAC4L,QAAQ,cAAAtO,qBAAA,uBAAxBA,qBAAA,CAA0B2I,QAAQ,KAAI,gBAAgB;UAAA;YAAAnJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACJlE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAQ;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3B,eAAe,CAAC0E,eAAe,CAAC7G,SAAS,EAAE6G,eAAe,CAAC5G,WAAW,EAAE4G,eAAe,CAAC3G,OAAO,EAAE2G,eAAe,CAAC1G,SAAS,CAAC;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5J,CAAC,eACJlE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAQ;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC7B,cAAc,CAAClC,sBAAsB,CAAC8G,eAAe,CAAC7G,SAAS,EAAE6G,eAAe,CAAC5G,WAAW,EAAE4G,eAAe,CAAC3G,OAAO,EAAE2G,eAAe,CAAC1G,SAAS,CAAC,CAAC;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnL,CAAC,eACJlE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAQ;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC+C,eAAe,CAACkB,MAAM;UAAA;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eAGJlE,OAAA;YAAK2D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5D,OAAA;cAAA4D,QAAA,EAAI;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrBkF,kBAAkB,gBACjBpJ,OAAA;cAAK2D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5D,OAAA,CAACf,OAAO;gBAACic,SAAS,EAAC,QAAQ;gBAAC/M,IAAI,EAAC;cAAI;gBAAApK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,0BAC1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GACJoF,eAAe,gBACjBtJ,OAAA,CAAChB,KAAK;cAAC2V,OAAO,EAAC,QAAQ;cAAA/Q,QAAA,EAAE0F;YAAe;cAAAvF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAC/CgF,WAAW,CAACqB,MAAM,KAAK,CAAC,gBAC1BvK,OAAA;cAAG2D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,gBAEpDlE,OAAA,CAACjB,SAAS;cAAA6E,QAAA,EACPsF,WAAW,CAAC/E,GAAG,CAAC+J,IAAI,iBACnBlO,OAAA,CAACjB,SAAS,CAACoc,IAAI;gBAAgBxX,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAC1F5D,OAAA;kBAAK2D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxC5D,OAAA;oBAAM2D,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAEmL,WAAW,CAACb,IAAI,CAACc,QAAQ;kBAAC;oBAAAjL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1DlE,OAAA;oBAAA4D,QAAA,EAAOsK,IAAI,CAACuC;kBAAY;oBAAA1M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChClE,OAAA;oBAAM2D,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,GAAC,GAAC,EAAC,CAACsK,IAAI,CAACkN,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAtX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC,eACNlE,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA,CAACxB,MAAM;oBACLmW,OAAO,EAAC,iBAAiB;oBACzBxG,IAAI,EAAC,IAAI;oBACTxK,SAAS,EAAC,MAAM;oBAChBE,OAAO,EAAEA,CAAA,KAAM6L,kBAAkB,CAACxB,IAAI,CAACzB,GAAG,CAAE;oBAAA7I,QAAA,eAE5C5D,OAAA,CAACF,UAAU;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,EACR,CAACS,IAAI,CAAC0F,IAAI,KAAK,aAAa,IAAI1F,IAAI,CAAC8H,GAAG,KAAKyB,IAAI,CAACoN,UAAU,kBAC3Dtb,OAAA,CAACxB,MAAM;oBACLmW,OAAO,EAAC,gBAAgB;oBACxBxG,IAAI,EAAC,IAAI;oBACTtK,OAAO,EAAEA,CAAA,KAAMmN,gBAAgB,CAAC9C,IAAI,CAACzB,GAAG,CAAE;oBAAA7I,QAAA,eAE1C5D,OAAA,CAACR,UAAU;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAxBagK,IAAI,CAACzB,GAAG;gBAAA1I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyBb,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENlE,OAAA,CAACpB,IAAI;YAAC2c,QAAQ,EAAE/J,oBAAqB;YAAA5N,QAAA,gBACnC5D,OAAA,CAACpB,IAAI,CAAC4c,KAAK;cAAC7X,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;gBAAA7X,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnClE,OAAA,CAACpB,IAAI,CAAC8c,MAAM;gBACVrN,IAAI,EAAC,QAAQ;gBACbM,KAAK,EAAExH,YAAY,CAACE,MAAO;gBAC3BiT,QAAQ,EAAE/I,oBAAqB;gBAAA3N,QAAA,gBAE/B5D,OAAA;kBAAQ2O,KAAK,EAAC,UAAU;kBAAA/K,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3ClE,OAAA;kBAAQ2O,KAAK,EAAC,UAAU;kBAAA/K,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEblE,OAAA,CAACpB,IAAI,CAAC4c,KAAK;cAAC7X,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;gBAAA7X,QAAA,EAAC;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClDlE,OAAA,CAACpB,IAAI,CAAC+c,OAAO;gBACXC,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRxN,IAAI,EAAC,UAAU;gBACfM,KAAK,EAAExH,YAAY,CAACG,QAAS;gBAC7BgT,QAAQ,EAAE/I,oBAAqB;gBAC/BgJ,WAAW,EAAC;cAAuB;gBAAAxW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAEblE,OAAA;cAAK2D,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC5D,OAAA,CAACxB,MAAM;gBAACmW,OAAO,EAAC,WAAW;gBAAChR,SAAS,EAAC,MAAM;gBAACE,OAAO,EAAEuN,gBAAiB;gBAAAxN,QAAA,EAAC;cAExE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlE,OAAA,CAACxB,MAAM;gBAACmW,OAAO,EAAC,SAAS;gBAAClG,IAAI,EAAC,QAAQ;gBAAA7K,QAAA,EAAC;cAExC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,eACP;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRlE,OAAA,CAACrB,KAAK;MAACkc,IAAI,EAAE9S,aAAc;MAAC+S,MAAM,EAAE7H,oBAAqB;MAAC9E,IAAI,EAAC,IAAI;MAAAvK,QAAA,gBACjE5D,OAAA,CAACrB,KAAK,CAACoc,MAAM;QAACC,WAAW;QAAApX,QAAA,eACvB5D,OAAA,CAACrB,KAAK,CAACsc,KAAK;UAAArX,QAAA,EAAC;QAA2B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACflE,OAAA,CAACrB,KAAK,CAACuZ,IAAI;QAAAtU,QAAA,EACRqD,eAAe,iBACdjH,OAAA,CAACpB,IAAI;UAAC2c,QAAQ,EAAElI,gBAAiB;UAAAzP,QAAA,gBAC/B5D,OAAA,CAACpB,IAAI,CAAC4c,KAAK;YAAC7X,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;cAAA7X,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClClE,OAAA,CAACpB,IAAI,CAAC+c,OAAO;cACXlN,IAAI,EAAC,MAAM;cACXE,KAAK,EAAE,EAAAnK,sBAAA,GAAAyC,eAAe,CAAC4L,QAAQ,cAAArO,sBAAA,uBAAxBA,sBAAA,CAA0B0I,QAAQ,KAAI,gBAAiB;cAC9DpJ,QAAQ;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEblE,OAAA,CAACpB,IAAI,CAAC4c,KAAK;YAAC7X,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;cAAA7X,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjClE,OAAA,CAACpB,IAAI,CAAC+c,OAAO;cACXlN,IAAI,EAAC,MAAM;cACXE,KAAK,EAAE,EAAAlK,qBAAA,GAAAwC,eAAe,CAACmB,UAAU,cAAA3D,qBAAA,uBAA1BA,qBAAA,CAA4ByI,QAAQ,KAAI,gBAAiB;cAChEpJ,QAAQ;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEblE,OAAA,CAAC7B,GAAG;YAAAyF,QAAA,gBACF5D,OAAA,CAAC5B,GAAG;cAAC0d,EAAE,EAAE,CAAE;cAAAlY,QAAA,eACT5D,OAAA,CAACpB,IAAI,CAAC4c,KAAK;gBAAC7X,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;kBAAA7X,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrClE,OAAA,CAACpB,IAAI,CAAC+c,OAAO;kBACXlN,IAAI,EAAC,MAAM;kBACXJ,IAAI,EAAC,WAAW;kBAChBM,KAAK,EAAE1G,QAAQ,CAAC7H,SAAU;kBAC1Bka,QAAQ,EAAEpH,gBAAiB;kBAC3B6I,QAAQ;gBAAA;kBAAAhY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlE,OAAA,CAAC5B,GAAG;cAAC0d,EAAE,EAAE,CAAE;cAAAlY,QAAA,eACT5D,OAAA,CAACpB,IAAI,CAAC4c,KAAK;gBAAC7X,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;kBAAA7X,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1ClE,OAAA,CAACpB,IAAI,CAAC8c,MAAM;kBACVrN,IAAI,EAAC,aAAa;kBAClBM,KAAK,EAAE1G,QAAQ,CAAC5H,WAAW,IAAI,UAAW;kBAC1Cia,QAAQ,EAAG3M,CAAC,IAAK;oBACf,MAAMqO,cAAc,GAAGrO,CAAC,CAACG,MAAM,CAACa,KAAK;;oBAErC;oBACAuE,gBAAgB,CAAC;sBACfpF,MAAM,EAAE;wBAAEO,IAAI,EAAE,aAAa;wBAAEM,KAAK,EAAEqN;sBAAe;oBACvD,CAAC,CAAC;;oBAEF;oBACA,MAAM5I,SAAS,GAAG,IAAIzS,IAAI,CAACsH,QAAQ,CAAC7H,SAAS,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAC/C,IAAIF,IAAI,CAACsH,QAAQ,CAAC3H,OAAO,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;;oBAE7D;oBACA,IAAIuS,SAAS,EAAE;sBACb,IAAI4I,cAAc,KAAK,UAAU,EAAE;wBACjC;wBACA9I,gBAAgB,CAAC;0BACfpF,MAAM,EAAE;4BAAEO,IAAI,EAAE,WAAW;4BAAEM,KAAK,EAAE;0BAAW;wBACjD,CAAC,CAAC;sBACJ,CAAC,MAAM,IAAIqN,cAAc,KAAK,WAAW,EAAE;wBACzC;wBACA9I,gBAAgB,CAAC;0BACfpF,MAAM,EAAE;4BAAEO,IAAI,EAAE,WAAW;4BAAEM,KAAK,EAAE;0BAAY;wBAClD,CAAC,CAAC;sBACJ,CAAC,MAAM,IAAIqN,cAAc,KAAK,SAAS,IAAI/T,QAAQ,CAAC1H,SAAS,KAAK,UAAU,EAAE;wBAC5E;wBACA2S,gBAAgB,CAAC;0BACfpF,MAAM,EAAE;4BAAEO,IAAI,EAAE,WAAW;4BAAEM,KAAK,EAAE;0BAAY;wBAClD,CAAC,CAAC;sBACJ;oBACF;kBACF,CAAE;kBAAA/K,QAAA,gBAEF5D,OAAA;oBAAQ2O,KAAK,EAAC,UAAU;oBAAA/K,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzClE,OAAA;oBAAQ2O,KAAK,EAAC,SAAS;oBAAA/K,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1ClE,OAAA;oBAAQ2O,KAAK,EAAC,WAAW;oBAAA/K,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlE,OAAA,CAAC7B,GAAG;YAAAyF,QAAA,gBACF5D,OAAA,CAAC5B,GAAG;cAAC0d,EAAE,EAAE,CAAE;cAAAlY,QAAA,eACT5D,OAAA,CAACpB,IAAI,CAAC4c,KAAK;gBAAC7X,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;kBAAA7X,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtClE,OAAA,CAACpB,IAAI,CAAC+c,OAAO;kBACXlN,IAAI,EAAC,MAAM;kBACXJ,IAAI,EAAC,SAAS;kBACdM,KAAK,EAAE1G,QAAQ,CAAC3H,OAAQ;kBACxBga,QAAQ,EAAEpH,gBAAiB;kBAC3B6I,QAAQ;gBAAA;kBAAAhY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlE,OAAA,CAAC5B,GAAG;cAAC0d,EAAE,EAAE,CAAE;cAAAlY,QAAA,eACT5D,OAAA,CAACpB,IAAI,CAAC4c,KAAK;gBAAC7X,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;kBAAA7X,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3ClE,OAAA,CAACpB,IAAI,CAAC8c,MAAM;kBACVrN,IAAI,EAAC,WAAW;kBAChBM,KAAK,EAAE1G,QAAQ,CAAC1H,SAAS,IAAI,UAAW;kBACxC+Z,QAAQ,EAAEpH,gBAAiB;kBAC3BpP,QAAQ;kBACN;kBACA,IAAInD,IAAI,CAACsH,QAAQ,CAAC7H,SAAS,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,IAAIF,IAAI,CAACsH,QAAQ,CAAC3H,OAAO,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAC9FoH,QAAQ,CAAC5H,WAAW,KAAK,UAAU,IAAI4H,QAAQ,CAAC5H,WAAW,KAAK,WAAW,CAC7E;kBAAAuD,QAAA,EAGA,IAAIjD,IAAI,CAACsH,QAAQ,CAAC7H,SAAS,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,IAAIF,IAAI,CAACsH,QAAQ,CAAC3H,OAAO,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,gBAC9Fb,OAAA,CAAAE,SAAA;oBAAA0D,QAAA,gBACE5D,OAAA;sBAAQ2O,KAAK,EAAC,UAAU;sBAAA/K,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzClE,OAAA;sBAAQ2O,KAAK,EAAC,SAAS;sBAAA/K,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1ClE,OAAA;sBAAQ2O,KAAK,EAAC,WAAW;sBAAA/K,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eAC7C,CAAC;kBAAA;kBAEH;kBACAlE,OAAA,CAAAE,SAAA;oBAAA0D,QAAA,GACGqE,QAAQ,CAAC5H,WAAW,KAAK,UAAU,iBAClCL,OAAA;sBAAQ2O,KAAK,EAAC,UAAU;sBAAA/K,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACzC,EAEA+D,QAAQ,CAAC5H,WAAW,KAAK,SAAS,iBACjCL,OAAA,CAAAE,SAAA;sBAAA0D,QAAA,gBACE5D,OAAA;wBAAQ2O,KAAK,EAAC,SAAS;wBAAA/K,QAAA,EAAC;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC1ClE,OAAA;wBAAQ2O,KAAK,EAAC,WAAW;wBAAA/K,QAAA,EAAC;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC7C,CACH,EAEA+D,QAAQ,CAAC5H,WAAW,KAAK,WAAW,iBACnCL,OAAA;sBAAQ2O,KAAK,EAAC,WAAW;sBAAA/K,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAC7C;kBAAA,eACD;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU,CAAC,eACdlE,OAAA,CAACpB,IAAI,CAACqd,IAAI;kBAACtY,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAC9B,IAAIjD,IAAI,CAACsH,QAAQ,CAAC7H,SAAS,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,IAAIF,IAAI,CAACsH,QAAQ,CAAC3H,OAAO,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;kBAC9F;kBACAoH,QAAQ,CAAC5H,WAAW,KAAK,UAAU,GAC/B,8EAA8E,GAC9E4H,QAAQ,CAAC5H,WAAW,KAAK,WAAW,GAClC,oFAAoF,GACpF,0CAA0C;kBAEhD;kBACA;gBACD;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlE,OAAA,CAACpB,IAAI,CAAC4c,KAAK;YAAC7X,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;cAAA7X,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9BlE,OAAA,CAACpB,IAAI,CAAC+c,OAAO;cACXC,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRxN,IAAI,EAAC,QAAQ;cACbM,KAAK,EAAE1G,QAAQ,CAACE,MAAO;cACvBmS,QAAQ,EAAEpH,gBAAiB;cAC3BqH,WAAW,EAAC,sCAAsB;cAClCwB,QAAQ;YAAA;cAAAhY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAGblE,OAAA;YAAK2D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5D,OAAA;cAAA4D,QAAA,EAAI;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrBkF,kBAAkB,gBACjBpJ,OAAA;cAAK2D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5D,OAAA,CAACf,OAAO;gBAACic,SAAS,EAAC,QAAQ;gBAAC/M,IAAI,EAAC;cAAI;gBAAApK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,0BAC1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GACJoF,eAAe,gBACjBtJ,OAAA,CAAChB,KAAK;cAAC2V,OAAO,EAAC,QAAQ;cAAA/Q,QAAA,EAAE0F;YAAe;cAAAvF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAEjDlE,OAAA,CAAAE,SAAA;cAAA0D,QAAA,GAEGsF,WAAW,CAACqB,MAAM,KAAK,CAAC,gBACvBvK,OAAA;gBAAG2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,gBAEpDlE,OAAA,CAACjB,SAAS;gBAAC4E,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACxBsF,WAAW,CAAC/E,GAAG,CAAC+J,IAAI,iBACnBlO,OAAA,CAACjB,SAAS,CAACoc,IAAI;kBAAgBxX,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,gBAC1F5D,OAAA;oBAAK2D,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC5D,OAAA;sBAAM2D,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEmL,WAAW,CAACb,IAAI,CAACc,QAAQ;oBAAC;sBAAAjL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1DlE,OAAA;sBAAA4D,QAAA,EAAOsK,IAAI,CAACuC;oBAAY;sBAAA1M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChClE,OAAA;sBAAM2D,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,GAAC,GAAC,EAAC,CAACsK,IAAI,CAACkN,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAtX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC,eACNlE,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA,CAACxB,MAAM;sBACLmW,OAAO,EAAC,iBAAiB;sBACzBxG,IAAI,EAAC,IAAI;sBACTxK,SAAS,EAAC,MAAM;sBAChBE,OAAO,EAAEA,CAAA,KAAM6L,kBAAkB,CAACxB,IAAI,CAACzB,GAAG,CAAE;sBAAA7I,QAAA,eAE5C5D,OAAA,CAACF,UAAU;wBAAAiE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,EACR,CAACS,IAAI,CAAC0F,IAAI,KAAK,aAAa,IAAI1F,IAAI,CAAC8H,GAAG,KAAKyB,IAAI,CAACoN,UAAU,kBAC3Dtb,OAAA,CAACxB,MAAM;sBACLmW,OAAO,EAAC,gBAAgB;sBACxBxG,IAAI,EAAC,IAAI;sBACTtK,OAAO,EAAEA,CAAA,KAAMmN,gBAAgB,CAAC9C,IAAI,CAACzB,GAAG,CAAE;sBAAA7I,QAAA,eAE1C5D,OAAA,CAACR,UAAU;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAxBagK,IAAI,CAACzB,GAAG;kBAAA1I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBb,CACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CACZ,eAGDlE,OAAA;gBAAK2D,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB5D,OAAA;kBAAK2D,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7C5D,OAAA,CAACxB,MAAM;oBACLmW,OAAO,EAAC,iBAAiB;oBACzB9Q,OAAO,EAAEA,CAAA,KAAMiG,YAAY,CAACoS,OAAO,CAACtL,KAAK,CAAC,CAAE;oBAC5CjN,SAAS,EAAC,2BAA2B;oBACrCG,QAAQ,EAAE8F,cAAe;oBAAAhG,QAAA,gBAEzB5D,OAAA,CAACT,YAAY;sBAACoE,SAAS,EAAC;oBAAM;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBACnC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlE,OAAA,CAACpB,IAAI,CAACqd,IAAI;oBAACtY,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAEvC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACZlE,OAAA;oBACEyO,IAAI,EAAC,MAAM;oBACX0N,GAAG,EAAErS,YAAa;oBAClBwQ,QAAQ,EAAE5M,gBAAiB;oBAC3B2K,KAAK,EAAE;sBAAES,OAAO,EAAE;oBAAO,CAAE;oBAC3BsD,QAAQ;kBAAA;oBAAArY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAELwF,WAAW,iBACV1J,OAAA,CAAChB,KAAK;kBAAC2V,OAAO,EAAC,QAAQ;kBAAChR,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAC1C8F;gBAAW;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACR,EAEAsF,aAAa,CAACe,MAAM,GAAG,CAAC,iBACvBvK,OAAA,CAAAE,SAAA;kBAAA0D,QAAA,gBACE5D,OAAA,CAACjB,SAAS;oBAAC4E,SAAS,EAAC,MAAM;oBAAAC,QAAA,EACxB4F,aAAa,CAACrF,GAAG,CAAC,CAAC+J,IAAI,EAAEW,KAAK,kBAC7B7O,OAAA,CAACjB,SAAS,CAACoc,IAAI;sBAAaxX,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBACvF5D,OAAA;wBAAK2D,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,gBACxC5D,OAAA;0BAAM2D,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAEmL,WAAW,CAACb,IAAI,CAACO,IAAI;wBAAC;0BAAA1K,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACtDlE,OAAA;0BAAA4D,QAAA,EAAOsK,IAAI,CAACG;wBAAI;0BAAAtK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACxBlE,OAAA;0BAAM2D,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,GAAC,GAAC,EAAC,CAACsK,IAAI,CAACC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEkN,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;wBAAA;0BAAAtX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjF,CAAC,eACNlE,OAAA,CAACxB,MAAM;wBACLmW,OAAO,EAAC,gBAAgB;wBACxBxG,IAAI,EAAC,IAAI;wBACTtK,OAAO,EAAEA,CAAA,KAAM+K,gBAAgB,CAACC,KAAK,CAAE;wBACvC/K,QAAQ,EAAE8F,cAAe;wBAAAhG,QAAA,eAEzB5D,OAAA,CAACR,UAAU;0BAAAuE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC;oBAAA,GAbU2K,KAAK;sBAAA9K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAcV,CACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eAEZlE,OAAA;oBAAK2D,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,eAC9C5D,OAAA,CAACxB,MAAM;sBACLmW,OAAO,EAAC,SAAS;sBACjBxG,IAAI,EAAC,IAAI;sBACTtK,OAAO,EAAEA,CAAA,KAAMsL,WAAW,CAAClI,eAAe,CAACwF,GAAG,CAAE;sBAChD3I,QAAQ,EAAE8F,cAAe;sBAAAhG,QAAA,EAExBgG,cAAc,gBACb5J,OAAA,CAAAE,SAAA;wBAAA0D,QAAA,gBACE5D,OAAA,CAACf,OAAO;0BAACic,SAAS,EAAC,QAAQ;0BAAC/M,IAAI,EAAC,IAAI;0BAACxK,SAAS,EAAC;wBAAM;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gCAE3D;sBAAA,eAAE,CAAC,GAEH;oBACD;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,eACN,CACH;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,eACN,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLS,IAAI,IAAIA,IAAI,CAAC0F,IAAI,KAAK,aAAa,gBAClCrK,OAAA,CAAAE,SAAA;YAAA0D,QAAA,gBACE5D,OAAA,CAACpB,IAAI,CAAC4c,KAAK;cAAC7X,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;gBAAA7X,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnClE,OAAA,CAACpB,IAAI,CAAC8c,MAAM;gBACVrN,IAAI,EAAC,QAAQ;gBACbM,KAAK,EAAE1G,QAAQ,CAACZ,MAAO;gBACvBiT,QAAQ,EAAEpH,gBAAiB;gBAAAtP,QAAA,gBAE3B5D,OAAA;kBAAQ2O,KAAK,EAAC,SAAS;kBAAA/K,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1ClE,OAAA;kBAAQ2O,KAAK,EAAC,UAAU;kBAAA/K,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1ClE,OAAA;kBAAQ2O,KAAK,EAAC,UAAU;kBAAA/K,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5ClE,OAAA;kBAAQ2O,KAAK,EAAC,WAAW;kBAAA/K,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGblE,OAAA;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNlE,OAAA;cAAA4D,QAAA,EAAI;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEhClE,OAAA,CAACpB,IAAI,CAAC4c,KAAK;cAAC7X,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;gBAAA7X,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxClE,OAAA,CAACpB,IAAI,CAAC8c,MAAM;gBACVrN,IAAI,EAAC,YAAY;gBACjBM,KAAK,EAAE1G,QAAQ,CAACI,UAAU,IAAI,KAAM;gBACpCiS,QAAQ,EAAEpH,gBAAiB;gBAAAtP,QAAA,gBAE3B5D,OAAA;kBAAQ2O,KAAK,EAAC,KAAK;kBAAA/K,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9ClE,OAAA;kBAAQ2O,KAAK,EAAC,YAAY;kBAAA/K,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACdlE,OAAA,CAACpB,IAAI,CAACqd,IAAI;gBAACtY,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAElC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAEZ+D,QAAQ,CAACI,UAAU,KAAK,YAAY,iBACnCrI,OAAA,CAACpB,IAAI,CAAC4c,KAAK;cAAC7X,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;gBAAA7X,QAAA,EAAC;cAAwB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjDlE,OAAA,CAACpB,IAAI,CAAC+c,OAAO;gBACXC,EAAE,EAAC,QAAQ;gBACXQ,QAAQ;gBACRzN,KAAK,EAAE1G,QAAQ,CAACK,SAAS,IAAI,EAAG;gBAChCgS,QAAQ,EAAG3M,CAAC,IAAK;kBACf,MAAM0O,eAAe,GAAGxR,KAAK,CAACgD,IAAI,CAACF,CAAC,CAACG,MAAM,CAACuO,eAAe,EAAEC,MAAM,IAAIA,MAAM,CAAC3N,KAAK,CAAC;kBACpFzG,WAAW,CAAC;oBACV,GAAGD,QAAQ;oBACXK,SAAS,EAAE+T;kBACb,CAAC,CAAC;gBACJ,CAAE;gBACFhE,KAAK,EAAE;kBAAEkE,MAAM,EAAE;gBAAQ,CAAE;gBAAA3Y,QAAA,EAE1BiH,KAAK,CAACC,OAAO,CAACtC,QAAQ,CAAC,GAAGA,QAAQ,CAChCyF,MAAM,CAACtJ,IAAI,IAAI,CAAC,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAACqI,QAAQ,CAACrI,IAAI,CAAC0F,IAAI,CAAC,CAAC,CACrHlG,GAAG,CAACQ,IAAI,iBACP3E,OAAA;kBAAuB2O,KAAK,EAAEhK,IAAI,CAAC8H,GAAI;kBAAA7I,QAAA,EACpCe,IAAI,CAACuI,QAAQ,IAAIvI,IAAI,CAAC0J,IAAI,IAAI1J,IAAI,CAAC6X;gBAAK,GAD9B7X,IAAI,CAAC8H,GAAG;kBAAA1I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC,GAAG;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACflE,OAAA,CAACpB,IAAI,CAACqd,IAAI;gBAACtY,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAGlC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACb,eAEDlE,OAAA,CAACpB,IAAI,CAAC4c,KAAK;cAAC7X,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;gBAAA7X,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/ClE,OAAA,CAACpB,IAAI,CAAC+c,OAAO;gBACXC,EAAE,EAAC,QAAQ;gBACXQ,QAAQ;gBACRzN,KAAK,EAAE1G,QAAQ,CAACM,UAAU,IAAI,EAAG;gBACjC+R,QAAQ,EAAG3M,CAAC,IAAK;kBACf,MAAM0O,eAAe,GAAGxR,KAAK,CAACgD,IAAI,CAACF,CAAC,CAACG,MAAM,CAACuO,eAAe,EAAEC,MAAM,IAAIA,MAAM,CAAC3N,KAAK,CAAC;kBACpFzG,WAAW,CAAC;oBACV,GAAGD,QAAQ;oBACXM,UAAU,EAAE8T;kBACd,CAAC,CAAC;gBACJ,CAAE;gBACFhE,KAAK,EAAE;kBAAEkE,MAAM,EAAE;gBAAQ,CAAE;gBAAA3Y,QAAA,EAE1BiH,KAAK,CAACC,OAAO,CAACtC,QAAQ,CAAC,GAAGA,QAAQ,CAChCyF,MAAM,CAACtJ,IAAI,IAAI,CAAC,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAACqI,QAAQ,CAACrI,IAAI,CAAC0F,IAAI,CAAC,CAAC,CACrHlG,GAAG,CAACQ,IAAI,iBACP3E,OAAA;kBAAuB2O,KAAK,EAAEhK,IAAI,CAAC8H,GAAI;kBAAA7I,QAAA,EACpCe,IAAI,CAACuI,QAAQ,IAAIvI,IAAI,CAAC0J,IAAI,IAAI1J,IAAI,CAAC6X;gBAAK,GAD9B7X,IAAI,CAAC8H,GAAG;kBAAA1I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC,GAAG;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACflE,OAAA,CAACpB,IAAI,CAACqd,IAAI;gBAACtY,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAIlC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,eACb,CAAC,gBAEHlE,OAAA,CAACpB,IAAI,CAAC4c,KAAK;YAAC7X,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1B5D,OAAA,CAACpB,IAAI,CAAC6c,KAAK;cAAA7X,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnClE,OAAA,CAACpB,IAAI,CAAC+c,OAAO;cACXlN,IAAI,EAAC,MAAM;cACXE,KAAK,EAAEwF,cAAc,CAAClM,QAAQ,CAACZ,MAAM,CAAC,CAACmT,KAAK,CAAC5W,QAAS;cACtDE,QAAQ;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFlE,OAAA,CAACpB,IAAI,CAACqd,IAAI;cAACtY,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACb,eAEDlE,OAAA;YAAK2D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC5D,OAAA,CAACxB,MAAM;cAACmW,OAAO,EAAC,WAAW;cAAChR,SAAS,EAAC,MAAM;cAACE,OAAO,EAAEoP,oBAAqB;cAAArP,QAAA,EAAC;YAE5E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlE,OAAA,CAACxB,MAAM;cAACmW,OAAO,EAAC,SAAS;cAAClG,IAAI,EAAC,QAAQ;cAAA7K,QAAA,EAAC;YAExC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRlE,OAAA,CAACrB,KAAK;MAACkc,IAAI,EAAEnS,eAAgB;MAACoS,MAAM,EAAEnH,sBAAuB;MAAA/P,QAAA,gBAC3D5D,OAAA,CAACrB,KAAK,CAACoc,MAAM;QAACC,WAAW;QAAApX,QAAA,eACvB5D,OAAA,CAACrB,KAAK,CAACsc,KAAK;UAAArX,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACflE,OAAA,CAACrB,KAAK,CAACuZ,IAAI;QAAAtU,QAAA,EACRgF,eAAe,iBACd5I,OAAA,CAAAE,SAAA;UAAA0D,QAAA,gBACE5D,OAAA;YAAA4D,QAAA,EAAG;UAAgD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvDlE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAQ;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,EAAAQ,qBAAA,GAAAkE,eAAe,CAACiK,QAAQ,cAAAnO,qBAAA,uBAAxBA,qBAAA,CAA0BwI,QAAQ,KAAI,gBAAgB;UAAA;YAAAnJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACJlE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAQ;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3B,eAAe,CAACqG,eAAe,CAACxI,SAAS,EAAEwI,eAAe,CAACvI,WAAW,EAAEuI,eAAe,CAACtI,OAAO,EAAEsI,eAAe,CAACrI,SAAS,CAAC;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5J,CAAC,eACJlE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAQ;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC7B,cAAc,CAAClC,sBAAsB,CAACyI,eAAe,CAACxI,SAAS,EAAEwI,eAAe,CAACvI,WAAW,EAAEuI,eAAe,CAACtI,OAAO,EAAEsI,eAAe,CAACrI,SAAS,CAAC,CAAC;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnL,CAAC,eACJlE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAQ;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC0E,eAAe,CAACT,MAAM;UAAA;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACJlE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAQ;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACiQ,cAAc,CAACvL,eAAe,CAACvB,MAAM,CAAC;UAAA;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACJlE,OAAA;YAAK2D,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC5D,OAAA;cAAG2D,SAAS,EAAC;YAAkC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mJAEtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA,eACN;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACblE,OAAA,CAACrB,KAAK,CAAC8d,MAAM;QAAA7Y,QAAA,gBACX5D,OAAA,CAACxB,MAAM;UAACmW,OAAO,EAAC,WAAW;UAAC9Q,OAAO,EAAE8P,sBAAuB;UAAA/P,QAAA,EAAC;QAE7D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlE,OAAA,CAACxB,MAAM;UAACmW,OAAO,EAAC,QAAQ;UAAC9Q,OAAO,EAAE+P,mBAAoB;UAAAhQ,QAAA,EAAC;QAEvD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRlE,OAAA,CAACrB,KAAK;MAACkc,IAAI,EAAE9T,mBAAoB;MAAC+T,MAAM,EAAExJ,0BAA2B;MAACnD,IAAI,EAAC,IAAI;MAAAvK,QAAA,gBAC7E5D,OAAA,CAACrB,KAAK,CAACoc,MAAM;QAACC,WAAW;QAAApX,QAAA,eACvB5D,OAAA,CAACrB,KAAK,CAACsc,KAAK;UAAArX,QAAA,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACflE,OAAA,CAACrB,KAAK,CAACuZ,IAAI;QAAAtU,QAAA,EACRqD,eAAe,iBACdjH,OAAA;UAAA4D,QAAA,gBACE5D,OAAA;YAAK2D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5D,OAAA;cAAA4D,QAAA,EAAI;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChClE,OAAA,CAAC1B,KAAK;cAACoe,QAAQ;cAAA9Y,QAAA,eACb5D,OAAA;gBAAA4D,QAAA,gBACE5D,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA;oBAAI2c,KAAK,EAAC,KAAK;oBAAA/Y,QAAA,eAAC5D,OAAA;sBAAA4D,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpDlE,OAAA;oBAAA4D,QAAA,EAAKrB,eAAe,CAAC0E,eAAe,CAAC7G,SAAS,EAAE6G,eAAe,CAAC5G,WAAW,EAAE4G,eAAe,CAAC3G,OAAO,EAAE2G,eAAe,CAAC1G,SAAS;kBAAC;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpI,CAAC,eACLlE,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA;oBAAA4D,QAAA,eAAI5D,OAAA;sBAAA4D,QAAA,EAAQ;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/BlE,OAAA;oBAAA4D,QAAA,EAAKqD,eAAe,CAACkB;kBAAM;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACLlE,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA;oBAAA4D,QAAA,eAAI5D,OAAA;sBAAA4D,QAAA,EAAQ;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpClE,OAAA;oBAAA4D,QAAA,EAAKuQ,cAAc,CAAClN,eAAe,CAACI,MAAM;kBAAC;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENlE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAI;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrBkF,kBAAkB,gBACjBpJ,OAAA;cAAK2D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B5D,OAAA,CAACf,OAAO;gBAACic,SAAS,EAAC,QAAQ;gBAACvG,OAAO,EAAC;cAAS;gBAAA5Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDlE,OAAA;gBAAG2D,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAA0B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,GACJoF,eAAe,gBACjBtJ,OAAA,CAAChB,KAAK;cAAC2V,OAAO,EAAC,QAAQ;cAAA/Q,QAAA,EAAE0F;YAAe;cAAAvF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAC/CgF,WAAW,CAACqB,MAAM,KAAK,CAAC,gBAC1BvK,OAAA,CAAChB,KAAK;cAAC2V,OAAO,EAAC,MAAM;cAAA/Q,QAAA,EAAC;YAA0B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,gBAExDlE,OAAA,CAACjB,SAAS;cAAA6E,QAAA,EACPsF,WAAW,CAAC/E,GAAG,CAAC+J,IAAI;gBAAA,IAAA0O,gBAAA;gBAAA,oBACnB5c,OAAA,CAACjB,SAAS,CAACoc,IAAI;kBAAgBxX,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,gBAC1F5D,OAAA;oBAAK2D,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxC5D,OAAA;sBAAM2D,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEmL,WAAW,CAACb,IAAI,CAACc,QAAQ;oBAAC;sBAAAjL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1DlE,OAAA;sBAAA4D,QAAA,EAAOsK,IAAI,CAACuC;oBAAY;sBAAA1M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChClE,OAAA;sBAAM2D,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,GAAC,GAAC,EAAC,CAACsK,IAAI,CAACkN,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAtX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxFlE,OAAA;sBAAM2D,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,GAAC,6BAAc,EAAC,EAAAgZ,gBAAA,GAAA1O,IAAI,CAACoN,UAAU,cAAAsB,gBAAA,uBAAfA,gBAAA,CAAiB1P,QAAQ,KAAI,gBAAgB;oBAAA;sBAAAnJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnG,CAAC,eACNlE,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA,CAACxB,MAAM;sBACLmW,OAAO,EAAC,iBAAiB;sBACzBxG,IAAI,EAAC,IAAI;sBACTxK,SAAS,EAAC,MAAM;sBAChBE,OAAO,EAAEA,CAAA,KAAM6L,kBAAkB,CAACxB,IAAI,CAACzB,GAAG,CAAE;sBAAA7I,QAAA,eAE5C5D,OAAA,CAACF,UAAU;wBAAAiE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,EACR,CAACS,IAAI,CAAC0F,IAAI,KAAK,aAAa,IAAI1F,IAAI,CAAC8H,GAAG,KAAKyB,IAAI,CAACoN,UAAU,kBAC3Dtb,OAAA,CAACxB,MAAM;sBACLmW,OAAO,EAAC,gBAAgB;sBACxBxG,IAAI,EAAC,IAAI;sBACTtK,OAAO,EAAEA,CAAA,KAAMmN,gBAAgB,CAAC9C,IAAI,CAACzB,GAAG,CAAE;sBAAA7I,QAAA,eAE1C5D,OAAA,CAACR,UAAU;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAzBagK,IAAI,CAACzB,GAAG;kBAAA1I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0Bb,CAAC;cAAA,CAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACblE,OAAA,CAACrB,KAAK,CAAC8d,MAAM;QAAA7Y,QAAA,eACX5D,OAAA,CAACxB,MAAM;UAACmW,OAAO,EAAC,WAAW;UAAC9Q,OAAO,EAAEyN,0BAA2B;UAAA1N,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAED,eAAeI,aAAa;;AAE5B;AACA,IAAI,OAAO4G,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAM2R,gBAAgB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;EAED;EACA,IAAI,CAAC3R,QAAQ,CAAC4R,aAAa,CAAC,qBAAqB,CAAC,EAAE;IAClD,MAAMC,UAAU,GAAG7R,QAAQ,CAACoF,aAAa,CAAC,OAAO,CAAC;IAClDyM,UAAU,CAACvQ,EAAE,GAAG,oBAAoB;IACpCuQ,UAAU,CAACtO,IAAI,GAAG,UAAU;IAC5BsO,UAAU,CAACC,SAAS,GAAGH,gBAAgB;IACvC3R,QAAQ,CAAC+R,IAAI,CAACtM,WAAW,CAACoM,UAAU,CAAC;EACvC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}