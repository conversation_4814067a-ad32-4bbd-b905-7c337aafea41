import React, { useState, useEffect, useRef } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Tabs, Tab, Modal, Form, Tooltip, OverlayTrigger, ListGroup, Alert, Spinner } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext'; 
import API from '../services/api';
import '../css/LeaveRequest.css';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import '../css/FileAttachments.css';
import { FaFileUpload, FaTrashAlt, FaFile, FaFilePdf, FaFileWord, FaFileExcel, FaFileImage, FaDownload } from 'react-icons/fa';

// Hàm tính tổng thời gian nghỉ
const calculateLeaveDuration = (startDate, startPeriod, endDate, endPeriod) => {
  // Kiểm tra nếu không có dữ liệu thời gian
  if (!startDate || !endDate) {
    return 0;
  }
  
  // Đặt giá trị mặc định cho startPeriod và endPeriod nếu không có
  const actualStartPeriod = startPeriod || 'full_day';
  const actualEndPeriod = endPeriod || 'full_day';
  
  // Chuyển đổi ngày thành đối tượng Date để so sánh
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // Đặt giờ về 0 để so sánh chỉ ngày
  start.setHours(0, 0, 0, 0);
  end.setHours(0, 0, 0, 0);
  
  // Hàm kiểm tra ngày có phải là Chủ nhật không
  const isSunday = (date) => {
    return date.getDay() === 0; // 0 là Chủ nhật trong JavaScript
  };
  
  // Hàm kiểm tra ngày có phải là thứ 7 không
  const isSaturday = (date) => {
    return date.getDay() === 6; // 6 là thứ 7 trong JavaScript
  };
  
  // Hàm đếm số ngày làm việc (không tính Chủ nhật) giữa hai ngày
  const countWorkDays = (startDate, endDate) => {
    let count = 0;
    const currentDate = new Date(startDate);
    
    // Lặp qua từng ngày giữa startDate và endDate
    while (currentDate <= endDate) {
      // Nếu không phải Chủ nhật thì tăng biến đếm
      if (!isSunday(currentDate)) {
        // Nếu là thứ 7, chỉ tính 0.5 ngày
        if (isSaturday(currentDate)) {
          count += 0.5;
        } else {
          count += 1.0;
        }
      }
      // Tăng ngày lên 1
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return count;
  };
  
  // Nếu cùng một ngày
  if (start.getTime() === end.getTime()) {
    // Kiểm tra nếu là Chủ nhật thì không tính
    if (isSunday(start)) {
      return 0;
    }
    
    // Kiểm tra nếu là thứ 7
    if (isSaturday(start)) {
      // Thứ 7 chỉ tính buổi sáng
      return 0.5;
    }
    
    if (actualStartPeriod === 'full_day' && actualEndPeriod === 'full_day') {
      return 1;
    } else if (actualStartPeriod === actualEndPeriod && actualStartPeriod !== 'full_day') {
      return 0.5;
    } else if (actualStartPeriod === 'morning' && actualEndPeriod === 'afternoon') {
      return 1;
    } else {
      return 0.5;
    }
  } else {
    // Tính số ngày giữa hai ngày
    const diffTime = Math.abs(end - start);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    // Kiểm tra các ngày đặc biệt
    const startIsSunday = isSunday(start);
    const endIsSunday = isSunday(end);
    const startIsSaturday = isSaturday(start);
    const endIsSaturday = isSaturday(end);
    
    // Trường hợp 2 ngày liền kề
    if (diffDays === 1) {
      // Trường hợp đặc biệt: từ thứ 6 đến Chủ nhật
      if (!startIsSaturday && !startIsSunday && endIsSunday) {
        // Nếu thứ 6 là cả ngày, tính 1.5 ngày (1 ngày thứ 6 + 0.5 ngày thứ 7 buổi sáng)
        if (actualStartPeriod === 'full_day') {
          return 1.5;
        } 
        // Nếu thứ 6 chỉ buổi chiều, tính 1 ngày (0.5 ngày thứ 6 + 0.5 ngày thứ 7 buổi sáng)
        else if (actualStartPeriod === 'afternoon') {
          return 1;
        }
        // Nếu thứ 6 chỉ buổi sáng, tính 1 ngày (0.5 ngày thứ 6 + 0.5 ngày thứ 7 buổi sáng)
        else {
          return 1;
        }
      }
      
      // Trường hợp từ thứ 6 đến thứ 7
      if (!startIsSaturday && !startIsSunday && endIsSaturday) {
        // Thứ 7 chỉ tính buổi sáng
        if (actualStartPeriod === 'full_day') {
          return 1.5;
        } else if (actualStartPeriod === 'afternoon') {
          return 1;
        } else {
          return 1;
        }
      }
      
      // Nếu cả hai ngày đều là Chủ nhật
      if (startIsSunday && endIsSunday) {
        return 0;
      }
      
      // Nếu ngày bắt đầu là Chủ nhật
      if (startIsSunday) {
        // Chỉ tính ngày kết thúc
        if (endIsSaturday) {
          // Nếu ngày kết thúc là thứ 7, chỉ tính 0.5 ngày
          return 0.5;
        } else if (actualEndPeriod === 'full_day') {
          return 1;
        } else {
          return 0.5;
        }
      }
      
      // Nếu ngày kết thúc là Chủ nhật
      if (endIsSunday) {
        // Nếu ngày bắt đầu là thứ 7, chỉ tính 0.5 ngày (thứ 7 buổi sáng)
        if (startIsSaturday) {
          return 0.5;
        }
        
        // Nếu ngày bắt đầu không phải thứ 7
        if (actualStartPeriod === 'full_day') {
          return 1;
        } else {
          return 0.5;
        }
      }
      
      // Nếu ngày bắt đầu là thứ 7
      if (startIsSaturday) {
        // Thứ 7 chỉ tính buổi sáng
        return 0.5;
      }
      
      // Nếu ngày kết thúc là thứ 7
      if (endIsSaturday) {
        // Thứ 7 chỉ tính buổi sáng
        if (actualStartPeriod === 'full_day') {
          return 1.5;
        } else if (actualStartPeriod === 'afternoon') {
          return 1;
        } else if (actualStartPeriod === 'morning') {
          return 1;
        }
      }
      
      // Các trường hợp thông thường
      if (actualStartPeriod === 'afternoon' && actualEndPeriod === 'morning') {
        return 1;
      } else if (actualStartPeriod === 'afternoon' && (actualEndPeriod === 'full_day' || actualEndPeriod === 'afternoon')) {
        return 1.5;
      } else if ((actualStartPeriod === 'full_day' || actualStartPeriod === 'morning') && actualEndPeriod === 'morning') {
        return 1.5;
      } else {
        return 2;
      }
    } else if (diffDays > 1) {
      // Trường hợp đặc biệt: từ thứ 5 đến thứ 7
      if (!startIsSaturday && !startIsSunday && endIsSaturday) {
        // Tính số ngày từ thứ 5 đến thứ 6 (không tính thứ 7)
        const thursdayToFriday = new Date(end);
        thursdayToFriday.setDate(thursdayToFriday.getDate() - 1); // Lùi 1 ngày từ thứ 7 để lấy thứ 6
        
        // Đếm số ngày làm việc từ ngày bắt đầu đến thứ 6
        const workDaysBeforeSaturday = diffDays; // Số ngày từ thứ 5 đến thứ 6
        
        // Điều chỉnh ngày đầu tiên nếu không phải cả ngày
        let adjustedDays = workDaysBeforeSaturday;
        if (!startIsSunday && !startIsSaturday && actualStartPeriod === 'afternoon') {
          adjustedDays -= 0.5;
        }
        
        // Cộng thêm 0.5 ngày cho thứ 7 buổi sáng
        return adjustedDays + 0.5;
      }
      
      // Trường hợp đặc biệt: từ thứ 5 đến chủ nhật
      if (!startIsSaturday && !startIsSunday && endIsSunday) {
        // Tính số ngày từ thứ 5 đến thứ 6 (không tính thứ 7 và chủ nhật)
        const thursdayToFriday = new Date(end);
        thursdayToFriday.setDate(thursdayToFriday.getDate() - 2); // Lùi 2 ngày từ chủ nhật để lấy thứ 6
        
        // Đếm số ngày làm việc từ ngày bắt đầu đến thứ 6
        const workDaysBeforeSaturday = diffDays - 1; // Số ngày từ thứ 5 đến thứ 6 (trừ chủ nhật)
        
        // Điều chỉnh ngày đầu tiên nếu không phải cả ngày
        let adjustedDays = workDaysBeforeSaturday;
        if (!startIsSunday && !startIsSaturday && actualStartPeriod === 'afternoon') {
          adjustedDays -= 0.5;
        }
        
        // Cộng thêm 0.5 ngày cho thứ 7 buổi sáng
        return adjustedDays + 0.5;
      }
      
      // Sử dụng hàm đếm ngày làm việc để tính toán chính xác
      let workDays = 0;
      
      // Đếm số ngày làm việc (không tính chủ nhật)
      const currentDate = new Date(start);
      while (currentDate <= end) {
        // Nếu không phải Chủ nhật thì tăng biến đếm
        if (!isSunday(currentDate)) {
          // Nếu là thứ 7, chỉ tính 0.5 ngày
          if (isSaturday(currentDate)) {
            workDays += 0.5;
          } else {
            workDays += 1;
          }
        }
        // Tăng ngày lên 1
        currentDate.setDate(currentDate.getDate() + 1);
      }
      
      // Điều chỉnh giá trị dựa trên thời gian bắt đầu và kết thúc
      let adjustedDays = workDays;
      
      // Điều chỉnh ngày đầu tiên nếu không phải cả ngày
      if (!startIsSunday && !startIsSaturday && actualStartPeriod === 'afternoon') {
        adjustedDays -= 0.5;
      }
      
      // Điều chỉnh ngày cuối cùng nếu không phải cả ngày
      if (!endIsSunday && !endIsSaturday && actualEndPeriod === 'morning') {
        adjustedDays -= 0.5;
      } else if (endIsSaturday && actualEndPeriod === 'morning') {
        // Thứ 7 đã được tính là 0.5 trong workDays, không cần điều chỉnh thêm
      }
      
      return adjustedDays;
    }
  }
  
  return 0;
};

// Hàm định dạng hiển thị tổng thời gian nghỉ
const formatDuration = (duration) => {
  if (duration === 0) return '0 ngày';
  if (duration % 1 === 0) return `${duration} ngày`;
  return `${Math.floor(duration)}.5 ngày`;
};

// Hàm tính toán và hiển thị thời gian nghỉ
const formatLeaveTime = (startDate, startPeriod, endDate, endPeriod) => {
  // Kiểm tra nếu không có dữ liệu thời gian
  if (!startDate || !endDate) {
    return 'Không có dữ liệu';
  }
  
  // Đặt giá trị mặc định cho startPeriod và endPeriod nếu không có
  const actualStartPeriod = startPeriod || 'full_day';
  const actualEndPeriod = endPeriod || 'full_day';
  
  // Chuyển đổi ngày thành đối tượng Date để so sánh
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // Đặt giờ về 0 để so sánh chỉ ngày
  start.setHours(0, 0, 0, 0);
  end.setHours(0, 0, 0, 0);
  
  // Định dạng ngày tháng
  const formatDate = (date) => {
    return format(new Date(date), 'dd/MM/yyyy', { locale: vi });
  };
  
  // Định dạng thời gian
  const formatPeriod = (period) => {
    switch(period) {
      case 'morning': return 'Sáng';
      case 'afternoon': return 'Chiều';
      case 'full_day': return 'Cả ngày';
      default: return 'Cả ngày';
    }
  };
  
  // Tính toán số ngày nghỉ thực tế
  const days = calculateLeaveDuration(startDate, actualStartPeriod, endDate, actualEndPeriod);
  
  // Định dạng hiển thị số ngày
  let duration = '';
  if (days === 0) {
    duration = '(0 ngày)';
  } else if (days % 1 === 0) {
    duration = `(${days} ngày)`;
  } else {
    duration = `(${Math.floor(days)}.5 ngày)`;
  }
  
  return `${formatDate(startDate)} ${formatPeriod(actualStartPeriod)} - ${formatDate(endDate)} ${formatPeriod(actualEndPeriod)} ${duration}`;
};

// Thêm component TablePagination
const TablePagination = ({ totalRecords, recordsPerPage, currentPage, onPageChange }) => {
  const pageNumbers = [];
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  let startPage = Math.max(1, currentPage - 2);
  let endPage = Math.min(totalPages, startPage + 4);

  if (endPage - startPage < 4) {
    startPage = Math.max(1, endPage - 4);
  }

  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  if (totalPages <= 1) return null;

  return (
    <div className="table-pagination">
      <button
        onClick={() => onPageChange(1)}
        disabled={currentPage === 1}
        className="pagination-button"
      >
        «
      </button>
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="pagination-button"
      >
        {'<'}
      </button>
      {pageNumbers.map((number) => (
        <button
          key={number}
          onClick={() => onPageChange(number)}
          className={`pagination-button ${currentPage === number ? 'active' : ''}`}
        >
          {number}
        </button>
      ))}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="pagination-button"
      >
        {'>'}
      </button>
      <button
        onClick={() => onPageChange(totalPages)}
        disabled={currentPage === totalPages}
        className="pagination-button"
      >
        »
      </button>
    </div>
  );
};

// Danh sách các vai trò quản lý có thể duyệt đơn
const managerRoles = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'SUPER_ADMIN'];

const LeaveRequests = () => {
  const { user } = useAuth();
  const token = localStorage.getItem('authToken')
  const navigate = useNavigate();
  const [leaveRequests, setLeaveRequests] = useState({
    asEmployee: [],
    asSupervisor: [],
    allRequests: []
  });
  const [groupedRequests, setGroupedRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  
  // States for infinite scroll
  const [displayedMyRequests, setDisplayedMyRequests] = useState([]);
  const [myRequestsToShow, setMyRequestsToShow] = useState(20);
  const [isMyRequestsLoading, setIsMyRequestsLoading] = useState(false);
  
  const [displayedApprovalRequests, setDisplayedApprovalRequests] = useState([]);
  const [approvalRequestsToShow, setApprovalRequestsToShow] = useState(20);
  const [isApprovalRequestsLoading, setIsApprovalRequestsLoading] = useState(false);
  
  const [displayedAllRequests, setDisplayedAllRequests] = useState([]);
  const [allRequestsToShow, setAllRequestsToShow] = useState(20);
  const [isAllRequestsLoading, setIsAllRequestsLoading] = useState(false);
  const [showAttachmentModal, setShowAttachmentModal] = useState(false); // Modal riêng cho xem file đính kèm
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [approvalData, setApprovalData] = useState({
    status: 'approved',
    comments: ''
  });
  const [tableFilters, setTableFilters] = useState({
    employeeName: '',
    supervisorName: '',
    status: ''
  });
  const [tableCurrentPage, setTableCurrentPage] = useState(1);
  const [tableRecordsPerPage, setTableRecordsPerPage] = useState(10);
  
  // Thêm state cho modal chỉnh sửa
  const [showEditModal, setShowEditModal] = useState(false);
  const [editData, setEditData] = useState({
    startDate: '',
    endDate: '',
    reason: '',
    status: '',
    supervisor: '', // Thêm trường supervisor
    visibility: 'all',
    visibleTo: [],
    hiddenFrom: []
  });
  
  // State cho danh sách người dùng (để chọn trong visibility settings)
  const [allUsers, setAllUsers] = useState([]);
  
  // Thêm state cho modal xác nhận xóa
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [requestToDelete, setRequestToDelete] = useState(null);
  
  // Thêm state cho thông báo thành công
  const [successMessage, setSuccessMessage] = useState('');
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  
  // State cho file đính kèm
  const [attachments, setAttachments] = useState([]);
  const [loadingAttachments, setLoadingAttachments] = useState(false);
  const [attachmentError, setAttachmentError] = useState(null);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadError, setUploadError] = useState(null);
  const [uploadingFiles, setUploadingFiles] = useState(false);
  const fileInputRef = useRef(null);

  useEffect(() => {
    if (!token) {
      setLoading(false);
      return;
    }
  
    setLoading(true);
  
    const fetchLeaveRequests = async () => {
      try {
        // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js
        const res = await API.get('/leave-requests');
        console.log('API trả về:', res.data);
        
        // Kiểm tra dữ liệu trả về từ API
        if (user && (user.role === 'SUPER_ADMIN' || user.role === 'LEVEL_II_MANAGER')) {
          console.log(`Người dùng là ${user.role}`);
          console.log('Số lượng đơn trong allRequests:', res.data.allRequests?.length || 0);
        }
        
        // Cập nhật state với dữ liệu từ API
        setLeaveRequests({
          asEmployee: res.data.asEmployee || [],
          asSupervisor: res.data.asSupervisor || [],
          allRequests: res.data.allRequests || []
        });
        
        // Nhóm các đơn có cùng thời gian và lý do
        const grouped = groupLeaveRequests(res.data.asEmployee || []);
        setGroupedRequests(grouped);
      } catch (err) {
        console.error('Lỗi lấy đơn nghỉ:', err);
        setError('Không thể lấy danh sách đơn. Vui lòng thử lại.');
      } finally {
        setLoading(false);
      }
    };
  
    fetchLeaveRequests();
  }, [token]);
  
  // Lấy danh sách tất cả người dùng (cho SUPER_ADMIN và LEVEL_II_MANAGER)
  useEffect(() => {
    if (user && (user.role === 'SUPER_ADMIN' || user.role === 'LEVEL_II_MANAGER')) {
      const fetchAllUsers = async () => {
        try {
          console.log('Đang lấy danh sách người dùng...');
          // Gọi API đúng đường dẫn, không thêm '/api' vì đã có trong baseURL
          const res = await API.get('/users');
          console.log('Kết quả API:', res);
          
          // Kiểm tra cấu trúc dữ liệu trả về
          if (res.data && res.data.users && Array.isArray(res.data.users)) {
            setAllUsers(res.data.users);
            console.log(`Đã tải danh sách ${res.data.users.length} người dùng cho thiết lập quyền xem đơn`);
          } else {
            console.error('Dữ liệu người dùng không đúng định dạng:', res.data);
            setAllUsers([]); // Đặt giá trị mặc định là mảng rỗng
          }
        } catch (err) {
          console.error('Lỗi lấy danh sách người dùng:', err.message);
          console.error('Chi tiết lỗi:', err);
          setAllUsers([]); // Đặt giá trị mặc định là mảng rỗng khi có lỗi
        }
      };
      
      fetchAllUsers();
    } else {
      // Đảm bảo allUsers luôn là mảng rỗng khi không phải SUPER_ADMIN
      setAllUsers([]);
    }
  }, [user]);
  
  // Infinite scroll setup for My Requests
  useEffect(() => {
    const handleMyRequestsScroll = () => {
      const container = document.getElementById('myRequestsContainer');
      if (!container) return;
      
      const { scrollTop, scrollHeight, clientHeight } = container;
      
      if (scrollTop + clientHeight >= scrollHeight - 20 && !isMyRequestsLoading) {
        loadMoreMyRequests();
      }
    };

    const container = document.getElementById('myRequestsContainer');
    if (container) {
      container.addEventListener('scroll', handleMyRequestsScroll);
      return () => container.removeEventListener('scroll', handleMyRequestsScroll);
    }
  }, [isMyRequestsLoading, myRequestsToShow, groupedRequests.length]);

  // Infinite scroll setup for Approval Requests
  useEffect(() => {
    const handleApprovalRequestsScroll = () => {
      const container = document.getElementById('approvalRequestsContainer');
      if (!container) return;
      
      const { scrollTop, scrollHeight, clientHeight } = container;
      
      if (scrollTop + clientHeight >= scrollHeight - 20 && !isApprovalRequestsLoading) {
        loadMoreApprovalRequests();
      }
    };

    const container = document.getElementById('approvalRequestsContainer');
    if (container) {
      container.addEventListener('scroll', handleApprovalRequestsScroll);
      return () => container.removeEventListener('scroll', handleApprovalRequestsScroll);
    }
  }, [isApprovalRequestsLoading, approvalRequestsToShow, leaveRequests.asSupervisor.length]);

  // Infinite scroll setup for All Requests
  useEffect(() => {
    const handleAllRequestsScroll = () => {
      const container = document.getElementById('allRequestsContainer');
      if (!container) return;
      
      const { scrollTop, scrollHeight, clientHeight } = container;
      
      if (scrollTop + clientHeight >= scrollHeight - 20 && !isAllRequestsLoading) {
        loadMoreAllRequests();
      }
    };

    const container = document.getElementById('allRequestsContainer');
    if (container) {
      container.addEventListener('scroll', handleAllRequestsScroll);
      return () => container.removeEventListener('scroll', handleAllRequestsScroll);
    }
  }, [isAllRequestsLoading, allRequestsToShow, leaveRequests.allRequests.length]);

  // Update displayed requests when data changes
  useEffect(() => {
    setDisplayedMyRequests(groupedRequests.slice(0, myRequestsToShow));
  }, [groupedRequests, myRequestsToShow]);

  useEffect(() => {
    setDisplayedApprovalRequests(leaveRequests.asSupervisor.slice(0, approvalRequestsToShow));
  }, [leaveRequests.asSupervisor, approvalRequestsToShow]);

  useEffect(() => {
    const filteredRequests = filterTableRecords(tableFilters);
    setDisplayedAllRequests(filteredRequests.slice(0, allRequestsToShow));
  }, [leaveRequests.allRequests, tableFilters, allRequestsToShow]);

  // Load more functions
  const loadMoreMyRequests = () => {
    if (isMyRequestsLoading || displayedMyRequests.length >= groupedRequests.length) return;
    
    setIsMyRequestsLoading(true);
    setTimeout(() => {
      const nextRequestsToShow = Math.min(myRequestsToShow + 20, groupedRequests.length);
      setMyRequestsToShow(nextRequestsToShow);
      setIsMyRequestsLoading(false);
    }, 300);
  };

  const loadMoreApprovalRequests = () => {
    if (isApprovalRequestsLoading || displayedApprovalRequests.length >= leaveRequests.asSupervisor.length) return;
    
    setIsApprovalRequestsLoading(true);
    setTimeout(() => {
      const nextRequestsToShow = Math.min(approvalRequestsToShow + 20, leaveRequests.asSupervisor.length);
      setApprovalRequestsToShow(nextRequestsToShow);
      setIsApprovalRequestsLoading(false);
    }, 300);
  };

  const loadMoreAllRequests = () => {
    const filteredRequests = filterTableRecords(tableFilters);
    if (isAllRequestsLoading || displayedAllRequests.length >= filteredRequests.length) return;
    
    setIsAllRequestsLoading(true);
    setTimeout(() => {
      const nextRequestsToShow = Math.min(allRequestsToShow + 20, filteredRequests.length);
      setAllRequestsToShow(nextRequestsToShow);
      setIsAllRequestsLoading(false);
    }, 300);
  };


  // Hàm nhóm các đơn có cùng thời gian và lý do
  const groupLeaveRequests = (requests) => {
    const requestGroups = {};
    
    requests.forEach(request => {
      // Tạo key dựa trên startDate, startPeriod, endDate, endPeriod và reason
      const startPeriod = request.startPeriod || 'full_day';
      const endPeriod = request.endPeriod || 'full_day';
      const key = `${request.startDate}_${startPeriod}_${request.endDate}_${endPeriod}_${request.reason}`;
      
      if (!requestGroups[key]) {
        requestGroups[key] = {
          id: key,
          _id: request._id, // Thêm _id từ request đầu tiên
          startDate: request.startDate,
          startPeriod: request.startPeriod || 'full_day', // Thêm startPeriod với giá trị mặc định
          endDate: request.endDate,
          endPeriod: request.endPeriod || 'full_day', // Thêm endPeriod với giá trị mặc định
          reason: request.reason,
          createdAt: request.createdAt,
          supervisors: [],
          status: request.status,
          comments: request.comments || '',
          hasAttachments: request.hasAttachments || false,
          attachmentCount: request.attachmentCount || 0,
          requests: []
        };
      }
      
      // Thêm supervisor vào nhóm
      if (request.supervisor && !requestGroups[key].supervisors.some(s => s._id === request.supervisor._id)) {
        requestGroups[key].supervisors.push(request.supervisor);
      }
      
      // Thêm request vào nhóm
      requestGroups[key].requests.push(request);
      
      // Nếu bất kỳ request nào là pending, nhóm sẽ hiển thị là pending
      if (request.status === 'pending') {
        requestGroups[key].status = 'pending';
      }
      // Nếu có bất kỳ request nào bị reject mà chưa có request nào pending, nhóm sẽ hiển thị là rejected
      else if (request.status === 'rejected' && requestGroups[key].status !== 'pending') {
        requestGroups[key].status = 'rejected';
      }
      
      // Tổng hợp comments từ các quản lý
      if (request.comments && !requestGroups[key].comments.includes(request.comments)) {
        requestGroups[key].comments += (requestGroups[key].comments ? '\n' : '') + 
          `${request.supervisor?.fullName || 'Quản lý'}: ${request.comments}`;
      }
      
      // Cập nhật thông tin file đính kèm
      if (request.hasAttachments) {
        requestGroups[key].hasAttachments = true;
        requestGroups[key].attachmentCount = Math.max(requestGroups[key].attachmentCount || 0, request.attachmentCount || 0);
      }
    });
    
    return Object.values(requestGroups);
  };

  const handleCreateRequest = () => {
    navigate('/leave-request/create');
  };

  // Hàm lấy danh sách file đính kèm
  const fetchAttachments = async (leaveRequestId) => {
    // Kiểm tra leaveRequestId có hợp lệ không
    if (!leaveRequestId) {
      console.error('ID đơn nghỉ phép không hợp lệ');
      setAttachmentError('Không thể lấy danh sách file đính kèm');
      setLoadingAttachments(false);
      setAttachments([]); // Đặt danh sách file rỗng
      return;
    }
    
    // Kiểm tra leaveRequestId có phải là chuỗi "undefined" không
    if (leaveRequestId === 'undefined') {
      console.error('ID đơn nghỉ phép là chuỗi "undefined"');
      setAttachmentError('Không thể lấy danh sách file đính kèm');
      setLoadingAttachments(false);
      setAttachments([]); // Đặt danh sách file rỗng
      return;
    }
    
    setLoadingAttachments(true);
    setAttachmentError(null);
    
    try {
      console.log('Đang lấy danh sách file đính kèm cho đơn có ID:', leaveRequestId);
      const response = await API.get(`/file-attachments/${leaveRequestId}/files`, {
        headers: { 'x-auth-token': token }
      });
      
      setAttachments(response.data);
      console.log('Đã lấy danh sách file đính kèm:', response.data);
    } catch (err) {
      console.error('Lỗi khi lấy danh sách file đính kèm:', err);
      setAttachmentError('Không thể tải danh sách file đính kèm');
      setAttachments([]); // Đặt danh sách file rỗng
    } finally {
      setLoadingAttachments(false);
    }
  };
  
  // Hàm xử lý khi chọn file
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    
    // Kiểm tra số lượng file (tối đa 5 file)
    if (selectedFiles.length + files.length > 5) {
      setUploadError('Chỉ được phép tải lên tối đa 5 file');
      return;
    }
    
    // Kiểm tra kích thước file (tối đa 10MB mỗi file)
    const maxSize = 10 * 1024 * 1024; // 10MB
    const oversizedFiles = files.filter(file => file.size > maxSize);
    if (oversizedFiles.length > 0) {
      setUploadError(`File ${oversizedFiles.map(f => f.name).join(', ')} vượt quá kích thước tối đa 10MB`);
      return;
    }
    
    // Kiểm tra loại file
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain'
    ];
    
    const invalidFiles = files.filter(file => !allowedTypes.includes(file.type));
    if (invalidFiles.length > 0) {
      setUploadError(`File ${invalidFiles.map(f => f.name).join(', ')} không được hỗ trợ. Chỉ chấp nhận PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF và TXT.`);
      return;
    }
    
    // Thêm các file hợp lệ vào danh sách
    setSelectedFiles(prev => [...prev, ...files]);
    setUploadError(null);
    
    // Reset input để có thể chọn lại cùng một file
    e.target.value = null;
  };
  
  // Hàm xóa file khỏi danh sách
  const handleRemoveFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };
  
  // Hàm lấy icon tương ứng với loại file
  const getFileIcon = (fileType) => {
    // Kiểm tra fileType có tồn tại không
    if (!fileType) return <FaFile />;
    
    // Chuyển đổi fileType thành chuỗi để đảm bảo có thể gọi includes()
    const type = String(fileType).toLowerCase();
    
    if (type.includes('pdf')) return <FaFilePdf />;
    if (type.includes('word') || type.includes('msword') || type.includes('doc')) return <FaFileWord />;
    if (type.includes('excel') || type.includes('sheet') || type.includes('xls')) return <FaFileExcel />;
    if (type.includes('image') || type.includes('jpg') || type.includes('jpeg') || type.includes('png') || type.includes('gif')) return <FaFileImage />;
    return <FaFile />;
  };
  
  // Hàm upload file
  const uploadFiles = async (leaveRequestId) => {
    if (selectedFiles.length === 0) return;
    
    // Kiểm tra leaveRequestId có hợp lệ không
    if (!leaveRequestId) {
      console.error('ID đơn nghỉ phép không hợp lệ');
      setUploadError('Không thể tải file lên. Vui lòng thử lại sau.');
      return;
    }
    
    setUploadingFiles(true);
    setUploadError(null);
    
    // Hiển thị thông báo đang tải lên
    setSuccessMessage('Đang tải file lên...');
    setShowSuccessAlert(true);
    
    try {
      const formData = new FormData();
      selectedFiles.forEach(file => {
        formData.append('files', file);
      });
      
      await API.post(
        `/file-attachments/${leaveRequestId}/upload`,
        formData,
        {
          headers: {
            'x-auth-token': token,
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      
      // Sau khi upload thành công, làm mới danh sách file
      await fetchAttachments(leaveRequestId);
      
      // Xóa danh sách file đã chọn
      setSelectedFiles([]);
      
      // Hiển thị thông báo thành công
      setSuccessMessage('Tải file lên thành công');
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 3000);
    } catch (err) {
      console.error('Lỗi khi tải file lên:', err);
      setUploadError('Lỗi khi tải file lên: ' + (err.response?.data?.message || err.message));
    } finally {
      setUploadingFiles(false);
    }
  };
  
  // Hàm download file
  const handleDownloadFile = async (fileId) => {
    try {
      // Kiểm tra selectedRequest và fileId có hợp lệ không
      if (!selectedRequest || !selectedRequest._id) {
        console.error('Không có đơn nghỉ phép được chọn');
        alert('Không thể tải file. Vui lòng thử lại sau.');
        return;
      }
      
      if (!fileId) {
        console.error('ID file không hợp lệ');
        alert('Không thể tải file. Vui lòng thử lại sau.');
        return;
      }
      
      const response = await API.get(`/file-attachments/${selectedRequest._id}/files/${fileId}/download`, {
        headers: { 'x-auth-token': token },
        responseType: 'blob'
      });
      
      // Tìm thông tin file trong danh sách attachments
      const fileInfo = attachments.find(file => file._id === fileId);
      
      // Tạo URL cho blob và tạo link tải xuống
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileInfo.originalName);
      document.body.appendChild(link);
      link.click();
      
      // Dọn dẹp
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Lỗi khi tải file:', err);
      alert('Không thể tải file. Vui lòng thử lại sau.');
    }
  };
  
  // Hàm xóa file
  const handleDeleteFile = async (fileId) => {
    if (!confirm('Bạn có chắc chắn muốn xóa file này không?')) {
      return;
    }
    
    // Kiểm tra selectedRequest và fileId có hợp lệ không
    if (!selectedRequest || !selectedRequest._id) {
      console.error('Không có đơn nghỉ phép được chọn');
      alert('Không thể xóa file. Vui lòng thử lại sau.');
      return;
    }
    
    if (!fileId) {
      console.error('ID file không hợp lệ');
      alert('Không thể xóa file. Vui lòng thử lại sau.');
      return;
    }
    
    try {
      await API.delete(`/file-attachments/${selectedRequest._id}/files/${fileId}`, {
        headers: { 'x-auth-token': token }
      });
      
      // Sau khi xóa thành công, làm mới danh sách file
      await fetchAttachments(selectedRequest._id);
      
      // Hiển thị thông báo thành công
      setSuccessMessage('Xóa file thành công');
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 3000);
    } catch (err) {
      console.error('Lỗi khi xóa file:', err);
      alert('Không thể xóa file. Vui lòng thử lại sau.');
    }
  };

  const handleShowApprovalModal = async (request) => {
    // Kiểm tra request có hợp lệ không
    if (!request) {
      console.error('Request không hợp lệ');
      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 3000);
      return;
    }
    
    // Kiểm tra request có ID hợp lệ không
    if (!request._id) {
      console.error('Request không có ID hợp lệ:', request);
      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 3000);
      return;
    }
    
    setSelectedRequest(request);
    setApprovalData({
      status: 'approved',
      comments: ''
    });
    setShowModal(true);
    
    // Lấy danh sách file đính kèm
    try {
      await fetchAttachments(request._id);
    } catch (err) {
      console.error('Lỗi khi lấy danh sách file đính kèm:', err);
      // Không hiển thị lỗi cho người dùng vì modal đã được mở
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedRequest(null);
  };
  
  // Hàm hiển thị modal xem file đính kèm
  const handleShowAttachmentModal = async (request) => {
    // Kiểm tra request có hợp lệ không
    if (!request) {
      console.error('Request không hợp lệ');
      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 3000);
      return;
    }
    
    // Kiểm tra request có ID hợp lệ không
    if (!request._id) {
      console.error('Request không có ID hợp lệ:', request);
      setSuccessMessage('Không thể hiển thị chi tiết đơn. Vui lòng thử lại sau.');
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 3000);
      return;
    }
    
    setSelectedRequest(request);
    setShowAttachmentModal(true);
    
    // Lấy danh sách file đính kèm
    try {
      await fetchAttachments(request._id);
    } catch (err) {
      console.error('Lỗi khi lấy danh sách file đính kèm:', err);
      // Không hiển thị lỗi cho người dùng vì modal đã được mở
    }
  };
  
  // Hàm đóng modal xem file đính kèm
  const handleCloseAttachmentModal = () => {
    setShowAttachmentModal(false);
    setSelectedRequest(null);
  };

  const handleApprovalChange = (e) => {
    setApprovalData({
      ...approvalData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmitApproval = async (e) => {
    e.preventDefault();
    
    try {
      console.log(`Đang duyệt đơn nghỉ phép với ID: ${selectedRequest._id}`, approvalData);
      
      // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js
      await API.put(
        `/leave-requests/${selectedRequest._id}`,
        approvalData
      );

      // Cập nhật trạng thái trong tất cả các danh sách
      setLeaveRequests(prev => {
        // Cập nhật trong danh sách asSupervisor
        const updatedAsSupervisor = prev.asSupervisor.map(req => 
          req._id === selectedRequest._id 
            ? { ...req, status: approvalData.status, comments: approvalData.comments } 
            : req
        );
        
        // Cập nhật trong danh sách allRequests
        const updatedAllRequests = prev.allRequests.map(req => 
          req._id === selectedRequest._id 
            ? { ...req, status: approvalData.status, comments: approvalData.comments } 
            : req
        );
        
        // Cập nhật trong danh sách asEmployee nếu có
        const updatedAsEmployee = prev.asEmployee.map(req => 
          req._id === selectedRequest._id 
            ? { ...req, status: approvalData.status, comments: approvalData.comments } 
            : req
        );
        
        return {
          asEmployee: updatedAsEmployee,
          asSupervisor: updatedAsSupervisor,
          allRequests: updatedAllRequests
        };
      });
      
      // Cập nhật groupedRequests nếu đơn được duyệt/từ chối là của người dùng hiện tại
      const isUserRequest = leaveRequests.asEmployee.some(req => req._id === selectedRequest._id);
      if (isUserRequest) {
        const updatedGrouped = groupLeaveRequests(
          leaveRequests.asEmployee.map(req => 
            req._id === selectedRequest._id 
              ? { ...req, status: approvalData.status, comments: approvalData.comments } 
              : req
          )
        );
        setGroupedRequests(updatedGrouped);
      }

      // Hiển thị thông báo thành công
      setSuccessMessage('Đã cập nhật trạng thái đơn thành công');
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 3000);

      // Đóng modal
      handleCloseModal();
      
      // Làm mới dữ liệu sau 500ms để đảm bảo API đã cập nhật xong
      setTimeout(async () => {
        try {
          const res = await API.get('/leave-requests');
          
          // Cập nhật state với dữ liệu mới từ API
          setLeaveRequests({
            asEmployee: res.data.asEmployee || [],
            asSupervisor: res.data.asSupervisor || [],
            allRequests: res.data.allRequests || []
          });
          
          // Cập nhật groupedRequests
          const grouped = groupLeaveRequests(res.data.asEmployee || []);
          setGroupedRequests(grouped);
        } catch (err) {
          console.error('Lỗi khi làm mới dữ liệu:', err);
        }
      }, 500);
    } catch (err) {
      console.error('Lỗi khi cập nhật đơn xin nghỉ phép:', err);
      setError('Không thể cập nhật đơn xin nghỉ phép. Vui lòng thử lại sau.');
      // Hiển thị thông báo lỗi chi tiết
      alert(`Lỗi: ${err.response?.data?.msg || err.message}`);
    }
  };
  
  // Hàm mở modal chỉnh sửa đơn
  const handleShowEditModal = (request) => {
    // Chuyển đổi định dạng ngày tháng từ ISO sang YYYY-MM-DD cho input date
    const formatDateForInput = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };
    
    // Kiểm tra quyền chỉnh sửa
    const isAdmin = user && user.role === 'SUPER_ADMIN';
    
    // Kiểm tra quyền sở hữu đơn - xử lý nhiều trường hợp khác nhau
    let isOwner = false;
    
    // Trường hợp 1: request có thuộc tính employee với _id
    if (request.employee && request.employee._id === user._id) {
      isOwner = true;
    }
    // Trường hợp 2: request có thuộc tính employeeId
    else if (request.employeeId === user._id) {
      isOwner = true;
    }
    // Trường hợp 3: request có thuộc tính employee với id
    else if (request.employee && request.employee.id === user._id) {
      isOwner = true;
    }
    // Trường hợp 4: Đơn được tạo bởi người dùng hiện tại (dựa vào createdBy)
    else if (request.createdBy === user._id) {
      isOwner = true;
    }
    
    // Debug: Ghi log thông tin để kiểm tra
    console.log('User ID:', user?._id);
    console.log('Request:', request);
    console.log('Is owner:', isOwner);
    
    const isPending = request.status === 'pending';
    
    // Kiểm tra quyền chỉnh sửa:
    // 1. Admin có thể chỉnh sửa bất kỳ đơn nào
    // 2. Nhân viên chỉ có thể chỉnh sửa đơn của mình và đơn phải ở trạng thái chờ duyệt
    if (isAdmin) {
      // Admin có thể chỉnh sửa bất kỳ đơn nào
    } else if (isOwner && isPending) {
      // Nhân viên có thể chỉnh sửa đơn của mình nếu đơn đang ở trạng thái chờ duyệt
    } else {
      // Các trường hợp khác không được phép chỉnh sửa
      if (!isOwner) {
        alert('Bạn không có quyền chỉnh sửa đơn này vì đây không phải đơn của bạn');
      } else if (!isPending) {
        alert('Bạn không thể chỉnh sửa đơn này vì đơn đã được duyệt/từ chối');
      } else {
        alert('Bạn không có quyền chỉnh sửa đơn này');
      }
      return;
    }
    
    setSelectedRequest(request);
    setEditData({
      startDate: formatDateForInput(request.startDate),
      startPeriod: request.startPeriod || 'full_day',
      endDate: formatDateForInput(request.endDate),
      endPeriod: request.endPeriod || 'full_day',
      reason: request.reason,
      status: request.status,
      supervisor: request.supervisor?._id || request.supervisor || '', // Lưu ID của cấp trên
      visibility: request.visibility || 'all',
      visibleTo: request.visibleTo || [],
      hiddenFrom: request.hiddenFrom || []
    });
    setShowEditModal(true);
    
    // Lấy danh sách file đính kèm
    fetchAttachments(request._id);
  };
  
  // Hàm đóng modal chỉnh sửa
  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedRequest(null);
  };
  
  // Hàm xử lý thay đổi dữ liệu chỉnh sửa
  const handleEditChange = (e) => {
    const { name, value } = e.target;
    
    setEditData(prev => {
      const newState = { ...prev, [name]: value };
      
      // Xử lý logic đặc biệt cho ngày tháng
      if (name === 'startDate' || name === 'endDate') {
        // Kiểm tra nếu ngày bắt đầu và kết thúc là cùng một ngày
        const startDate = name === 'startDate' ? value : prev.startDate;
        const endDate = name === 'endDate' ? value : prev.endDate;
        const isSameDay = new Date(startDate).setHours(0,0,0,0) === new Date(endDate).setHours(0,0,0,0);
        
        // Nếu cùng một ngày, áp dụng các quy tắc thời gian kết thúc
        if (isSameDay) {
          if (prev.startPeriod === 'full_day') {
            newState.endPeriod = 'full_day';
          } else if (prev.startPeriod === 'afternoon') {
            newState.endPeriod = 'afternoon';
          }
        }
      }
      
      return newState;
    });
  };
  
  // Hàm gửi dữ liệu chỉnh sửa
  const handleSubmitEdit = async (e) => {
    e.preventDefault();
    
    try {
      // Tạo dữ liệu để gửi lên server
      const dataToSubmit = { ...editData };
      
      // Nếu không phải admin, không cho phép thay đổi trạng thái và visibility
      if (user.role !== 'SUPER_ADMIN') {
        // Xóa các trường không được phép thay đổi
        delete dataToSubmit.status;
        delete dataToSubmit.visibility;
        delete dataToSubmit.visibleTo;
        delete dataToSubmit.hiddenFrom;
      }
      
      console.log(`Đang cập nhật đơn nghỉ phép với ID: ${selectedRequest._id}`, dataToSubmit);
      
      // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js
      const response = await API.put(
        `/leave-requests/${selectedRequest._id}`,
        dataToSubmit
      );
      
      // Cập nhật state với dữ liệu mới
      const updatedRequest = response.data;
      
      // Cập nhật các danh sách đơn
      setLeaveRequests(prev => {
        // Cập nhật trong danh sách asEmployee
        const updatedAsEmployee = prev.asEmployee.map(req => 
          req._id === updatedRequest._id ? updatedRequest : req
        );
        
        // Cập nhật trong danh sách asSupervisor
        const updatedAsSupervisor = prev.asSupervisor.map(req => 
          req._id === updatedRequest._id ? updatedRequest : req
        );
        
        // Cập nhật trong danh sách allRequests
        const updatedAllRequests = prev.allRequests.map(req => 
          req._id === updatedRequest._id ? updatedRequest : req
        );
        
        return {
          asEmployee: updatedAsEmployee,
          asSupervisor: updatedAsSupervisor,
          allRequests: updatedAllRequests
        };
      });
      
      // Cập nhật groupedRequests
      const updatedGrouped = groupLeaveRequests(leaveRequests.asEmployee.map(req => 
        req._id === updatedRequest._id ? updatedRequest : req
      ));
      setGroupedRequests(updatedGrouped);
      
      // Hiển thị thông báo thành công
      setSuccessMessage('Đã cập nhật đơn nghỉ phép thành công');
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 3000);
      
      handleCloseEditModal();
    } catch (err) {
      console.error('Lỗi khi cập nhật đơn xin nghỉ phép:', err);
      setError('Không thể cập nhật đơn xin nghỉ phép. Vui lòng thử lại sau.');
      // Hiển thị thông báo lỗi chi tiết
      alert(`Lỗi: ${err.response?.data?.msg || err.message}`);
    }
  };
  
  // Hàm mở modal xác nhận xóa
  const handleShowDeleteModal = (request) => {
    // Kiểm tra quyền xóa
    const isAdmin = user && user.role === 'SUPER_ADMIN';
    
    // Kiểm tra quyền sở hữu đơn - xử lý nhiều trường hợp khác nhau
    let isOwner = false;
    
    // Trường hợp 1: request có thuộc tính employee với _id
    if (request.employee && request.employee._id === user._id) {
      isOwner = true;
    }
    // Trường hợp 2: request có thuộc tính employeeId
    else if (request.employeeId === user._id) {
      isOwner = true;
    }
    // Trường hợp 3: request có thuộc tính employee với id
    else if (request.employee && request.employee.id === user._id) {
      isOwner = true;
    }
    // Trường hợp 4: Đơn được tạo bởi người dùng hiện tại (dựa vào createdBy)
    else if (request.createdBy === user._id) {
      isOwner = true;
    }
    
    // Debug: Ghi log thông tin để kiểm tra
    console.log('Delete - User ID:', user?._id);
    console.log('Delete - Request:', request);
    console.log('Delete - Is owner:', isOwner);
    
    const isPending = request.status === 'pending';
    
    // Kiểm tra quyền xóa:
    // 1. Admin có thể xóa bất kỳ đơn nào
    // 2. Nhân viên chỉ có thể xóa đơn của mình và đơn phải ở trạng thái chờ duyệt
    if (isAdmin) {
      // Admin có thể xóa bất kỳ đơn nào
    } else if (isOwner && isPending) {
      // Nhân viên có thể xóa đơn của mình nếu đơn đang ở trạng thái chờ duyệt
    } else {
      // Các trường hợp khác không được phép xóa
      if (!isOwner) {
        alert('Bạn không có quyền xóa đơn này vì đây không phải đơn của bạn');
      } else if (!isPending) {
        alert('Bạn không thể xóa đơn này vì đơn đã được duyệt/từ chối');
      } else {
        alert('Bạn không có quyền xóa đơn này');
      }
      return;
    }
    
    setRequestToDelete(request);
    setShowDeleteModal(true);
  };
  
  // Hàm đóng modal xác nhận xóa
  const handleCloseDeleteModal = () => {
    setShowDeleteModal(false);
    setRequestToDelete(null);
  };
  
  // Hàm xóa đơn nghỉ phép
  const handleDeleteRequest = async () => {
    try {
      console.log(`Đang xóa đơn nghỉ phép với ID: ${requestToDelete._id}`);
      
      // Không cần thêm tiền tố /api vì đã được cấu hình trong api.js
      await API.delete(`/leave-requests/${requestToDelete._id}`);
      
      // Cập nhật state sau khi xóa
      setLeaveRequests(prev => ({
        asEmployee: prev.asEmployee.filter(req => req._id !== requestToDelete._id),
        asSupervisor: prev.asSupervisor.filter(req => req._id !== requestToDelete._id),
        allRequests: prev.allRequests.filter(req => req._id !== requestToDelete._id)
      }));
      
      // Cập nhật groupedRequests
      const updatedGrouped = groupLeaveRequests(
        leaveRequests.asEmployee.filter(req => req._id !== requestToDelete._id)
      );
      setGroupedRequests(updatedGrouped);
      
      // Hiển thị thông báo thành công
      setSuccessMessage('Đã xóa đơn nghỉ phép thành công');
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 3000);
      
      handleCloseDeleteModal();
    } catch (err) {
      console.error('Lỗi khi xóa đơn xin nghỉ phép:', err);
      
      // Kiểm tra nếu lỗi là 500 nhưng đơn vẫn được xóa (lỗi khi tạo thông báo)
      if (err.response?.status === 500 && err.response?.data?.error?.includes('Notification validation failed')) {
        // Vẫn cập nhật UI như đã xóa thành công
        setLeaveRequests(prev => ({
          asEmployee: prev.asEmployee.filter(req => req._id !== requestToDelete._id),
          asSupervisor: prev.asSupervisor.filter(req => req._id !== requestToDelete._id),
          allRequests: prev.allRequests.filter(req => req._id !== requestToDelete._id)
        }));
        
        // Cập nhật groupedRequests
        const updatedGrouped = groupLeaveRequests(
          leaveRequests.asEmployee.filter(req => req._id !== requestToDelete._id)
        );
        setGroupedRequests(updatedGrouped);
        
        // Hiển thị thông báo thành công
        setSuccessMessage('Đã xóa đơn nghỉ phép thành công (có lỗi thông báo)');
        setShowSuccessAlert(true);
        setTimeout(() => setShowSuccessAlert(false), 3000);
        
        handleCloseDeleteModal();
      } else {
        setError('Không thể xóa đơn xin nghỉ phép. Vui lòng thử lại sau.');
        // Hiển thị thông báo lỗi chi tiết
        alert(`Lỗi: ${err.response?.data?.msg || err.message}`);
      }
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return <Badge bg="warning">Chờ duyệt</Badge>;
      case 'approved':
        return <Badge bg="success">Đã duyệt</Badge>;
      case 'rejected':
        return <Badge bg="danger">Đã từ chối</Badge>;
      case 'cancelled':
        return <Badge bg="secondary">Đã hủy</Badge>;
      default:
        return <Badge bg="secondary">Không xác định</Badge>;
    }
  };
  
  // Hiển thị biểu tượng file đính kèm
  const getAttachmentBadge = (request) => {
    if (request && request.hasAttachments) {
      // Kiểm tra xem request có ID hợp lệ không
      const hasValidId = request._id || (request.requests && request.requests.length > 0 && request.requests[0]._id);
      
      if (!hasValidId) {
        console.warn('Đơn nghỉ phép không có ID hợp lệ:', request);
        return (
          <Badge bg="secondary" title="Không thể xem file đính kèm">
            <FaFile className="me-1" /> {request.attachmentCount}
          </Badge>
        );
      }
      
      return (
        <OverlayTrigger
          placement="top"
          overlay={<Tooltip>Xem {request.attachmentCount} file đính kèm</Tooltip>}
        >
          <Button 
            variant="outline-info" 
            size="sm"
            className="d-flex align-items-center file-attachment-btn"
            onClick={() => {
              // Tìm ID hợp lệ cho đơn nghỉ phép
              let validRequest = {...request};
              
              // Nếu request không có _id nhưng có requests array, sử dụng _id của request đầu tiên
              if (!validRequest._id && validRequest.requests && validRequest.requests.length > 0) {
                validRequest._id = validRequest.requests[0]._id;
              }
              
              // Mở modal duyệt đơn
              handleShowApprovalModal(validRequest);
            }}
          >
            <FaFile className="me-1" /> {request.attachmentCount}
          </Button>
        </OverlayTrigger>
      );
    }
    return <span className="text-muted">-</span>;
  };
  
  // Hiển thị biểu tượng file đính kèm cho tab "Đơn xin nghỉ phép của tôi"
  const getMyRequestAttachmentBadge = (request) => {
    if (request && request.hasAttachments) {
      // Kiểm tra xem request có ID hợp lệ không
      const hasValidId = request._id || (request.requests && request.requests.length > 0 && request.requests[0]._id);
      
      if (!hasValidId) {
        console.warn('Đơn nghỉ phép không có ID hợp lệ:', request);
        return (
          <Badge bg="secondary" title="Không thể xem file đính kèm">
            <FaFile className="me-1" /> {request.attachmentCount}
          </Badge>
        );
      }
      
      return (
        <OverlayTrigger
          placement="top"
          overlay={<Tooltip>Xem {request.attachmentCount} file đính kèm</Tooltip>}
        >
          <Button 
            variant="outline-info" 
            size="sm"
            className="d-flex align-items-center file-attachment-btn"
            onClick={() => {
              // Tìm ID hợp lệ cho đơn nghỉ phép
              let validRequest = {...request};
              
              // Nếu request không có _id nhưng có requests array, sử dụng _id của request đầu tiên
              if (!validRequest._id && validRequest.requests && validRequest.requests.length > 0) {
                validRequest._id = validRequest.requests[0]._id;
              }
              
              // Mở modal xem file đính kèm
              handleShowAttachmentModal(validRequest);
            }}
          >
            <FaFile className="me-1" /> {request.attachmentCount}
          </Button>
        </OverlayTrigger>
      );
    }
    return <span className="text-muted">-</span>;
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    try {
      // Xử lý timezone để đảm bảo hiển thị đúng ngày theo giờ Việt Nam
      const date = new Date(dateString);
      
      // Tạo một đối tượng Date mới với giờ là 12:00 giờ Việt Nam để tránh vấn đề timezone
      // Lấy các thành phần ngày tháng năm từ đối tượng Date gốc
      const year = date.getFullYear();
      const month = date.getMonth();
      const day = date.getDate();
      
      // Tạo đối tượng Date mới với giờ cố định là 12:00
      const adjustedDate = new Date(year, month, day, 12, 0, 0);
      
      console.log(`Ngày gốc: ${dateString}, Ngày hiển thị: ${adjustedDate.toISOString()}`);
      
      // Format ngày theo định dạng Việt Nam
      return format(adjustedDate, 'dd/MM/yyyy', { locale: vi });
    } catch (error) {
      console.error('Lỗi khi format ngày:', error, dateString);
      return 'Ngày không hợp lệ';
    }
  };
  
  // Hàm định dạng thời gian nghỉ với period
  const formatLeaveTime = (startDate, startPeriod, endDate, endPeriod) => {
    const start = formatDate(startDate);
    const end = formatDate(endDate);
    
    const startText = startPeriod && startPeriod !== 'full_day' 
      ? `${start} (${startPeriod === 'morning' ? 'Sáng' : 'Chiều'})` 
      : start;
      
    const endText = endPeriod && endPeriod !== 'full_day'
      ? `${end} (${endPeriod === 'morning' ? 'Sáng' : 'Chiều'})` 
      : end;
      
    return `${startText} - ${endText}`;
  };

  // Render danh sách quản lý
  const renderSupervisors = (supervisors) => {
    if (!supervisors || supervisors.length === 0) return 'Chưa xác định';
    
    if (supervisors.length === 1) {
      return supervisors[0].fullName || 'Chưa xác định';
    }
    
    return (
      <OverlayTrigger
        placement="right"
        overlay={
          <Tooltip id="supervisors-tooltip">
            {supervisors.map(sup => sup.fullName).join(', ')}
          </Tooltip>
        }
      >
        <span>
          {supervisors[0].fullName} và {supervisors.length - 1} khác
        </span>
      </OverlayTrigger>
    );
  };

  const handleTableFilterChange = (e) => {
    setTableFilters({
      ...tableFilters,
      [e.target.name]: e.target.value
    });
  };

  const resetTableFilters = () => {
    setTableFilters({
      employeeName: '',
      supervisorName: '',
      status: ''
    });
  };

  // Calculate leave request statistics for all employees
  const calculateLeaveStatistics = () => {
    if (!leaveRequests.allRequests || leaveRequests.allRequests.length === 0) {
      return [];
    }

    const currentYear = new Date().getFullYear();
    const employeeStats = {};
    const processedRequests = new Set(); // Track processed request IDs to avoid duplicates

    leaveRequests.allRequests.forEach(request => {
      const requestYear = new Date(request.createdAt).getFullYear();
      const requestMonth = new Date(request.createdAt).getMonth();
      const employeeId = request.employee?._id;
      const employeeName = request.employee?.fullName || 'Chưa xác định';

      if (!employeeId || requestYear !== currentYear) return;

      // Create unique key based on employee + dates only (ignore reason)
      // This treats requests on same dates as one request regardless of reason
      const uniqueKey = `${employeeId}-${request.startDate}-${request.endDate}-${request.startPeriod || ''}-${request.endPeriod || ''}`;

      if (processedRequests.has(uniqueKey)) {
        return; // Skip duplicate request (same employee, same dates, same reason)
      }
      processedRequests.add(uniqueKey);

      if (!employeeStats[employeeId]) {
        employeeStats[employeeId] = {
          employeeName,
          totalRequests: 0,
          totalDays: 0,
          monthlyRequests: {},
          approvedRequests: 0,
          pendingRequests: 0,
          rejectedRequests: 0
        };
      }

      const stats = employeeStats[employeeId];
      stats.totalRequests++;

      // Calculate leave days
      const leaveDays = calculateLeaveDuration(
        request.startDate,
        request.startPeriod,
        request.endDate,
        request.endPeriod
      );
      stats.totalDays += leaveDays;

      // Track monthly requests
      const monthKey = `${requestYear}-${requestMonth}`;
      stats.monthlyRequests[monthKey] = (stats.monthlyRequests[monthKey] || 0) + 1;

      // Track by status
      if (request.status === 'approved') stats.approvedRequests++;
      else if (request.status === 'pending') stats.pendingRequests++;
      else if (request.status === 'rejected') stats.rejectedRequests++;
    });

    return Object.values(employeeStats).sort((a, b) => a.employeeName.localeCompare(b.employeeName));
  };

  // Check if employee has exceeded monthly limit
  const checkMonthlyLimit = (employeeStats) => {
    const currentMonth = `${new Date().getFullYear()}-${new Date().getMonth()}`;
    return (employeeStats.monthlyRequests[currentMonth] || 0) >= 1;
  };

  // Check if employee has exceeded annual limit
  const checkAnnualLimit = (employeeStats) => {
    return employeeStats.totalRequests >= 12;
  };

  const filterTableRecords = (filters) => {
    // Kiểm tra nếu allRequests là undefined hoặc null
    if (!leaveRequests.allRequests || leaveRequests.allRequests.length === 0) {
      console.log('Không có đơn nghỉ phép nào để lọc');
      return [];
    }
    
    return leaveRequests.allRequests.filter(request => {
      // Kiểm tra nếu employee hoặc supervisor là undefined hoặc null
      const employeeFullName = request.employee?.fullName || '';
      const supervisorFullName = request.supervisor?.fullName || '';
      
      const matchesEmployee = filters.employeeName ? 
        employeeFullName.toLowerCase().includes(filters.employeeName.toLowerCase()) : true;
      
      const matchesSupervisor = filters.supervisorName ? 
        supervisorFullName.toLowerCase().includes(filters.supervisorName.toLowerCase()) : true;
      
      const matchesStatus = filters.status ? request.status === filters.status : true;
      
      return matchesEmployee && matchesSupervisor && matchesStatus;
    });
  };

  const getTablePaginatedRecords = (records) => {
    // Kiểm tra nếu records là undefined hoặc null
    if (!records || records.length === 0) {
      console.log('Không có đơn nghỉ phép nào để hiển thị');
      return [];
    }
    
    const filteredRecords = filterTableRecords(tableFilters);
    console.log(`Số lượng đơn sau khi lọc: ${filteredRecords.length}`);
    
    const startIndex = (tableCurrentPage - 1) * tableRecordsPerPage;
    const endIndex = startIndex + tableRecordsPerPage;
    return filteredRecords.slice(startIndex, endIndex);
  };

  if (!user) {
    return (
      <Container className="mt-4">
        <Card>
          <Card.Body className="text-center">
            <p>Bạn cần đăng nhập để xem đơn xin nghỉ phép</p>
            <Button variant="primary" onClick={() => navigate('/login')}>
              Đăng nhập
            </Button>
          </Card.Body>
        </Card>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container className="mt-4">
        <Card>
          <Card.Body className="text-center">
            <p>Đang tải dữ liệu...</p>
          </Card.Body>
        </Card>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <Card>
          <Card.Body className="text-center">
            <p className="text-danger">{error}</p>
            <Button variant="primary" onClick={() => window.location.reload()}>
              Thử lại
            </Button>
          </Card.Body>
        </Card>
      </Container>
    );
  }

  return (
    <Container className="leave-requests-container">
      {/* Hiển thị thông báo thành công */}
      {showSuccessAlert && (
        <Alert 
          variant="success" 
          className="position-fixed top-0 start-50 translate-middle-x mt-3"
          style={{ zIndex: 1050 }}
          onClose={() => setShowSuccessAlert(false)} 
          dismissible
        >
          {successMessage}
        </Alert>
      )}
      <Row className="mb-4">
        <Col>
          <h2>Quản lý nghỉ phép</h2>
        </Col>
        <Col xs="auto">
          <Button variant="primary" onClick={handleCreateRequest}>
            Tạo đơn xin nghỉ phép
          </Button>
        </Col>
      </Row>

      <Tabs defaultActiveKey="my-requests" className="mb-3">
        <Tab eventKey="my-requests" title="Đơn xin nghỉ phép">
          <div className="employee-simple-container">
            <div className="employee-header">
              <h4 style={{ margin: '0', color: '#333', display: 'flex', alignItems: 'center' }}>
                <i className="fas fa-calendar-alt" style={{ marginRight: '8px' }}></i>
                Đơn xin nghỉ phép
              </h4>
              <span className="employee-count">
                {groupedRequests.length} đơn xin nghỉ phép
              </span>
            </div>
            
            {/* Danh sách đơn nghỉ phép với infinite scroll */}
            <div className="employee-list-simple" id="myRequestsContainer">
              {displayedMyRequests.length === 0 ? (
                <div className="no-employees">
                  <div className="no-employees-icon">
                    <i className="fas fa-calendar-times"></i>
                  </div>
                  <p>Bạn chưa có đơn xin nghỉ phép nào</p>
                </div>
              ) : (
                displayedMyRequests.map((request, index) => (
                  <div key={request.id} className="employee-card-simple">
                    <div className="employee-info-main">
                      <div className="employee-avatar">
                        <i className="fas fa-calendar-check"></i>
                      </div>
                      <div className="employee-details">
                        <h4>Đơn nghỉ phép #{index + 1}</h4>
                        <p><i className="fas fa-calendar"></i> {formatDate(request.createdAt)}</p>
                        <p><i className="fas fa-clock"></i> {formatLeaveTime(request.startDate, request.startPeriod, request.endDate, request.endPeriod)}</p>
                        <p><strong>{formatDuration(calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod))}</strong></p>
                      </div>
                    </div>

                    <div className="employee-contact">
                      <p><i className="fas fa-user-tie"></i> <strong>Cấp trên:</strong> {renderSupervisors(request.supervisors)}</p>
                      <p><i className="fas fa-comment"></i> <strong>Lý do:</strong> {request.reason}</p>
                      {request.comments && (
                        <p><i className="fas fa-sticky-note"></i> <strong>Nhận xét:</strong> {request.comments}</p>
                      )}
                    </div>

                    <div className="employee-work-info">
                      <p>
                        <i className="fas fa-info-circle"></i>
                        <strong>Trạng thái:</strong> {getStatusBadge(request.status)}
                      </p>
                      <p>
                        <i className="fas fa-paperclip"></i>
                        <strong>File đính kèm:</strong> {getMyRequestAttachmentBadge(request)}
                      </p>
                    </div>

                    <div className="employee-actions">
                      {request.status === 'pending' && request.requests && request.requests.length > 0 && (
                        <>
                          <button 
                            className="btn-view"
                            onClick={() => {
                              const enhancedRequest = {
                                ...request.requests[0],
                                employee: { _id: user._id, fullName: user.fullName },
                                employeeId: user._id,
                                createdBy: user._id
                              };
                              handleShowEditModal(enhancedRequest);
                            }}
                            title="Chỉnh sửa đơn"
                          >
                            <i className="fas fa-edit"></i> Chỉnh sửa
                          </button>
                          <button 
                            className="btn-delete"
                            onClick={() => {
                              const enhancedRequest = {
                                ...request.requests[0],
                                employee: { _id: user._id, fullName: user.fullName },
                                employeeId: user._id,
                                createdBy: user._id
                              };
                              handleShowDeleteModal(enhancedRequest);
                            }}
                            title="Xóa đơn"
                            style={{ background: '#dc3545', color: 'white' }}
                          >
                            <i className="fas fa-trash-alt"></i> Xóa
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                ))
              )}
              
              {/* Loading more indicator */}
              {isMyRequestsLoading && (
                <div className="loading-more">
                  <div className="spinner"></div>
                  <span>Đang tải thêm...</span>
                </div>
              )}
              
              {/* End of results */}
              {displayedMyRequests.length > 0 && displayedMyRequests.length >= groupedRequests.length && (
                <div className="end-of-results">
                  <p>Đã hiển thị tất cả {groupedRequests.length} đơn xin nghỉ phép</p>
                </div>
              )}
            </div>
          </div>
        </Tab>

        <Tab eventKey="approval-requests" title="Đơn cần duyệt">
          <div className="employee-simple-container">
            <div className="employee-header">
              <h4 style={{ margin: '0', color: '#333', display: 'flex', alignItems: 'center' }}>
                <i className="fas fa-user-check" style={{ marginRight: '8px' }}></i>
                Đơn cần duyệt
              </h4>
              <span className="employee-count">
                {leaveRequests.asSupervisor.length} đơn cần duyệt
              </span>
            </div>
            
            {/* Danh sách đơn cần duyệt với infinite scroll */}
            <div className="employee-list-simple" id="approvalRequestsContainer">
              {displayedApprovalRequests.length === 0 ? (
                <div className="no-employees">
                  <div className="no-employees-icon">
                    <i className="fas fa-calendar-check"></i>
                  </div>
                  <p>Không có đơn xin nghỉ phép nào cần duyệt</p>
                </div>
              ) : (
                displayedApprovalRequests.map((request, index) => (
                  <div key={request._id} className="employee-card-simple">
                    <div className="employee-info-main">
                      <div className="employee-avatar">
                        {(request.employee?.fullName || request.employeeName || 'N/A').charAt(0).toUpperCase()}
                      </div>
                      <div className="employee-details">
                        <h4>{request.employee?.fullName || request.employeeName || 'Chưa xác định'}</h4>
                        <p><i className="fas fa-calendar"></i> {formatDate(request.createdAt)}</p>
                        <p><i className="fas fa-clock"></i> {formatLeaveTime(request.startDate, request.startPeriod, request.endDate, request.endPeriod)}</p>
                        <p><strong>{formatDuration(calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod))}</strong></p>
                      </div>
                    </div>

                    <div className="employee-contact">
                      <p><i className="fas fa-comment"></i> <strong>Lý do:</strong> {request.reason}</p>
                    </div>

                    <div className="employee-work-info">
                      <p>
                        <i className="fas fa-info-circle"></i>
                        <strong>Trạng thái:</strong> {getStatusBadge(request.status)}
                      </p>
                      <p>
                        <i className="fas fa-paperclip"></i>
                        <strong>File đính kèm:</strong> {getAttachmentBadge(request)}
                      </p>
                    </div>

                    <div className="employee-actions">
                      {request.status === 'pending' && (
                        <button 
                          className="btn-view"
                          onClick={() => handleShowApprovalModal(request)}
                          style={{ background: '#28a745', color: 'white' }}
                        >
                          <i className="fas fa-check-circle"></i> Duyệt/Từ chối
                        </button>
                      )}
                    </div>
                  </div>
                ))
              )}
              
              {/* Loading more indicator */}
              {isApprovalRequestsLoading && (
                <div className="loading-more">
                  <div className="spinner"></div>
                  <span>Đang tải thêm...</span>
                </div>
              )}
              
              {/* End of results */}
              {displayedApprovalRequests.length > 0 && displayedApprovalRequests.length >= leaveRequests.asSupervisor.length && (
                <div className="end-of-results">
                  <p>Đã hiển thị tất cả {leaveRequests.asSupervisor.length} đơn cần duyệt</p>
                </div>
              )}
            </div>
          </div>
        </Tab>
        
        {/* Tab hiển thị cho SUPER_ADMIN và LEVEL_II_MANAGER */}
        {user && (user.role === 'SUPER_ADMIN' || user.role === 'LEVEL_II_MANAGER') && (
          <Tab eventKey="all-requests" title="Tất cả đơn nghỉ phép">
            <div className="employee-table-container">
              {/* Leave Request Statistics Summary */}
              <div className="leave-statistics-banner" style={{
                background: 'linear-gradient(135deg, #3498db, #2980b9)',
                color: 'white',
                padding: '20px',
                borderRadius: '10px',
                marginBottom: '20px',
                boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
              }}>
                <h3 style={{ margin: '0 0 15px 0', display: 'flex', alignItems: 'center' }}>
                  <i className="fas fa-chart-bar" style={{ marginRight: '10px' }}></i>
                  Tóm tắt đơn nghỉ phép năm {new Date().getFullYear()}
                </h3>

                {(() => {
                  const stats = calculateLeaveStatistics();
                  const totalEmployees = stats.length;
                  const totalRequests = stats.reduce((sum, emp) => sum + emp.totalRequests, 0);
                  const totalDays = stats.reduce((sum, emp) => sum + emp.totalDays, 0);
                  const employeesAtLimit = stats.filter(emp => checkAnnualLimit(emp)).length;
                  const employeesAtMonthlyLimit = stats.filter(emp => checkMonthlyLimit(emp)).length;

                  return (
                    <div className="row">
                      <div className="col-md-3">
                        <div className="stat-card" style={{ background: 'rgba(255,255,255,0.2)', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
                          <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalEmployees}</div>
                          <div style={{ fontSize: '14px', opacity: '0.9' }}>Nhân viên có đơn</div>
                        </div>
                      </div>
                      <div className="col-md-3">
                        <div className="stat-card" style={{ background: 'rgba(255,255,255,0.2)', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
                          <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalRequests}</div>
                          <div style={{ fontSize: '14px', opacity: '0.9' }}>Tổng số đơn</div>
                        </div>
                      </div>
                      <div className="col-md-3">
                        <div className="stat-card" style={{ background: 'rgba(255,255,255,0.2)', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
                          <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalDays}</div>
                          <div style={{ fontSize: '14px', opacity: '0.9' }}>Tổng ngày nghỉ</div>
                        </div>
                      </div>
                      <div className="col-md-3">
                        <div className="stat-card" style={{ background: 'rgba(255,255,255,0.2)', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
                          <div style={{ fontSize: '24px', fontWeight: 'bold', color: employeesAtLimit > 0 ? '#ffeb3b' : '#fff' }}>
                            {employeesAtLimit}
                          </div>
                          <div style={{ fontSize: '14px', opacity: '0.9' }}>Đạt giới hạn năm</div>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </div>

              {/* Employee Statistics Detail Table */}
              {(() => {
                const stats = calculateLeaveStatistics();
                return stats.length > 0 && (
                  <div className="employee-stats-section" style={{ marginBottom: '20px' }}>
                    <h4 style={{ marginBottom: '15px', color: '#333' }}>
                      <i className="fas fa-users" style={{ marginRight: '8px' }}></i>
                      Chi tiết theo nhân viên
                    </h4>
                    <div className="table-responsive">
                      <table className="table table-striped table-hover">
                        <thead className="table-dark">
                          <tr>
                            <th>Nhân viên</th>
                            <th>Số đơn năm</th>
                            <th>Tổng ngày nghỉ</th>
                            <th>Đã duyệt</th>
                            <th>Chờ duyệt</th>
                            <th>Từ chối</th>
                            <th>Trạng thái</th>
                          </tr>
                        </thead>
                        <tbody>
                          {stats.map((empStat, index) => (
                            <tr key={index}>
                              <td>
                                <strong>{empStat.employeeName}</strong>
                              </td>
                              <td>
                                <span className={`badge ${checkAnnualLimit(empStat) ? 'bg-danger' : empStat.totalRequests >= 8 ? 'bg-warning' : 'bg-success'}`}>
                                  {empStat.totalRequests}/12
                                </span>
                              </td>
                              <td>
                                <strong>{empStat.totalDays}</strong> ngày
                              </td>
                              <td>
                                <span className="badge bg-success">{empStat.approvedRequests}</span>
                              </td>
                              <td>
                                <span className="badge bg-warning">{empStat.pendingRequests}</span>
                              </td>
                              <td>
                                <span className="badge bg-danger">{empStat.rejectedRequests}</span>
                              </td>
                              <td>
                                {checkAnnualLimit(empStat) && (
                                  <span className="badge bg-danger me-1">
                                    <i className="fas fa-exclamation-triangle"></i> Đạt giới hạn năm
                                  </span>
                                )}
                                {checkMonthlyLimit(empStat) && (
                                  <span className="badge bg-warning">
                                    <i className="fas fa-calendar-times"></i> Đạt giới hạn tháng
                                  </span>
                                )}
                                {!checkAnnualLimit(empStat) && !checkMonthlyLimit(empStat) && (
                                  <span className="badge bg-success">
                                    <i className="fas fa-check"></i> Bình thường
                                  </span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                );
              })()}

              {/* Debug info */}
              {leaveRequests.allRequests.length === 0 && (
                <div className="alert alert-warning">
                  <strong>Thông báo:</strong> Không tìm thấy đơn nghỉ phép nào. Vui lòng kiểm tra quyền truy cập của bạn.
                </div>
              )}
              
              <div className="table-filters">
                <h3>Lọc dữ liệu</h3>
                <div className="filter-row">
                  <div className="filter-group">
                    <label>Nhân viên:</label>
                    <input 
                      type="text" 
                      name="employeeName" 
                      value={tableFilters.employeeName || ''} 
                      onChange={handleTableFilterChange}
                      placeholder="Nhập tên nhân viên..."
                    />
                  </div>
                  <div className="filter-group">
                    <label>Quản lý:</label>
                    <input 
                      type="text" 
                      name="supervisorName" 
                      value={tableFilters.supervisorName || ''} 
                      onChange={handleTableFilterChange}
                      placeholder="Nhập tên quản lý..."
                    />
                  </div>
                  <div className="filter-group">
                    <label>Trạng thái:</label>
                    <select name="status" value={tableFilters.status} onChange={handleTableFilterChange}>
                      <option value="">Tất cả trạng thái</option>
                      <option value="pending">Đang chờ duyệt</option>
                      <option value="approved">Đã duyệt</option>
                      <option value="rejected">Đã từ chối</option>
                    </select>
                  </div>
                </div>
                <div className="filter-actions">
                  <button 
                    className="clear-filters-btn"
                    onClick={resetTableFilters}
                  >
                    Xóa bộ lọc
                  </button>
                </div>
              </div>

              <div className="table-summary">
                <p>Tổng số đơn: <strong>{filterTableRecords(tableFilters).length}</strong> / {leaveRequests.allRequests.length}</p>
                
                {tableFilters.status && (
                  <p>Trạng thái <strong>{getStatusBadge(tableFilters.status).props.children}</strong>: {filterTableRecords(tableFilters).length} đơn</p>
                )}
              </div>

              {/* Danh sách tất cả đơn nghỉ phép với infinite scroll */}
              <div className="employee-list-simple" id="allRequestsContainer">
                {displayedAllRequests.length === 0 ? (
                  <div className="no-employees">
                    <div className="no-employees-icon">
                      <i className="fas fa-calendar-times"></i>
                    </div>
                    <p>Không tìm thấy đơn nghỉ phép nào phù hợp với bộ lọc</p>
                  </div>
                ) : (
                  displayedAllRequests.map((request, index) => (
                    <div key={request._id} className="employee-card-simple">
                      <div className="employee-info-main">
                        <div className="employee-avatar">
                          {(request.employee?.fullName || 'N/A').charAt(0).toUpperCase()}
                        </div>
                        <div className="employee-details">
                          <h4>{request.employee?.fullName || 'Chưa xác định'}</h4>
                          <p><i className="fas fa-calendar"></i> {formatDate(request.createdAt)}</p>
                          <p><i className="fas fa-clock"></i> {formatLeaveTime(request.startDate, request.startPeriod, request.endDate, request.endPeriod)}</p>
                          <p><strong>{formatDuration(calculateLeaveDuration(request.startDate, request.startPeriod, request.endDate, request.endPeriod))}</strong></p>
                        </div>
                      </div>

                      <div className="employee-contact">
                        <p><i className="fas fa-user-tie"></i> <strong>Quản lý:</strong> {request.supervisor?.fullName || 'Chưa xác định'}</p>
                        <p><i className="fas fa-comment"></i> <strong>Lý do:</strong> {request.reason}</p>
                        {request.comments && (
                          <p><i className="fas fa-sticky-note"></i> <strong>Nhận xét:</strong> {request.comments}</p>
                        )}
                      </div>

                      <div className="employee-work-info">
                        <p>
                          <i className="fas fa-info-circle"></i>
                          <strong>Trạng thái:</strong> {getStatusBadge(request.status)}
                        </p>
                        <p>
                          <i className="fas fa-paperclip"></i>
                          <strong>File đính kèm:</strong> {getAttachmentBadge(request)}
                        </p>
                      </div>

                      <div className="employee-actions">
                        {/* SUPER_ADMIN có thể chỉnh sửa/xóa tất cả đơn */}
                        {/* Người nộp đơn chỉ có thể chỉnh sửa/xóa đơn của họ khi đơn còn ở trạng thái pending */}
                        {/* LEVEL_II_MANAGER chỉ có thể xem, không thể chỉnh sửa/xóa */}
                        {(user.role === 'SUPER_ADMIN' || 
                          (request.employee?._id === user._id && request.status === 'pending')) && 
                          user.role !== 'LEVEL_II_MANAGER' && (
                          <>
                            <button 
                              className="btn-view"
                              onClick={() => handleShowEditModal(request)}
                              title="Chỉnh sửa đơn"
                            >
                              <i className="fas fa-edit"></i> Chỉnh sửa
                            </button>
                            <button 
                              className="btn-delete"
                              onClick={() => handleShowDeleteModal(request)}
                              title="Xóa đơn"
                              style={{ background: '#dc3545', color: 'white' }}
                            >
                              <i className="fas fa-trash-alt"></i> Xóa
                            </button>
                          </>
                        )}
                        {/* Hiển thị thông báo "Chỉ xem" cho LEVEL_II_MANAGER */}
                        {user.role === 'LEVEL_II_MANAGER' && (
                          <span className="text-muted fst-italic" style={{ padding: '6px 12px', background: '#f8f9fa', borderRadius: '4px' }}>
                            <i className="fas fa-eye"></i> Chỉ xem
                          </span>
                        )}
                      </div>
                    </div>
                  ))
                )}
                
                {/* Loading more indicator */}
                {isAllRequestsLoading && (
                  <div className="loading-more">
                    <div className="spinner"></div>
                    <span>Đang tải thêm...</span>
                  </div>
                )}
                
                {/* End of results */}
                {displayedAllRequests.length > 0 && displayedAllRequests.length >= filterTableRecords(tableFilters).length && (
                  <div className="end-of-results">
                    <p>Đã hiển thị tất cả {filterTableRecords(tableFilters).length} đơn nghỉ phép</p>
                  </div>
                )}
              </div>
            </div>
          </Tab>
        )}
      </Tabs>

      {/* Thông báo thành công */}
      {showSuccessAlert && (
        <div className="success-alert">
          <div className="success-alert-content">
            <i className="fas fa-check-circle"></i>
            <span>{successMessage}</span>
          </div>
        </div>
      )}

      {/* Modal duyệt đơn */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Duyệt đơn xin nghỉ phép</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedRequest && (
            <>
              <p>
                <strong>Nhân viên:</strong> {selectedRequest.employee?.fullName || 'Không xác định'}
              </p>
              <p>
                <strong>Thời gian nghỉ:</strong> {formatLeaveTime(selectedRequest.startDate, selectedRequest.startPeriod, selectedRequest.endDate, selectedRequest.endPeriod)}
              </p>
              <p>
                <strong>Tổng ngày nghỉ:</strong> {formatDuration(calculateLeaveDuration(selectedRequest.startDate, selectedRequest.startPeriod, selectedRequest.endDate, selectedRequest.endPeriod))}
              </p>
              <p>
                <strong>Lý do:</strong> {selectedRequest.reason}
              </p>
              
              {/* Hiển thị file đính kèm */}
              <div className="mt-4 mb-4">
                <h5>File đính kèm</h5>
                {loadingAttachments ? (
                  <div className="text-center">
                    <Spinner animation="border" size="sm" /> Đang tải...
                  </div>
                ) : attachmentError ? (
                  <Alert variant="danger">{attachmentError}</Alert>
                ) : attachments.length === 0 ? (
                  <p className="text-muted">Không có file đính kèm</p>
                ) : (
                  <ListGroup>
                    {attachments.map(file => (
                      <ListGroup.Item key={file._id} className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                          <span className="me-2">{getFileIcon(file.fileType)}</span>
                          <span>{file.originalName}</span>
                          <span className="text-muted ms-2">({(file.fileSize / 1024 / 1024).toFixed(2)} MB)</span>
                        </div>
                        <div>
                          <Button 
                            variant="outline-primary" 
                            size="sm"
                            className="me-2"
                            onClick={() => handleDownloadFile(file._id)}
                          >
                            <FaDownload />
                          </Button>
                          {(user.role === 'SUPER_ADMIN' || user._id === file.uploadedBy) && (
                            <Button 
                              variant="outline-danger" 
                              size="sm"
                              onClick={() => handleDeleteFile(file._id)}
                            >
                              <FaTrashAlt />
                            </Button>
                          )}
                        </div>
                      </ListGroup.Item>
                    ))}
                  </ListGroup>
                )}
              </div>

              <Form onSubmit={handleSubmitApproval}>
                <Form.Group className="mb-3">
                  <Form.Label>Quyết định</Form.Label>
                  <Form.Select
                    name="status"
                    value={approvalData.status}
                    onChange={handleApprovalChange}
                  >
                    <option value="approved">Duyệt đơn</option>
                    <option value="rejected">Từ chối đơn</option>
                  </Form.Select>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Nhận xét (không bắt buộc)</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="comments"
                    value={approvalData.comments}
                    onChange={handleApprovalChange}
                    placeholder="Nhập nhận xét của bạn"
                  />
                </Form.Group>

                <div className="d-flex justify-content-end">
                  <Button variant="secondary" className="me-2" onClick={handleCloseModal}>
                    Hủy
                  </Button>
                  <Button variant="primary" type="submit">
                    Xác nhận
                  </Button>
                </div>
              </Form>
            </>
          )}
        </Modal.Body>
      </Modal>

      {/* Modal chỉnh sửa đơn */}
      <Modal show={showEditModal} onHide={handleCloseEditModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Chỉnh sửa đơn xin nghỉ phép</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedRequest && (
            <Form onSubmit={handleSubmitEdit}>
              <Form.Group className="mb-3">
                <Form.Label>Nhân viên</Form.Label>
                <Form.Control
                  type="text"
                  value={selectedRequest.employee?.fullName || 'Không xác định'}
                  disabled
                />
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>Cấp trên</Form.Label>
                <Form.Control
                  type="text"
                  value={selectedRequest.supervisor?.fullName || 'Không xác định'}
                  disabled
                />
              </Form.Group>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Ngày bắt đầu</Form.Label>
                    <Form.Control
                      type="date"
                      name="startDate"
                      value={editData.startDate}
                      onChange={handleEditChange}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Thời gian bắt đầu</Form.Label>
                    <Form.Select 
                      name="startPeriod"
                      value={editData.startPeriod || 'full_day'}
                      onChange={(e) => {
                        const newStartPeriod = e.target.value;
                        
                        // Cập nhật thời gian bắt đầu
                        handleEditChange({
                          target: { name: 'startPeriod', value: newStartPeriod }
                        });
                        
                        // Kiểm tra nếu ngày bắt đầu và kết thúc là cùng một ngày
                        const isSameDay = new Date(editData.startDate).setHours(0,0,0,0) === 
                                         new Date(editData.endDate).setHours(0,0,0,0);
                        
                        // Nếu cùng một ngày, áp dụng các quy tắc thời gian kết thúc
                        if (isSameDay) {
                          if (newStartPeriod === 'full_day') {
                            // Nếu chọn cả ngày, tự động đặt thời gian kết thúc cũng là cả ngày
                            handleEditChange({
                              target: { name: 'endPeriod', value: 'full_day' }
                            });
                          } else if (newStartPeriod === 'afternoon') {
                            // Nếu chọn buổi chiều, thời gian kết thúc chỉ có thể là buổi chiều
                            handleEditChange({
                              target: { name: 'endPeriod', value: 'afternoon' }
                            });
                          } else if (newStartPeriod === 'morning' && editData.endPeriod === 'full_day') {
                            // Nếu chọn buổi sáng và thời gian kết thúc đang là cả ngày, đổi thành buổi chiều
                            handleEditChange({
                              target: { name: 'endPeriod', value: 'afternoon' }
                            });
                          }
                        }
                      }}
                    >
                      <option value="full_day">Cả ngày</option>
                      <option value="morning">Buổi sáng</option>
                      <option value="afternoon">Buổi chiều</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Ngày kết thúc</Form.Label>
                    <Form.Control
                      type="date"
                      name="endDate"
                      value={editData.endDate}
                      onChange={handleEditChange}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Thời gian kết thúc</Form.Label>
                    <Form.Select 
                      name="endPeriod"
                      value={editData.endPeriod || 'full_day'}
                      onChange={handleEditChange}
                      disabled={
                        // Chỉ disable khi cùng một ngày và thời gian bắt đầu là cả ngày hoặc buổi chiều
                        new Date(editData.startDate).setHours(0,0,0,0) === new Date(editData.endDate).setHours(0,0,0,0) && 
                        (editData.startPeriod === 'full_day' || editData.startPeriod === 'afternoon')
                      }
                    >
                      {/* Nếu ngày bắt đầu và kết thúc khác nhau, hiển thị tất cả các tùy chọn */}
                      {new Date(editData.startDate).setHours(0,0,0,0) !== new Date(editData.endDate).setHours(0,0,0,0) ? (
                        <>
                          <option value="full_day">Cả ngày</option>
                          <option value="morning">Buổi sáng</option>
                          <option value="afternoon">Buổi chiều</option>
                        </>
                      ) : (
                        /* Nếu cùng một ngày, hiển thị tùy chọn dựa trên thời gian bắt đầu */
                        <>
                          {editData.startPeriod === 'full_day' && (
                            <option value="full_day">Cả ngày</option>
                          )}
                          
                          {editData.startPeriod === 'morning' && (
                            <>
                              <option value="morning">Buổi sáng</option>
                              <option value="afternoon">Buổi chiều</option>
                            </>
                          )}
                          
                          {editData.startPeriod === 'afternoon' && (
                            <option value="afternoon">Buổi chiều</option>
                          )}
                        </>
                      )}
                    </Form.Select>
                    <Form.Text className="text-muted">
                      {new Date(editData.startDate).setHours(0,0,0,0) === new Date(editData.endDate).setHours(0,0,0,0) ? (
                        // Nếu cùng một ngày
                        editData.startPeriod === 'full_day' 
                          ? 'Khi chọn thời gian bắt đầu là cả ngày, thời gian kết thúc cũng sẽ là cả ngày'
                          : editData.startPeriod === 'afternoon'
                            ? 'Khi chọn thời gian bắt đầu là buổi chiều, thời gian kết thúc cũng sẽ là buổi chiều'
                            : 'Sáng: 8:00 - 12:00, Chiều: 13:30 - 17:30'
                      ) : (
                        // Nếu khác ngày
                        'Sáng: 8:00 - 12:00, Chiều: 13:30 - 17:30, Cả ngày: 8:00 - 17:30'
                      )}
                    </Form.Text>
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label>Lý do</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="reason"
                  value={editData.reason}
                  onChange={handleEditChange}
                  placeholder="Nhập lý do nghỉ phép"
                  required
                />
              </Form.Group>
              
              {/* Hiển thị file đính kèm */}
              <div className="mt-4 mb-4">
                <h5>File đính kèm</h5>
                {loadingAttachments ? (
                  <div className="text-center">
                    <Spinner animation="border" size="sm" /> Đang tải...
                  </div>
                ) : attachmentError ? (
                  <Alert variant="danger">{attachmentError}</Alert>
                ) : (
                  <>
                    {/* Danh sách file hiện có */}
                    {attachments.length === 0 ? (
                      <p className="text-muted">Không có file đính kèm</p>
                    ) : (
                      <ListGroup className="mb-3">
                        {attachments.map(file => (
                          <ListGroup.Item key={file._id} className="d-flex justify-content-between align-items-center">
                            <div className="d-flex align-items-center">
                              <span className="me-2">{getFileIcon(file.fileType)}</span>
                              <span>{file.originalName}</span>
                              <span className="text-muted ms-2">({(file.fileSize / 1024 / 1024).toFixed(2)} MB)</span>
                            </div>
                            <div>
                              <Button 
                                variant="outline-primary" 
                                size="sm"
                                className="me-2"
                                onClick={() => handleDownloadFile(file._id)}
                              >
                                <FaDownload />
                              </Button>
                              {(user.role === 'SUPER_ADMIN' || user._id === file.uploadedBy) && (
                                <Button 
                                  variant="outline-danger" 
                                  size="sm"
                                  onClick={() => handleDeleteFile(file._id)}
                                >
                                  <FaTrashAlt />
                                </Button>
                              )}
                            </div>
                          </ListGroup.Item>
                        ))}
                      </ListGroup>
                    )}
                    
                    {/* Form upload file mới */}
                    <div className="mt-3">
                      <div className="d-flex align-items-center mb-2">
                        <Button 
                          variant="outline-primary" 
                          onClick={() => fileInputRef.current.click()}
                          className="d-flex align-items-center"
                          disabled={uploadingFiles}
                        >
                          <FaFileUpload className="me-2" /> Chọn file
                        </Button>
                        <Form.Text className="text-muted ms-3">
                          Hỗ trợ PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF, TXT (tối đa 5 file, mỗi file không quá 10MB)
                        </Form.Text>
                        <input
                          type="file"
                          ref={fileInputRef}
                          onChange={handleFileChange}
                          style={{ display: 'none' }}
                          multiple
                        />
                      </div>
                      
                      {uploadError && (
                        <Alert variant="danger" className="mt-2 mb-2">
                          {uploadError}
                        </Alert>
                      )}
                      
                      {selectedFiles.length > 0 && (
                        <>
                          <ListGroup className="mt-2">
                            {selectedFiles.map((file, index) => (
                              <ListGroup.Item key={index} className="d-flex justify-content-between align-items-center">
                                <div className="d-flex align-items-center">
                                  <span className="me-2">{getFileIcon(file.type)}</span>
                                  <span>{file.name}</span>
                                  <span className="text-muted ms-2">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                                </div>
                                <Button 
                                  variant="outline-danger" 
                                  size="sm"
                                  onClick={() => handleRemoveFile(index)}
                                  disabled={uploadingFiles}
                                >
                                  <FaTrashAlt />
                                </Button>
                              </ListGroup.Item>
                            ))}
                          </ListGroup>
                          
                          <div className="d-flex justify-content-end mt-2">
                            <Button 
                              variant="success" 
                              size="sm"
                              onClick={() => uploadFiles(selectedRequest._id)}
                              disabled={uploadingFiles}
                            >
                              {uploadingFiles ? (
                                <>
                                  <Spinner animation="border" size="sm" className="me-2" />
                                  Đang tải lên...
                                </>
                              ) : (
                                'Tải lên'
                              )}
                            </Button>
                          </div>
                        </>
                      )}
                    </div>
                  </>
                )}
              </div>

              {/* Chỉ hiển thị trường trạng thái cho admin */}
              {user && user.role === 'SUPER_ADMIN' ? (
                <>
                  <Form.Group className="mb-3">
                    <Form.Label>Trạng thái</Form.Label>
                    <Form.Select
                      name="status"
                      value={editData.status}
                      onChange={handleEditChange}
                    >
                      <option value="pending">Chờ duyệt</option>
                      <option value="approved">Đã duyệt</option>
                      <option value="rejected">Đã từ chối</option>
                      <option value="cancelled">Đã hủy</option>
                    </Form.Select>
                  </Form.Group>
                  
                  {/* Phần thiết lập quyền xem đơn - chỉ dành cho SUPER_ADMIN */}
                  <hr />
                  <h5>Thiết lập quyền xem đơn</h5>
                  
                  <Form.Group className="mb-3">
                    <Form.Label>Chế độ hiển thị</Form.Label>
                    <Form.Select
                      name="visibility"
                      value={editData.visibility || 'all'}
                      onChange={handleEditChange}
                    >
                      <option value="all">Tất cả (mặc định)</option>
                      <option value="restricted">Hạn chế</option>
                    </Form.Select>
                    <Form.Text className="text-muted">
                      Chế độ "Tất cả" cho phép mọi người có quyền xem đơn nghỉ phép. Chế độ "Hạn chế" chỉ cho phép những người được chỉ định xem đơn.
                    </Form.Text>
                  </Form.Group>
                  
                  {editData.visibility === 'restricted' && (
                    <Form.Group className="mb-3">
                      <Form.Label>Người dùng được phép xem</Form.Label>
                      <Form.Control
                        as="select"
                        multiple
                        value={editData.visibleTo || []}
                        onChange={(e) => {
                          const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
                          setEditData({
                            ...editData,
                            visibleTo: selectedOptions
                          });
                        }}
                        style={{ height: '150px' }}
                      >
                        {Array.isArray(allUsers) ? allUsers
                          .filter(user => ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'SUPER_ADMIN'].includes(user.role))
                          .map(user => (
                            <option key={user._id} value={user._id}>
                              {user.fullName || user.name || user.email}
                            </option>
                          )) : []}
                      </Form.Control>
                      <Form.Text className="text-muted">
                        Chỉ quản lý và admin được hiển thị trong danh sách này. Nhân viên thường không được thêm vào.
                        Giữ Ctrl (hoặc Cmd trên Mac) để chọn nhiều người dùng.
                      </Form.Text>
                    </Form.Group>
                  )}
                  
                  <Form.Group className="mb-3">
                    <Form.Label>Ẩn đơn khỏi người dùng</Form.Label>
                    <Form.Control
                      as="select"
                      multiple
                      value={editData.hiddenFrom || []}
                      onChange={(e) => {
                        const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
                        setEditData({
                          ...editData,
                          hiddenFrom: selectedOptions
                        });
                      }}
                      style={{ height: '150px' }}
                    >
                      {Array.isArray(allUsers) ? allUsers
                        .filter(user => ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'SUPER_ADMIN'].includes(user.role))
                        .map(user => (
                          <option key={user._id} value={user._id}>
                            {user.fullName || user.name || user.email}
                          </option>
                        )) : []}
                    </Form.Control>
                    <Form.Text className="text-muted">
                      Những quản lý/admin được chọn sẽ không thể xem đơn này, ngay cả khi họ có quyền quản lý đơn nghỉ phép.
                      Chỉ quản lý và admin được hiển thị trong danh sách này. Nhân viên thường không được thêm vào.
                      Giữ Ctrl (hoặc Cmd trên Mac) để chọn nhiều người dùng.
                    </Form.Text>
                  </Form.Group>
                </>
              ) : (
                <Form.Group className="mb-3">
                  <Form.Label>Trạng thái</Form.Label>
                  <Form.Control
                    type="text"
                    value={getStatusBadge(editData.status).props.children}
                    disabled
                  />
                  <Form.Text className="text-muted">
                    Chỉ quản trị viên mới có thể thay đổi trạng thái đơn
                  </Form.Text>
                </Form.Group>
              )}

              <div className="d-flex justify-content-end">
                <Button variant="secondary" className="me-2" onClick={handleCloseEditModal}>
                  Hủy
                </Button>
                <Button variant="primary" type="submit">
                  Lưu thay đổi
                </Button>
              </div>
            </Form>
          )}
        </Modal.Body>
      </Modal>

      {/* Modal xác nhận xóa */}
      <Modal show={showDeleteModal} onHide={handleCloseDeleteModal}>
        <Modal.Header closeButton>
          <Modal.Title>Xác nhận xóa</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {requestToDelete && (
            <>
              <p>Bạn có chắc chắn muốn xóa đơn xin nghỉ phép này?</p>
              <p>
                <strong>Nhân viên:</strong> {requestToDelete.employee?.fullName || 'Không xác định'}
              </p>
              <p>
                <strong>Thời gian nghỉ:</strong> {formatLeaveTime(requestToDelete.startDate, requestToDelete.startPeriod, requestToDelete.endDate, requestToDelete.endPeriod)}
              </p>
              <p>
                <strong>Tổng ngày nghỉ:</strong> {formatDuration(calculateLeaveDuration(requestToDelete.startDate, requestToDelete.startPeriod, requestToDelete.endDate, requestToDelete.endPeriod))}
              </p>
              <p>
                <strong>Lý do:</strong> {requestToDelete.reason}
              </p>
              <p>
                <strong>Trạng thái:</strong> {getStatusBadge(requestToDelete.status)}
              </p>
              <div className="alert alert-warning">
                <i className="fas fa-exclamation-triangle me-2"></i>
                Hành động này không thể hoàn tác. Đơn xin nghỉ phép sẽ bị xóa vĩnh viễn.
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseDeleteModal}>
            Hủy
          </Button>
          <Button variant="danger" onClick={handleDeleteRequest}>
            Xóa
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal xem file đính kèm */}
      <Modal show={showAttachmentModal} onHide={handleCloseAttachmentModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Xem file đính kèm</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedRequest && (
            <div>
              <div className="mb-4">
                <h5>Thông tin đơn nghỉ phép</h5>
                <Table bordered>
                  <tbody>
                    <tr>
                      <td width="30%"><strong>Thời gian nghỉ</strong></td>
                      <td>{formatLeaveTime(selectedRequest.startDate, selectedRequest.startPeriod, selectedRequest.endDate, selectedRequest.endPeriod)}</td>
                    </tr>
                    <tr>
                      <td><strong>Lý do</strong></td>
                      <td>{selectedRequest.reason}</td>
                    </tr>
                    <tr>
                      <td><strong>Trạng thái</strong></td>
                      <td>{getStatusBadge(selectedRequest.status)}</td>
                    </tr>
                  </tbody>
                </Table>
              </div>

              <div>
                <h5>File đính kèm</h5>
                {loadingAttachments ? (
                  <div className="text-center py-3">
                    <Spinner animation="border" variant="primary" />
                    <p className="mt-2">Đang tải danh sách file...</p>
                  </div>
                ) : attachmentError ? (
                  <Alert variant="danger">{attachmentError}</Alert>
                ) : attachments.length === 0 ? (
                  <Alert variant="info">Không có file đính kèm nào</Alert>
                ) : (
                  <ListGroup>
                    {attachments.map(file => (
                      <ListGroup.Item key={file._id} className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                          <span className="me-2">{getFileIcon(file.fileType)}</span>
                          <span>{file.originalName}</span>
                          <span className="text-muted ms-2">({(file.fileSize / 1024 / 1024).toFixed(2)} MB)</span>
                          <span className="text-muted ms-2">- Tải lên bởi {file.uploadedBy?.fullName || 'Không xác định'}</span>
                        </div>
                        <div>
                          <Button 
                            variant="outline-primary" 
                            size="sm"
                            className="me-2"
                            onClick={() => handleDownloadFile(file._id)}
                          >
                            <FaDownload />
                          </Button>
                          {(user.role === 'SUPER_ADMIN' || user._id === file.uploadedBy) && (
                            <Button 
                              variant="outline-danger" 
                              size="sm"
                              onClick={() => handleDeleteFile(file._id)}
                            >
                              <FaTrashAlt />
                            </Button>
                          )}
                        </div>
                      </ListGroup.Item>
                    ))}
                  </ListGroup>
                )}
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseAttachmentModal}>
            Đóng
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default LeaveRequests;

// Additional CSS styles for leave statistics - inject into document head
if (typeof document !== 'undefined') {
  const additionalStyles = `
    .leave-statistics-banner .stat-card {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .leave-statistics-banner .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }

    .employee-stats-section .table th {
      border-top: none;
      font-weight: 600;
      font-size: 14px;
      background-color: #343a40;
      color: #000;
    }

    .employee-stats-section .table td {
      vertical-align: middle;
      font-size: 13px;
      padding: 12px 8px;
    }

    .employee-stats-section .badge {
      font-size: 11px;
      padding: 4px 8px;
      margin-right: 4px;
    }

    .leave-statistics-banner h3 {
      text-shadow: 0 1px 3px rgba(0,0,0,0.3);
      font-weight: 600;
    }

    .leave-statistics-banner .stat-card div:first-child {
      font-family: 'Arial', sans-serif;
      font-weight: 700;
    }

    @media (max-width: 768px) {
      .leave-statistics-banner .row > div {
        margin-bottom: 15px;
      }

      .employee-stats-section {
        overflow-x: auto;
      }

      .leave-statistics-banner {
        padding: 15px;
      }

      .leave-statistics-banner h3 {
        font-size: 18px;
      }
    }
  `;

  // Check if styles already exist to avoid duplicates
  if (!document.querySelector('#leave-stats-styles')) {
    const styleSheet = document.createElement('style');
    styleSheet.id = 'leave-stats-styles';
    styleSheet.type = 'text/css';
    styleSheet.innerText = additionalStyles;
    document.head.appendChild(styleSheet);
  }
}