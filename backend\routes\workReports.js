const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const mongoose = require('mongoose');
const WorkReport = require('../models/WorkReport');
const AssignedTask = require('../models/AssignedTask');
const User = require('../models/User');
const UserProject = require('../models/UserProject');
const TaskComment = require('../models/TaskComment');
const ExcelJS = require('exceljs');
const path = require('path');
const nodemailer = require('nodemailer');
const { createNotification, createRoleNotification } = require('../utils/notificationUtils');
const Notification = require('../models/Notification');
const upload = require('../middleware/upload');
require('dotenv').config();

// <PERSON><PERSON>u h<PERSON>nh nodemailer
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// TEST ENDPOINT - G<PERSON>i thông báo test
router.post('/test-notification', auth, async (req, res) => {
  try {
    console.log('=== TEST NOTIFICATION ENDPOINT ===');
    console.log('User ID:', req.user.id);
    console.log('User info:', req.user);
    
    const testNotification = new Notification({
      user: req.user.id,
      message: `Test notification at ${new Date().toLocaleString()}`,
      type: 'SYSTEM',
      icon: 'fas fa-test-tube',
      link: '/work-reports',
      isRead: false
    });
    
    const savedNotification = await testNotification.save();
    console.log('Test notification saved:', savedNotification._id);
    
    // Gửi qua WebSocket
    if (global.wsService) {
      console.log('Sending test notification via wsService');
      global.wsService.sendNotification(req.user.id, savedNotification);
    } else {
      console.log('No wsService available');
    }
    
    if (global.broadcastToUser) {
      console.log('Sending test notification via broadcastToUser');
      global.broadcastToUser(req.user.id, {
        type: 'notification',
        message: savedNotification.message,
        data: savedNotification
      });
    } else {
      console.log('No broadcastToUser available');
    }
    
    res.json({ 
      success: true, 
      message: 'Test notification sent',
      notification: savedNotification 
    });
  } catch (error) {
    console.error('Error sending test notification:', error);
    res.status(500).json({ error: error.message });
  }
});

// New endpoint to receive a notification payload and process it for selected managers.
router.post('/notifications/role', auth, async (req, res) => {
  try {
    const { managerIds, message, type, icon, link } = req.body;
    
    if (!managerIds || !Array.isArray(managerIds) || managerIds.length === 0) {
      return res.status(400).json({ error: 'Danh sách manager IDs không hợp lệ' });
    }
    
    // Sử dụng hàm util thực sự để tạo thông báo cho từng manager
    for (const managerId of managerIds) {
      await createNotification(managerId, {
        message,
        type: type || 'REPORT',
        icon: icon || 'fas fa-file-alt',
        link: link || '/work-reports'
      });
    }
    
    console.log('Đã gửi thông báo push đến các quản lý:', managerIds);
    return res.json({ success: true, message: 'Thông báo đã được gửi đến các quản lý' });
  } catch (error) {
    console.error('Lỗi khi gửi thông báo:', error);
    return res.status(500).json({ error: 'Lỗi khi gửi thông báo' });
  }
});

// GET: Thống kê công việc cho quản lý
router.get('/stats/manager', auth, async (req, res) => {
  if (!['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'].includes(req.user.role)) {
    return res.status(403).json({ error: 'Bạn không có quyền truy cập chức năng này' });
  }
  try {
    const userId = req.query.userId || req.user.id;
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({ error: 'Mã người dùng không hợp lệ' });
    }
    let query = {};
    if (!['ADMIN', 'SUPER_ADMIN'].includes(req.user.role)) {
      query = { managers: new mongoose.Types.ObjectId(userId) };
    }
    const stats = await WorkReport.aggregate([
      { $match: query },
      { $unwind: '$tasks' },
      {
        $group: {
          _id: '$tasks.status',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          status: '$_id',
          count: 1,
          _id: 0
        }
      }
    ]);
    const result = [
      { status: 'ongoing', count: 0 },
      { status: 'completed', count: 0 }
    ];
    stats.forEach(stat => {
      const index = stat.status === 'ongoing' ? 0 : 1;
      result[index] = stat;
    });
    res.json(result);
  } catch (err) {
    console.error('Lỗi khi tải thống kê:', err.message, err.stack);
    res.status(500).json({ error: 'Lỗi server khi tải thống kê', details: err.message });
  }
});

// GET: Lấy tất cả người dùng có vai trò quản lý
router.get('/users/managers', auth, async (req, res) => {
  try {
    const searchTerm = req.query.search || '';
    
    // Chỉ lấy những người có vai trò quản lý
    const managerRoles = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER'];
    
    const query = {
      role: { $in: managerRoles }
    };
    
    // Nếu có từ khóa tìm kiếm, thêm điều kiện tìm kiếm theo tên
    if (searchTerm) {
      query.fullName = { $regex: new RegExp(searchTerm, 'i') };
    }
    
    // Chỉ lấy những người dùng có vai trò quản lý
    const managers = await User.find(query)
      .select('fullName role _id email')
      .sort({ fullName: 1 });
    
    // Trả về danh sách quản lý với isManager = true cho tất cả
    const managersWithFlag = managers.map(manager => ({
      _id: manager._id,
      fullName: manager.fullName,
      role: manager.role,
      email: manager.email,
      isManager: true
    }));
    
    res.json(managersWithFlag);
  } catch (err) {
    console.error('Lỗi khi tải danh sách quản lý:', err);
    res.status(500).json({ error: 'Lỗi server khi tải danh sách quản lý' });
  }
});

// GET: Lấy danh sách nhân viên (cho cấp trên chọn khi giao việc)
router.get('/users/employees', auth, async (req, res) => {
  try {
    const managerRoles = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'];
    if (!managerRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Bạn không có quyền truy cập danh sách nhân viên' });
    }

    const excludedRoles = ['ADMIN'];
    
      const employees = await User.find({
      role: { $nin: excludedRoles } 
    }).select('_id fullName role reportManager');
    
    res.json(employees);
  } catch (error) {
    res.status(500).json({ message: 'Lỗi khi lấy danh sách nhân viên', error: error.message });
  }
});

// GET: Lấy danh sách dự án của user hiện tại
router.get('/user-projects', auth, async (req, res) => {
  try {
    console.log('GET /user-projects - User ID:', req.user.id);
    const projects = await UserProject.find({ user: req.user.id })
      .sort({ lastUsed: -1, projectName: 1 })
      .select('projectName lastUsed');
    
    console.log('Found projects:', projects);
    const projectNames = projects.map(p => p.projectName);
    console.log('Returning project names:', projectNames);
    res.json(projectNames);
  } catch (error) {
    console.error('Error fetching user projects:', error);
    res.status(500).json({ error: 'Lỗi khi lấy danh sách dự án' });
  }
});

// POST: Thêm dự án mới cho user
router.post('/user-projects', auth, async (req, res) => {
  try {
    console.log('POST /user-projects - User ID:', req.user.id);
    console.log('Request body:', req.body);
    
    const { projectName } = req.body;
    
    if (!projectName || !projectName.trim()) {
      console.log('Invalid project name:', projectName);
      return res.status(400).json({ error: 'Tên dự án không được để trống' });
    }

    const trimmedName = projectName.trim();
    console.log('Trimmed project name:', trimmedName);
    
    // Kiểm tra xem dự án đã tồn tại chưa
    const existingProject = await UserProject.findOne({
      user: req.user.id,
      projectName: trimmedName
    });

    console.log('Existing project:', existingProject);

    if (existingProject) {
      // Cập nhật lastUsed nếu dự án đã tồn tại
      existingProject.lastUsed = new Date();
      await existingProject.save();
      console.log('Updated existing project lastUsed');
      return res.json({ message: 'Dự án đã tồn tại, cập nhật thời gian sử dụng' });
    }

    // Tạo dự án mới
    const newProject = new UserProject({
      user: req.user.id,
      projectName: trimmedName
    });

    console.log('Creating new project:', newProject);
    await newProject.save();
    console.log('Project saved successfully');
    res.status(201).json({ message: 'Thêm dự án thành công', projectName: trimmedName });
  } catch (error) {
    console.error('Error adding user project:', error);
    res.status(500).json({ error: 'Lỗi khi thêm dự án: ' + error.message });
  }
});

// DELETE: Xóa dự án của user
router.delete('/user-projects/:projectName', auth, async (req, res) => {
  try {
    const { projectName } = req.params;
    
    const result = await UserProject.deleteOne({
      user: req.user.id,
      projectName: decodeURIComponent(projectName)
    });

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: 'Không tìm thấy dự án để xóa' });
    }

    res.json({ message: 'Xóa dự án thành công' });
  } catch (error) {
    console.error('Error deleting user project:', error);
    res.status(500).json({ error: 'Lỗi khi xóa dự án' });
  }
});

// PUT: Cập nhật thời gian sử dụng dự án
router.put('/user-projects/:projectName/use', auth, async (req, res) => {
  try {
    const { projectName } = req.params;
    
    const project = await UserProject.findOne({
      user: req.user.id,
      projectName: decodeURIComponent(projectName)
    });

    if (project) {
      project.lastUsed = new Date();
      await project.save();
    }

    res.json({ message: 'Cập nhật thời gian sử dụng thành công' });
  } catch (error) {
    console.error('Error updating project usage:', error);
    res.status(500).json({ error: 'Lỗi khi cập nhật thời gian sử dụng' });
  }
});

// GET: Lấy danh sách người dùng để chọn collaborator (tất cả user có thể truy cập)
router.get('/users/collaborators', auth, async (req, res) => {
  try {
    const searchTerm = req.query.search || '';
    
    // Tạo query cơ bản - loại trừ chính user hiện tại
    const query = {
      _id: { $ne: req.user.id }
    };
    
    // Nếu có từ khóa tìm kiếm, thêm điều kiện tìm kiếm theo tên
    if (searchTerm) {
      query.fullName = { $regex: new RegExp(searchTerm, 'i') };
    }
    
    // Lấy tất cả user trừ chính user hiện tại (bao gồm tất cả các cấp)
    const users = await User.find(query)
      .select('_id fullName role email')
      .sort({ fullName: 1 });
    
    // Thêm thông tin về cấp bậc để hiển thị trong UI
    const usersWithRoleInfo = users.map(user => ({
      _id: user._id,
      fullName: user.fullName,
      role: user.role,
      email: user.email,
      roleDisplay: getRoleDisplayName(user.role)
    }));
    
    res.json(usersWithRoleInfo);
  } catch (error) {
    console.error('Error fetching collaborators:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách Cá nhân phối hợp', error: error.message });
  }
});

// Helper function để hiển thị tên role dễ hiểu
function getRoleDisplayName(role) {
  const roleNames = {
    'SUPER_ADMIN': 'Quản trị viên cấp cao',
    'ADMIN': 'Quản trị viên',
    'LEVEL_I_MANAGER': 'Quản lý cấp I',
    'LEVEL_II_MANAGER': 'Nhân sự',
    'REPORT_MANAGER': 'Quản lý báo cáo',
    'ACCOUNTANT': 'Kế toán',
    'EMPLOYEE': 'Nhân viên'
  };
  return roleNames[role] || role;
}

// POST: Tạo báo cáo công việc và gửi email & push thông báo đến các quản lý được chọn
router.post('/', auth, async (req, res) => {
  try {
    const { tasks, managers } = req.body;
    
    if (!Array.isArray(managers) || managers.length === 0) {
      return res.status(400).json({ error: 'Phải chọn ít nhất một quản lý' });
    }
    
    // Xác minh tất cả quản lý được chọn có phải là quản lý hợp lệ hay không
    const managerRoles = ['LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT', 'SUPER_ADMIN'];
    const selectedUsers = await User.find({ _id: { $in: managers } }).select('role fullName');
    
    // Kiểm tra từng người dùng được chọn có phải là quản lý không
    const nonManagerUsers = selectedUsers.filter(user => !managerRoles.includes(user.role));
    
    if (nonManagerUsers.length > 0) {
      return res.status(400).json({ 
        error: 'Một hoặc nhiều người dùng được chọn không phải là quản lý',
        nonManagers: nonManagerUsers.map(u => ({ _id: u._id, fullName: u.fullName }))
      });
    }
    
    const processedTasks = tasks.map(task => ({
      ...task,
      project: task.project || '',
      progress: task.progress || null,
      completionPercentage: task.completionPercentage || 0,
      endTime: task.endTime || null
    }));

    const report = new WorkReport({
      user: req.user.id,
      managers,
      tasks: processedTasks,
    });
    await report.save();

    // Lấy thông tin người nộp báo cáo
    const submitter = await User.findById(req.user.id).select('fullName email');

    // Gửi thông báo cho các collaborator được chọn
    const allCollaborators = new Set();
    tasks.forEach(task => {
      if (task.collaborator && Array.isArray(task.collaborator)) {
        task.collaborator.forEach(collab => {
          const collabId = typeof collab === 'object' ? collab._id || collab : collab;
          if (collabId && collabId.toString() !== req.user.id.toString()) {
            allCollaborators.add(collabId.toString());
          }
        });
      }
    });

    // Gửi thông báo cho từng collaborator
    for (const collaboratorId of allCollaborators) {
      try {
        await createNotification(collaboratorId, {
          message: `${submitter.fullName} đã chọn bạn làm cá nhân phối hợp trong báo cáo công việc`,
          type: 'COLLABORATION',
          icon: 'fas fa-users',
          link: `/work-reports?reportId=${report._id}`
        });
        console.log('Đã gửi thông báo collaborator đến:', collaboratorId);
      } catch (notifError) {
        console.error('Lỗi khi gửi thông báo collaborator:', notifError);
      }
    }

    // Tự động thêm các dự án mới vào danh sách dự án của user
    for (const task of tasks) {
      if (task.project && task.project.trim()) {
        try {
          const trimmedProject = task.project.trim();
          const existingProject = await UserProject.findOne({
            user: req.user.id,
            projectName: trimmedProject
          });

          if (existingProject) {
            // Cập nhật lastUsed nếu dự án đã tồn tại
            existingProject.lastUsed = new Date();
            await existingProject.save();
          } else {
            // Tạo dự án mới
            const newProject = new UserProject({
              user: req.user.id,
              projectName: trimmedProject
            });
            await newProject.save();
            console.log('Auto-added new project:', trimmedProject);
          }
        } catch (projectError) {
          console.error('Error auto-adding project:', projectError);
          // Không throw lỗi để không ảnh hưởng đến việc tạo báo cáo
        }
      }
    }

    // Lấy danh sách email của các quản lý được chọn
    const managerUsers = await User.find({ _id: { $in: managers } }).select('email fullName');
    const managerEmails = managerUsers.map((m) => m.email).filter(email => email && email.trim() !== '');

    // Chỉ gửi email nếu có email hợp lệ và cấu hình email đã được thiết lập
    if (managerEmails.length > 0 && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
      try {
        // Tạo danh sách nội dung công việc để thêm vào email
        const taskList = tasks.map((task, index) => `
          <li>
            <strong>Công việc ${index + 1}:</strong> ${task.content} <br>
            <strong>Dự án:</strong> ${task.project || 'Không xác định'} <br>
            <strong>Trạng thái:</strong> ${task.status === 'ongoing' ? 'Tiếp tục' : 'Hoàn thành'}
          </li>
        `).join('');

        // Gửi email thông báo
        const mailOptions = {
          from: process.env.EMAIL_USER,
          to: managerEmails,
          subject: `Thông báo: Báo cáo công việc mới từ ${submitter.fullName}`,
          html: `
            <p>Xin chào,</p>
            <p>Nhân viên <strong>${submitter.fullName}</strong> vừa nộp một báo cáo công việc mới.</p>
            <p><strong>Ngày nộp:</strong> ${formatDate(new Date())}</p>
            <p><strong>Số lượng nhiệm vụ:</strong> ${tasks.length}</p>
            <p><strong>Nội dung công việc:</strong></p>
            <ul>${taskList}</ul>
            <p>Vui lòng truy cập hệ thống để xem chi tiết: <a href="https://quanly.newatlantic.vn/work-reports">Link hệ thống</a></p>
            <p>Trân trọng,<br>Hệ thống quản lý báo cáo</p>
          `,
        };
        await transporter.sendMail(mailOptions);
        console.log('Email sent to:', managerEmails);
      } catch (emailError) {
        console.error('Lỗi khi gửi email thông báo:', emailError);
        // Không throw lỗi, chỉ ghi log để quá trình vẫn tiếp tục
      }
    } else {
      console.log('Không gửi email do không có địa chỉ email hợp lệ hoặc cấu hình email chưa được thiết lập');
    }

    // Gửi email thông báo cho collaborator
    if (allCollaborators.size > 0 && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
      try {
        const collaboratorUsers = await User.find({ 
          _id: { $in: Array.from(allCollaborators) } 
        }).select('email fullName');
        
        const collaboratorEmails = collaboratorUsers
          .map(u => u.email)
          .filter(email => email && email.trim() !== '');

        if (collaboratorEmails.length > 0) {
          const mailOptions = {
            from: process.env.EMAIL_USER,
            to: collaboratorEmails,
            subject: `Thông báo: Bạn được chọn làm cá nhân phối hợp trong báo cáo của ${submitter.fullName}`,
            html: `
              <p>Xin chào,</p>
              <p><strong>${submitter.fullName}</strong> đã chọn bạn làm cá nhân phối hợp trong báo cáo công việc.</p>
              <p>Vui lòng truy cập hệ thống để xem chi tiết và tham gia thảo luận: <a href="https://quanly.newatlantic.vn/work-reports?reportId=${report._id}">Xem báo cáo</a></p>
              <p>Trân trọng,<br>Hệ thống quản lý báo cáo</p>
            `,
          };
          await transporter.sendMail(mailOptions);
          console.log('Email thông báo collaborator đã được gửi đến:', collaboratorEmails);
        }
      } catch (emailError) {
        console.error('Lỗi khi gửi email thông báo collaborator:', emailError);
      }
    }

    try {
      // 1. Gửi thông báo tới các quản lý được chọn
      for (const managerId of managers) {
        // Sử dụng createNotification để tự động gửi cả WebSocket và Push
        await createNotification(managerId, {
          message: `${submitter.fullName} đã gửi báo cáo công việc`,
          type: 'REPORT',
          icon: 'fas fa-file-alt',
          link: `/work-reports`
        });

        // Sử dụng lại phương thức cũ nếu global.wsService không có sẵn
        if (!global.wsService && global.broadcastToUser) {
          global.broadcastToUser(managerId, {
            type: 'notification',
            message: `${submitter.fullName} đã gửi báo cáo công việc`,
            data: {
              type: 'REPORT',
              icon: 'fas fa-file-alt',
              link: `/work-reports`,
              reportId: report._id
            }
          });
        }

        // 1.1. Gửi thông báo bổ sung nếu có công việc hoàn thành
        const hasCompletedTasks = tasks.some(task => task.status === 'completed');
        if (hasCompletedTasks) {
          await createNotification(managerId, {
            message: `${submitter.fullName} đã hoàn thành một số công việc trong báo cáo`,
            type: 'REPORT',
            icon: 'fas fa-check-circle',
            link: `/work-reports`
          });

          // Sử dụng lại phương thức cũ nếu global.wsService không có sẵn
          if (!global.wsService && global.broadcastToUser) {
            global.broadcastToUser(managerId, {
              type: 'notification',
              message: `${submitter.fullName} đã hoàn thành một số công việc trong báo cáo`,
              data: {
                type: 'REPORT',
                icon: 'fas fa-check-circle',
                link: `/work-reports`,
                reportId: report._id
              }
            });
          }
        }
      }

      // 2. Gửi thông báo xác nhận đến người nộp báo cáo
      await createNotification(req.user.id, {
        message: `Báo cáo công việc của bạn đã được nộp thành công`,
        type: 'REPORT',
        icon: 'fas fa-check-circle',
        link: `/work-reports`
      });

      // Sử dụng lại phương thức cũ nếu global.wsService không có sẵn
      if (!global.wsService && global.broadcastToUser) {
        global.broadcastToUser(req.user.id, {
          type: 'notification',
          message: `Báo cáo công việc của bạn đã được nộp thành công`,
          data: {
            type: 'REPORT',
            icon: 'fas fa-check-circle',
            link: `/work-reports`,
            reportId: report._id
          }
        });
      }

      console.log('Notifications sent successfully through WebSocket');
    } catch (notifyError) {
      console.error('Lỗi khi gửi thông báo push:', notifyError);
    }

    res.status(201).json({ 
      message: 'Báo cáo công việc đã được gửi. Email và thông báo đã được gửi tới các quản lý', 
      report 
    });
  } catch (err) {
    console.error('Error creating report:', err);
    res.status(500).json({ error: err.message });
  }
});

// GET: Lấy báo cáo của nhân viên hiện tại
router.get('/my-reports', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 1000000;
    const skip = (page - 1) * limit;
    
    // Tìm báo cáo mà user là tác giả HOẶC là collaborator trong bất kỳ task nào
    const query = {
      $or: [
        { user: req.user.id }, // Báo cáo do user tạo
        { 'tasks.collaborator': req.user.id } // Báo cáo mà user là collaborator
      ]
    };
    
    const reports = await WorkReport.find(query)
      .populate('user', 'fullName company')
      .populate('managers', 'fullName role')
      .populate('comments.user', 'fullName')
      .populate({
        path: 'tasks.collaborator',
        select: 'fullName role',
        match: { _id: { $exists: true } } // Chỉ populate những collaborator là ObjectId
      })
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 }); // Sắp xếp theo ngày tạo mới nhất
    
    const total = await WorkReport.countDocuments(query);
    res.json({ reports, total });
  } catch (err) {
    console.error('Lỗi khi tải báo cáo của tôi:', err);
    res.status(500).json({ error: err.message });
  }
});

// GET: Lấy báo cáo theo quản lý (chỉ cấp trên)
router.get('/manager', auth, async (req, res) => {
  if (!['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'].includes(req.user.role)) {
    return res.status(403).json({ error: 'Bạn không có quyền truy cập chức năng này' });
  }
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 1000000;
    const skip = (page - 1) * limit;
    const userId = req.query.userId || req.user.id;
    let query = {};
    
    if (!['ADMIN', 'SUPER_ADMIN'].includes(req.user.role)) {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ error: 'Mã người dùng không hợp lệ' });
      }
      
      // Sửa lại điều kiện query để lọc chính xác
      query = { 
        managers: new mongoose.Types.ObjectId(userId),
        approvalStatus: { $ne: 'draft' } // Lấy tất cả ngoại trừ draft
      };
    } else {
      // Admin vẫn xem tất cả báo cáo trừ draft
      query = { approvalStatus: { $ne: 'draft' } };
    }
    
    console.log("Query /manager:", query); // Log để kiểm tra query
    
    const reports = await WorkReport.find(query)
      .populate('user', 'fullName company')
      .populate('managers', 'fullName role')
      .populate('comments.user', 'fullName')
      .populate({
        path: 'tasks.collaborator',
        select: 'fullName role',
        match: { _id: { $exists: true } }
      })
      .skip(skip)
      .limit(limit);
    
    const total = await WorkReport.countDocuments(query);
    res.json({ reports, total });
  } catch (err) {
    console.error('Lỗi khi tải báo cáo:', err);
    res.status(500).json({ error: err.message });
  }
});

// POST: Thêm bình luận vào báo cáo và gửi thông báo cho người liên quan
router.post('/:id/comments', auth, async (req, res) => {
  try {
    const { content } = req.body;
    const reportId = req.params.id;
    const userId = req.user.id;

    const report = await WorkReport.findById(reportId);
    if (!report) return res.status(404).json({ error: 'Báo cáo không tồn tại' });

    // Kiểm tra quyền: Quản lý được chỉ định hoặc người nộp báo cáo có quyền bình luận
    const isManager = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT', 'EMPLOYEE'].includes(req.user.role);
    const isAssignedManager = report.managers.some((m) => String(m) === String(userId));
    const isReportOwner = String(report.user) === String(userId);

    if (!(isManager && isAssignedManager) && !isReportOwner) {
      return res.status(403).json({ error: 'Bạn không có quyền bình luận báo cáo này' });
    }

    // Thêm bình luận
    report.comments.push({ user: userId, content });
    await report.save();

    // Lấy thông tin để gửi thông báo
    const submitter = await User.findById(report.user).select('fullName email');
    const commenter = await User.findById(userId).select('fullName');

    // Gửi thông báo push cho tất cả người liên quan
    try {
      console.log('=== COMMENT NOTIFICATION ===');
      console.log('Report ID:', reportId);
      console.log('Commenter:', commenter.fullName);
      console.log('Report owner:', submitter.fullName);
      
      // Danh sách người nhận thông báo (loại trừ người bình luận)
      const notificationRecipients = new Set();
      
      // Luôn thêm người nộp báo cáo vào danh sách nhận thông báo (trừ khi chính họ bình luận)
      if (String(report.user) !== String(userId)) {
        notificationRecipients.add(String(report.user));
      }
      
      // Thêm tất cả quản lý được chỉ định vào danh sách nhận thông báo (trừ khi chính họ bình luận)
      for (const managerId of report.managers) {
        if (String(managerId) !== String(userId)) {
          notificationRecipients.add(String(managerId));
        }
      }
      
      console.log('Recipients:', Array.from(notificationRecipients));

      // Gửi thông báo cho từng người nhận
      for (const recipientId of notificationRecipients) {
        const isRecipientReportOwner = String(recipientId) === String(report.user);
        const isReportOwner = String(report.user) === String(userId);
        const message = isRecipientReportOwner 
          ? `Quản lý ${commenter.fullName} đã bình luận về báo cáo công việc của bạn: "${content}"`
          : `${isReportOwner ? submitter.fullName : `Quản lý ${commenter.fullName}`} đã bình luận về báo cáo công việc: "${content}"`;

        console.log(`Creating notification for user ${recipientId}:`, message);

        try {
          const savedNotification = await createNotification(recipientId, {
            message: message,
            type: 'REPORT',
            icon: 'fas fa-comment',
            link: `/work-reports?reportId=${report._id}&action=viewComments`
          });

          console.log('✅ Notification saved:', savedNotification._id);

          if (global.wsService) {
            console.log('Sending via wsService to:', recipientId);
            global.wsService.sendNotification(recipientId, savedNotification);
          }

          if (!global.wsService && global.broadcastToUser) {
            console.log('Sending via broadcastToUser to:', recipientId);
            global.broadcastToUser(recipientId, {
              type: 'notification',
              message: message,
              data: {
                type: 'REPORT',
                icon: 'fas fa-comment',
                link: `/work-reports?reportId=${report._id}&action=viewComments`,
                reportId: report._id
              }
            });
          }
          
          if (!global.wsService && !global.broadcastToUser) {
            console.log('No WebSocket service available for real-time notifications');
          }
        } catch (notificationError) {
          console.error(`❌ Error creating notification for user ${recipientId}:`, notificationError);
        }
      }

      // Gửi email thông báo cho người nộp báo cáo nếu có email và không phải là người bình luận
      if (String(report.user) !== String(userId) && submitter.email && submitter.email.trim() !== '' && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
        try {
          const mailOptions = {
            from: process.env.EMAIL_USER,
            to: submitter.email,
            subject: `Thông báo: Báo cáo công việc của bạn có bình luận mới`,
            html: `
              <p>Xin chào ${submitter.fullName},</p>
              <p><strong>${isReportOwner ? 'Bạn' : `Quản lý ${commenter.fullName}`}</strong> đã bình luận về báo cáo công việc${isReportOwner ? '' : ' của bạn'}:</p>
              <p><strong>Bình luận:</strong> ${content}</p>
              <p>Vui lòng truy cập hệ thống để xem chi tiết: <a href="https://quanly.newatlantic.vn/work-reports">Link hệ thống</a></p>
              <p>Trân trọng,<br>Hệ thống quản lý báo cáo</p>
            `,
          };
          await transporter.sendMail(mailOptions);
          console.log('Email thông báo bình luận đã được gửi đến:', submitter.email);
        } catch (emailError) {
          console.error('Lỗi khi gửi email thông báo bình luận:', emailError);
          // Không throw lỗi, chỉ ghi log để quá trình vẫn tiếp tục
        }
      }

      console.log('=== COMMENT NOTIFICATION SUMMARY ===');
      console.log('Total recipients:', notificationRecipients.size);
      console.log('Recipients list:', Array.from(notificationRecipients));
      console.log('Notifications sent successfully');
    } catch (notifyError) {
      console.error('❌ Lỗi khi gửi thông báo push:', notifyError);
      console.error('Error stack:', notifyError.stack);
    }

    res.status(201).json({ message: 'Thêm bình luận thành công', report });
  } catch (err) {
    console.error('Error adding comment:', err);
    res.status(500).json({ error: err.message });
  }
});

// POST: Duyệt báo cáo
router.post('/:id/approve', auth, async (req, res) => {
  try {
    const reportId = req.params.id;

    const report = await WorkReport.findById(reportId);
    if (!report) {
      return res.status(404).json({ error: 'Báo cáo không tồn tại' });
    }

    const isManager = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'].includes(req.user.role);
    const isAssignedManager = report.managers.some((m) => String(m) === String(req.user.id));

    if (!isManager || !isAssignedManager) {
      return res.status(403).json({ error: 'Bạn không có quyền duyệt báo cáo này' });
    }

    // Cập nhật báo cáo
    report.approvalStatus = 'approved';
    report.approvedBy = req.user.id;
    await report.save();

    // Trả về phản hồi ngay lập tức
    res.json({ message: 'Báo cáo đã được duyệt', report });

    // Thực hiện các tác vụ không đồng bộ sau khi trả về phản hồi
    try {
      // Lấy thông tin để gửi thông báo
      const submitter = await User.findById(report.user).select('fullName email');
      const manager = await User.findById(req.user.id).select('fullName');

      // 1. Tạo thông báo
      await createNotification(report.user.toString(), {
        message: `Báo cáo công việc của bạn đã được duyệt bởi ${manager.fullName}`,
        type: 'REPORT',
        icon: 'fas fa-check-circle',
        link: `/work-reports`,
      });

      // 2. Gửi thông báo WebSocket
      if (global.wsService) {
        global.wsService.sendNotification(report.user.toString(), {
          message: `Báo cáo công việc của bạn đã được duyệt bởi ${manager.fullName}`,
          type: 'REPORT',
          icon: 'fas fa-check-circle',
          link: `/work-reports`,
        });
      }

      // 3. Gửi email nếu người dùng có email
      if (submitter.email && submitter.email.trim() !== '' && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
        try {
          const mailOptions = {
            from: process.env.EMAIL_USER,
            to: submitter.email,
            subject: `Thông báo: Báo cáo công việc của bạn đã được duyệt`,
            html: `
              <p>Xin chào ${submitter.fullName},</p>
              <p>Báo cáo công việc của bạn đã được duyệt bởi ${manager.fullName}.</p>
              <p>Vui lòng truy cập hệ thống để xem chi tiết: <a href="https://quanly.newatlantic.vn/work-reports">Link hệ thống</a></p>
              <p>Trân trọng,<br>Hệ thống quản lý báo cáo</p>
            `,
          };
          await transporter.sendMail(mailOptions);
          console.log('Email đã được gửi đến:', submitter.email);
        } catch (emailError) {
          console.error('Lỗi khi gửi email thông báo duyệt báo cáo:', emailError);
          // Không throw lỗi, chỉ ghi log để quá trình vẫn tiếp tục
        }
      } else {
        console.log('Không gửi email thông báo duyệt báo cáo do người dùng không có địa chỉ email hợp lệ hoặc cấu hình email chưa được thiết lập');
      }

    } catch (asyncError) {
      console.error('Lỗi trong quá trình gửi thông báo và email:', asyncError);
      // Không ảnh hưởng đến phản hồi vì đã gửi về phía client
    }

  } catch (error) {
    console.error('Lỗi khi duyệt báo cáo:', error);
    res.status(500).json({ error: 'Lỗi khi duyệt báo cáo', details: error.message });
  }
});

router.delete('/draft/:id', auth, async (req, res) => {
  try {
    const reportId = req.params.id;
    // Loại bỏ ràng buộc về user: cho phép mọi người xóa bản nháp
    const report = await WorkReport.findOne({ 
      _id: reportId,
      approvalStatus: 'draft'
    });
    
    if (!report) {
      return res.status(404).json({ error: 'Không tìm thấy báo cáo nháp' });
    }
    
    // Xóa báo cáo nháp
    await WorkReport.findByIdAndDelete(reportId);
    
    return res.json({ message: 'Xóa báo cáo nháp thành công' });
  } catch (error) {
    console.error('Error deleting draft report:', error);
    return res.status(500).json({ error: 'Lỗi server khi xóa báo cáo nháp' });
  }
});

// DELETE: Xóa bình luận khỏi báo cáo
router.delete('/:id/comments/:commentId', auth, async (req, res) => {
  try {
    const reportId = req.params.id;
    const commentId = req.params.commentId;
    const userId = req.user.id;
    const report = await WorkReport.findById(reportId);
    if (!report) {
      return res.status(404).json({ error: 'Báo cáo không tồn tại' });
    }
    const comment = report.comments.id(commentId);
    if (!comment) {
      return res.status(404).json({ error: 'Bình luận không tồn tại' });
    }
    const isCommentOwner = comment.user.toString() === userId;
    const isAdminOrSuper = ['ADMIN', 'SUPER_ADMIN'].includes(req.user.role);
    if (!isCommentOwner && !isAdminOrSuper) {
      return res.status(403).json({ error: 'Bạn không có quyền xóa bình luận này' });
    }
    report.comments = report.comments.filter(c => c._id.toString() !== commentId);
    await report.save();
    const updatedReport = await WorkReport.findById(reportId)
      .populate('user', 'fullName company')
      .populate('managers', 'fullName role')
      .populate('comments.user', 'fullName');
    res.json({ message: 'Xóa bình luận thành công', report: updatedReport });
  } catch (err) {
    console.error('Lỗi khi xóa bình luận:', err);
    res.status(500).json({ error: err.message });
  }
});

router.put('/:id', auth, async (req, res) => {
  try {
    let report = await WorkReport.findById(req.params.id);
    if (!report) {
      console.log(`[PUT /${req.params.id}] Report not found`);
      return res.status(404).json({ error: 'Báo cáo không tồn tại' });
    }

    const reportOwnerId = (report.user._id ? report.user._id : report.user).toString();
    const reqUserId = req.user.id.toString();

    const isAdmin = ['ADMIN', 'SUPER_ADMIN'].includes(req.user.role);
    const isManager = ['LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'REPORT_MANAGER', 'ACCOUNTANT'].includes(req.user.role);
    const isAssignedManager = report.managers.some(m => m.toString() === reqUserId);
    const isReportOwner = reportOwnerId === reqUserId;

    if (!(isReportOwner || isAdmin || (isManager && isAssignedManager))) {
      return res.status(403).json({ error: 'Bạn không có quyền chỉnh sửa hoặc cập nhật báo cáo này' });
    }

    let updatePerformed = false;
    let message = 'Báo cáo đã được cập nhật thành công';
    let taskCompleted = false;
    
    // THÊM XỬ LÝ CHUYỂN ĐỔI DRAFT THÀNH PENDING
    // Kiểm tra nếu đang chuyển từ draft sang pending
    if (report.approvalStatus === 'draft' && req.body.approvalStatus === 'pending') {
      console.log(`Chuyển báo cáo ${report._id} từ nháp thành chính thức (pending)`);
      report.approvalStatus = 'pending';
      
      // Loại bỏ trạng thái nháp nếu có
      if (req.body.isDraft === false) {
        report.isDraft = false;
      }
      
      updatePerformed = true;
      message = 'Báo cáo đã được chuyển từ nháp thành chính thức thành công';
      
      // Gửi thông báo đến các quản lý
      try {
        const submitter = await User.findById(report.user).select('fullName');
        
        // Thông báo cho các quản lý
        for (const managerId of report.managers) {
          await createNotification(managerId, {
            message: `${submitter.fullName} đã gửi báo cáo công việc`,
            type: 'REPORT',
            icon: 'fas fa-file-alt',
            link: `/work-reports`,
          });
        }
      } catch (notifyError) {
        console.error('Lỗi khi gửi thông báo khi chuyển draft thành pending:', notifyError);
      }
    }

    // Cập nhật trạng thái công việc nếu có gửi taskId và status
    if (req.body.taskId && req.body.status) {
      const taskIndex = report.tasks.findIndex(task => task._id.toString() === req.body.taskId);
      if (taskIndex === -1) {
        return res.status(404).json({ error: 'Công việc không tồn tại trong báo cáo' });
      }
      if (report.tasks[taskIndex].status !== req.body.status) {
        report.tasks[taskIndex].status = req.body.status;
        updatePerformed = true;
        message = 'Trạng thái công việc đã được cập nhật thành công';
        if (req.body.status === 'completed') {
          taskCompleted = true;
        }
      }
    }


    if (req.body.managers !== undefined) {
      report.managers = req.body.managers;
      updatePerformed = true;
    }
    if (req.body.tasks !== undefined) {
      report.tasks = req.body.tasks;
      updatePerformed = true;
      const hasCompletedTasks = req.body.tasks.some(task => task.status === 'completed');
      if (hasCompletedTasks) {
        taskCompleted = true;
      }
    }

    await report.save();

    // Nếu có nhiệm vụ hoàn thành, gửi thông báo đến các quản lý (không bắt buộc)
    if (taskCompleted) {
      try {
        const submitter = await User.findById(report.user).select('fullName');
        for (const managerId of report.managers) {
          await createNotification(managerId, {
            message: `${submitter.fullName} đã hoàn thành một công việc trong báo cáo tuần (từ ${formatDate(report.weekStart)})`,
            type: 'REPORT',
            icon: 'fas fa-check-circle',
            link: `/work-reports`
          });
          if (!global.wsService && global.broadcastToUser) {
            global.broadcastToUser(managerId, {
              type: 'notification',
              message: `${submitter.fullName} đã hoàn thành một công việc trong báo cáo tuần (từ ${formatDate(report.weekStart)})`,
              data: {
                type: 'REPORT',
                icon: 'fas fa-check-circle',
                link: `/work-reports`,
                reportId: report._id
              }
            });
          }
        }
      } catch (notifyError) {
        console.error('Lỗi khi gửi thông báo push:', notifyError);
      }
    }

    if (isReportOwner && report.approvalStatus === 'approved' && updatePerformed) {
      try {
        const submitter = await User.findById(report.user).select('fullName');
        for (const managerId of report.managers) {
          await createNotification(managerId, {
            message: `${submitter.fullName} đã cập nhật báo cáo công việc tuần ${formatDate(report.weekStart)}`,
            type: 'REPORT_UPDATE',
            icon: 'fas fa-edit',
            link: `/work-reports`
          });
          if (!global.wsService && global.broadcastToUser) {
            global.broadcastToUser(managerId, {
              type: 'notification',
              message: `${submitter.fullName} đã cập nhật báo cáo công việc tuần ${formatDate(report.weekStart)}`,
              data: {
                type: 'REPORT_UPDATE',
                icon: 'fas fa-edit',
                link: `/work-reports`,
                reportId: report._id
              }
            });
          }
        }
      } catch (notificationError) {
        console.error('Error sending update notifications:', notificationError);
      }
    }

    const updatedReport = await WorkReport.findById(report._id)
      .populate('user', 'fullName company')
      .populate('managers', 'fullName role')
      .populate('comments.user', 'fullName');

    res.json({ message, report: updatedReport });
  } catch (err) {
    console.error('Lỗi khi cập nhật báo cáo:', err.message, err.stack);
    res.status(500).json({ error: 'Lỗi server khi cập nhật báo cáo', details: err.message });
  }
});

// DELETE: Xóa báo cáo
router.delete('/:id', auth, async (req, res) => {
  try {
    const report = await WorkReport.findById(req.params.id);
    if (!report) return res.status(404).json({ error: 'Báo cáo không tồn tại' });
    
    // Cho phép SUPER_ADMIN hoặc người tạo báo cáo xóa báo cáo
    const isOwner = report.user && report.user.toString() === req.user.id;
    const isSuperAdmin = req.user.role === 'SUPER_ADMIN';
    
    if (!isSuperAdmin && !isOwner) {
      return res.status(403).json({ error: 'Bạn không có quyền xóa báo cáo này' });
    }
    
    await WorkReport.findByIdAndDelete(req.params.id);
    res.json({ message: 'Báo cáo đã được xóa thành công' });
  } catch (err) {
    console.error('Lỗi khi xóa báo cáo:', err.message, err.stack);
    res.status(500).json({ error: 'Có lỗi xảy ra khi xóa báo cáo', details: err.message });
  }
});


// POST: Giao việc cho nhân viên
router.post('/assign-task', auth, async (req, res) => {
  try {
    const { employeeId, content, project, dueDate } = req.body;
    const assignedBy = req.user.id;
    if (!mongoose.Types.ObjectId.isValid(employeeId)) {
      return res.status(400).json({ message: 'ID nhân viên không hợp lệ' });
    }
    const task = new AssignedTask({ employeeId, content, project, dueDate, assignedBy, status: 'pending' });
    await task.save();
    // Lấy thông tin người giao việc và nhân viên được giao
    const assigner = await User.findById(assignedBy).select('fullName email');
    const employee = await User.findById(employeeId).select('fullName email');
    
    // Chỉ gửi email nếu nhân viên có email và cấu hình email đã được thiết lập
    if (employee.email && employee.email.trim() !== '' && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
      try {
        // Gửi email thông báo tới nhân viên
        const mailOptions = {
          from: process.env.EMAIL_USER,
          to: employee.email,
          subject: `Thông báo: Bạn được giao công việc mới từ ${assigner.fullName}`,
          html: `
            <p>Xin chào ${employee.fullName},</p>
            <p>Bạn vừa được <strong>${assigner.fullName}</strong> giao một công việc mới.</p>
            <p><strong>Nội dung:</strong> ${content}</p>
            <p><strong>Dự án:</strong> ${project || 'Không xác định'}</p>
            <p><strong>Hạn chót:</strong> ${formatDate(dueDate)}</p>
            <p>Vui lòng truy cập hệ thống để xem chi tiết: <a href="https://quanly.newatlantic.vn/work-reports">Link hệ thống</a></p>
            <p>Trân trọng,<br>Hệ thống quản lý báo cáo</p>
          `,
        };
        await transporter.sendMail(mailOptions);
        console.log('Email sent to:', employee.email);
      } catch (emailError) {
        console.error('Lỗi khi gửi email thông báo giao việc:', emailError);
        // Không throw lỗi, chỉ ghi log để quá trình vẫn tiếp tục
      }
    } else {
      console.log('Không gửi email do nhân viên không có địa chỉ email hợp lệ hoặc cấu hình email chưa được thiết lập');
    }
    
    // THÊM MỚI: Gửi thông báo push cho nhân viên
    try {
      // 1. Tạo thông báo trong database
      await createNotification(employeeId, {
        message: `Bạn được giao công việc mới: ${content}`,
        type: 'TASK',
        icon: 'fas fa-tasks',
        link: `/work-reports`
      });
      
      // 2. Gửi thông báo realtime
      if (global.wsService && global.wsService.sendNotification) {
        global.wsService.sendNotification(employeeId, {
          message: `Bạn được giao công việc mới: ${content}`,
          type: 'TASK',
          icon: 'fas fa-tasks',
          link: `/work-reports`,
          data: { taskId: task._id }
        });
      }
      
      console.log('Push notification sent to employee:', employeeId);
    } catch (notifyError) {
      console.error('Lỗi khi gửi thông báo push:', notifyError);
    }
    
    res.status(201).json({ 
      message: 'Giao việc thành công và thông báo đã được gửi tới nhân viên', 
      task 
    });
  } catch (error) {
    console.error('Error assigning task:', error);
    res.status(500).json({ message: 'Lỗi khi giao việc', error: error.message });
  }
});

// GET: Lấy danh sách công việc đã giao (cho manager)
router.get('/assigned-tasks/manager', auth, async (req, res) => {
  try {
    const managerRoles = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'];
    if (!managerRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Bạn không có quyền xem danh sách công việc đã giao' });
    }
    const tasks = await AssignedTask.find({ assignedBy: req.user.id })
      .populate('employeeId', 'fullName')
      .populate('assignedBy', 'fullName')
      .select('content project dueDate employeeId status images createdAt');
    res.json(tasks);
  } catch (error) {
    res.status(500).json({ message: 'Lỗi khi lấy danh sách công việc đã giao', error: error.message });
  }
});

// PUT: Cập nhật công việc đã giao (cho manager)
router.put('/assigned-tasks/manager/:id', auth, async (req, res) => {
  try {
    const managerRoles = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'];
    if (!managerRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Bạn không có quyền cập nhật công việc đã giao' });
    }
    
    const { id } = req.params;
    const { employeeId, content, project, dueDate } = req.body;
    
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'ID công việc không hợp lệ' });
    }
    
    // Kiểm tra xem công việc có tồn tại và thuộc về manager này không
    const task = await AssignedTask.findOne({ _id: id, assignedBy: req.user.id });
    if (!task) {
      return res.status(404).json({ message: 'Không tìm thấy công việc hoặc bạn không có quyền cập nhật' });
    }
    
    // Cập nhật thông tin công việc
    const updatedTask = await AssignedTask.findByIdAndUpdate(
      id,
      { 
        employeeId: employeeId || task.employeeId,
        content: content || task.content,
        project: project || task.project,
        dueDate: dueDate || task.dueDate
      },
      { new: true }
    )
    .populate('employeeId', 'fullName')
    .populate('assignedBy', 'fullName');
    
    // Gửi thông báo cho nhân viên về việc cập nhật công việc
    try {
      // Tạo thông báo trong database
      await createNotification(updatedTask.employeeId._id, {
        message: `Công việc của bạn đã được cập nhật: ${updatedTask.content}`,
        type: 'TASK',
        icon: 'fas fa-edit',
        link: `/work-reports`
      });
      
      // Gửi thông báo realtime
      if (global.wsService && global.wsService.sendNotification) {
        global.wsService.sendNotification(updatedTask.employeeId._id, {
          message: `Công việc của bạn đã được cập nhật: ${updatedTask.content}`,
          type: 'TASK',
          icon: 'fas fa-edit',
          link: `/work-reports`,
          data: { taskId: updatedTask._id }
        });
      }
    } catch (notifyError) {
      console.error('Lỗi khi gửi thông báo cập nhật công việc:', notifyError);
    }
    
    res.json({ 
      message: 'Cập nhật công việc thành công', 
      task: updatedTask 
    });
  } catch (error) {
    console.error('Error updating assigned task:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật công việc', error: error.message });
  }
});

// DELETE: Xóa công việc đã giao (cho manager)
router.delete('/assigned-tasks/manager/:id', auth, async (req, res) => {
  try {
    const managerRoles = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'];
    if (!managerRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Bạn không có quyền xóa công việc đã giao' });
    }
    
    const { id } = req.params;
    
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'ID công việc không hợp lệ' });
    }
    
    // Kiểm tra xem công việc có tồn tại và thuộc về manager này không
    const task = await AssignedTask.findOne({ _id: id, assignedBy: req.user.id });
    if (!task) {
      return res.status(404).json({ message: 'Không tìm thấy công việc hoặc bạn không có quyền xóa' });
    }
    
    // Lưu thông tin nhân viên trước khi xóa để gửi thông báo
    const employeeId = task.employeeId;
    const taskContent = task.content;
    
    // Xóa công việc
    await AssignedTask.findByIdAndDelete(id);
    
    // Gửi thông báo cho nhân viên về việc xóa công việc
    try {
      // Tạo thông báo trong database
      await createNotification(employeeId, {
        message: `Công việc đã bị hủy: ${taskContent}`,
        type: 'TASK',
        icon: 'fas fa-trash',
        link: `/work-reports`
      });
      
      // Gửi thông báo realtime
      if (global.wsService && global.wsService.sendNotification) {
        global.wsService.sendNotification(employeeId, {
          message: `Công việc đã bị hủy: ${taskContent}`,
          type: 'TASK',
          icon: 'fas fa-trash',
          link: `/work-reports`
        });
      }
    } catch (notifyError) {
      console.error('Lỗi khi gửi thông báo xóa công việc:', notifyError);
    }
    
    res.json({ message: 'Xóa công việc thành công' });
  } catch (error) {
    console.error('Error deleting assigned task:', error);
    res.status(500).json({ message: 'Lỗi khi xóa công việc', error: error.message });
  }
});

// DELETE: Xóa nhiều công việc đã giao (cho manager)
router.delete('/assigned-tasks/manager/batch', auth, async (req, res) => {
  try {
    console.log('Batch delete request received:', req.body);
    
    const managerRoles = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'];
    if (!managerRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Bạn không có quyền xóa công việc đã giao' });
    }
    
    const { taskIds } = req.body;
    console.log('Task IDs received:', taskIds);
    
    if (!Array.isArray(taskIds) || taskIds.length === 0) {
      return res.status(400).json({ message: 'Danh sách ID công việc không hợp lệ' });
    }
    
    // Kiểm tra xem tất cả các ID có hợp lệ không
    const validTaskIds = [];
    for (const id of taskIds) {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        console.error('Invalid task ID:', id);
        return res.status(400).json({ 
          message: 'ID công việc không hợp lệ', 
          invalidId: id,
          allIds: taskIds
        });
      }
      validTaskIds.push(id);
    }
    
    if (validTaskIds.length === 0) {
      return res.status(400).json({ message: 'Không có ID công việc hợp lệ để xóa' });
    }
    
    // Kiểm tra xem tất cả các công việc có tồn tại và thuộc về manager này không
    const tasks = await AssignedTask.find({ 
      _id: { $in: validTaskIds }, 
      assignedBy: req.user.id 
    });
    
    if (tasks.length !== validTaskIds.length) {
      return res.status(404).json({ 
        message: 'Một số công việc không tồn tại hoặc bạn không có quyền xóa',
        foundCount: tasks.length,
        requestedCount: validTaskIds.length,
        foundIds: tasks.map(t => t._id.toString()),
        requestedIds: validTaskIds
      });
    }
    
    // Lưu thông tin các nhân viên và nội dung công việc để gửi thông báo
    const notifications = tasks.map(task => ({
      employeeId: task.employeeId,
      content: task.content
    }));
    
    // Xóa các công việc
    await AssignedTask.deleteMany({ _id: { $in: validTaskIds } });
    
    // Gửi thông báo cho các nhân viên về việc xóa công việc
    try {
      for (const notification of notifications) {
        // Tạo thông báo trong database
        await createNotification(notification.employeeId, {
          message: `Công việc đã bị hủy: ${notification.content}`,
          type: 'TASK',
          icon: 'fas fa-trash',
          link: `/work-reports`
        });
        
        // Gửi thông báo realtime
        if (global.wsService && global.wsService.sendNotification) {
          global.wsService.sendNotification(notification.employeeId, {
            message: `Công việc đã bị hủy: ${notification.content}`,
            type: 'TASK',
            icon: 'fas fa-trash',
            link: `/work-reports`
          });
        }
      }
    } catch (notifyError) {
      console.error('Lỗi khi gửi thông báo xóa công việc:', notifyError);
    }
    
    res.json({ 
      message: 'Xóa các công việc thành công',
      count: tasks.length
    });
  } catch (error) {
    console.error('Error deleting multiple assigned tasks:', error);
    
    // Provide more detailed error message
    let errorMessage = 'Lỗi khi xóa các công việc';
    if (error.name === 'CastError') {
      errorMessage = `ID không hợp lệ: ${error.value}`;
    } else if (error.name === 'ValidationError') {
      errorMessage = Object.values(error.errors).map(e => e.message).join(', ');
    }
    
    res.status(500).json({ 
      message: errorMessage, 
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// GET: Lấy danh sách công việc được giao (cho nhân viên)
router.get('/assigned-tasks', auth, async (req, res) => {
  try {
    const tasks = await AssignedTask.find({ employeeId: req.user.id })
      .populate('assignedBy', 'fullName')
      .select('content project dueDate assignedBy status images createdAt');
    res.json(tasks);
  } catch (error) {
    res.status(500).json({ message: 'Lỗi khi lấy danh sách công việc được giao', error: error.message });
  }
});

// Fixed: Upload ảnh cho công việc trong báo cáo cá nhân
router.post('/:reportId/tasks/:taskId/upload', auth, upload.array('images', 5), async (req, res) => {
  const { reportId, taskId } = req.params;
  try {
    const report = await WorkReport.findById(reportId);
    if (!report) return res.status(404).json({ message: 'Báo cáo không tồn tại' });
    
    const isManager = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'].includes(req.user.role);
    
    const reportUserId = report.user._id ? report.user._id.toString() : report.user.toString();
    const currentUserId = req.user.id.toString();
    
    const isReportOwner = reportUserId === currentUserId;
    const isAssignedManager = report.managers.some(m => 
      (m._id ? m._id.toString() : m.toString()) === currentUserId
    );
    
    if (!isReportOwner && !(isManager && isAssignedManager)) {
      return res.status(403).json({ message: 'Bạn không có quyền upload ảnh cho báo cáo này' });
    }
    
    const task = report.tasks.id(taskId);
    if (!task) return res.status(404).json({ message: 'Công việc không tồn tại' });
    const imageUrls = req.files.map(file => file.path);
    task.images = task.images ? [...task.images, ...imageUrls] : imageUrls;
    await report.save();
    res.json({ message: 'Upload ảnh thành công', images: task.images });
  } catch (error) {
    console.error('Lỗi khi upload ảnh:', error);
    res.status(500).json({ message: 'Lỗi khi upload ảnh', error: error.message });
  }
});

// POST: Upload ảnh cho công việc được giao
router.post('/assigned-tasks/:taskId/upload', auth, upload.array('images', 5), async (req, res) => {
  const { taskId } = req.params;
  try {
    const task = await AssignedTask.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Công việc không tồn tại' });
    }
    
    const assignedUserId = task.employeeId._id ? 
      task.employeeId._id.toString() : task.employeeId.toString();
    const currentUserId = req.user.id.toString();
    
    if (assignedUserId !== currentUserId) {
      return res.status(403).json({ message: 'Bạn không có quyền upload ảnh cho công việc này' });
    }
    
    const imageUrls = req.files.map(file => file.path);
    task.images = task.images ? [...task.images, ...imageUrls] : imageUrls;
    await task.save();
    res.json({ message: 'Upload ảnh thành công', images: task.images });
  } catch (error) {
    console.error('Lỗi khi upload ảnh:', error);
    res.status(500).json({ message: 'Lỗi khi upload ảnh', error: error.message });
  }
});

router.get('/drafts', auth, async (req, res) => {
  try {
    const drafts = await WorkReport.find({ user: req.user.id, approvalStatus: 'draft' })
      .populate({
        path: 'tasks.collaborator',
        select: 'fullName role',
        match: { _id: { $exists: true } }
      })
      .sort({ createdAt: -1 });
    res.json({ reports: drafts });
  } catch (error) {
    console.error('Error fetching draft reports:', error);
    res.status(500).json({ error: error.message });
  }
});

router.post('/draft', auth, async (req, res) => {
  try {
    let { weekStart, tasks, managers } = req.body;
    if (typeof tasks === 'string') tasks = JSON.parse(tasks);
    if (typeof managers === 'string') managers = JSON.parse(managers);

    // For drafts, we only need to validate that tasks is an array
    if (!tasks || !Array.isArray(tasks)) {
      return res.status(400).json({ error: 'Vui lòng cung cấp danh sách công việc' });
    }
    
    // Process tasks to ensure they have valid default values
    const processedTasks = tasks.map(task => ({
      date: task.date || new Date(),
      content: task.content || '',
      project: task.project || '',
      startTime: task.startTime || new Date(),
      endTime: task.endTime || null,
      collaborator: task.collaborator || [],
      progress: task.progress || '',
      completionPercentage: task.completionPercentage || 0,
      status: task.status || 'ongoing',
      images: task.images || []
    }));
    
    const report = new WorkReport({
      user: req.user.id,
      managers: managers || [],
      weekStart: weekStart || null,
      tasks: processedTasks,
      approvalStatus: 'draft',  
    });
    
    await report.save();
    return res.status(201).json({ message: 'Báo cáo nháp đã được lưu thành công', report });
  } catch (error) {
    console.error('Error creating draft report:', error);
    return res.status(500).json({ error: error.message });
  }
});

router.put('/draft/:id', auth, async (req, res) => {
  try {
    let { weekStart, tasks, managers } = req.body;
    if (typeof tasks === 'string') tasks = JSON.parse(tasks);
    if (typeof managers === 'string') managers = JSON.parse(managers);

    // For drafts, we only need to validate that tasks is an array
    if (!tasks || !Array.isArray(tasks)) {
      return res.status(400).json({ error: 'Vui lòng cung cấp danh sách công việc' });
    }

    const report = await WorkReport.findById(req.params.id);
    if (!report) {
      return res.status(404).json({ error: 'Không tìm thấy báo cáo nháp' });
    }
    
    // Process tasks to ensure they have valid default values
    const processedTasks = tasks.map(task => ({
      date: task.date || new Date(),
      content: task.content || '',
      project: task.project || '',
      startTime: task.startTime || new Date(),
      endTime: task.endTime || null,
      collaborator: task.collaborator || [],
      progress: task.progress || '',
      completionPercentage: task.completionPercentage || 0,
      status: task.status || 'ongoing',
      images: task.images || []
    }));

    report.weekStart = weekStart || null;
    report.managers = managers || [];
    report.tasks = processedTasks;
    report.approvalStatus = 'draft';

    await report.save();
    return res.json({ message: 'Cập nhật báo cáo nháp thành công', report });
  } catch (error) {
    console.error('Error updating draft report:', error);
    return res.status(500).json({ error: error.message });
  }
});


// PUT: Cập nhật trạng thái công việc được giao
router.put('/assigned-tasks/:taskId', auth, async (req, res) => {
  try {
    const { taskId } = req.params;
    const { status } = req.body;
    const task = await AssignedTask.findById(taskId);
    
    if (!task) {
      return res.status(404).json({ message: 'Công việc không tồn tại' });
    }
    
    // Kiểm tra quyền - chỉ người được giao việc mới có quyền cập nhật
    if (task.employeeId.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Bạn không có quyền cập nhật trạng thái công việc này' });
    }
    
    // Cập nhật trạng thái
    task.status = status;
    await task.save();
    
    // Lấy thông tin để gửi thông báo
    const employee = await User.findById(req.user.id).select('fullName');
    const manager = await User.findById(task.assignedBy).select('fullName email');
    
    // Tạo thông điệp dựa trên trạng thái
    let statusText = '';
    let iconClass = '';
    
    switch(status) {
      case 'accepted':
        statusText = 'đã xác nhận';
        iconClass = 'fas fa-user-check';
        break;
      case 'in_progress':
        statusText = 'đang thực hiện';
        iconClass = 'fas fa-spinner';
        break;
      case 'completed':
        statusText = 'đã hoàn thành';
        iconClass = 'fas fa-check-circle';
        break;
      case 'rejected':
        statusText = 'đã từ chối';
        iconClass = 'fas fa-times-circle';
        break;
      default:
        statusText = 'đã cập nhật trạng thái';
        iconClass = 'fas fa-sync';
    }
    
    // THÊM MỚI: Gửi thông báo push cho người giao việc
    try {
      // 1. Tạo thông báo trong database
      await createNotification(task.assignedBy.toString(), {
        message: `${employee.fullName} ${statusText} công việc: ${task.content}`,
        type: 'TASK',
        icon: iconClass,
        link: `/work-reports`,
      });
      
      // 2. Gửi thông báo realtime
      if (global.wsService && global.wsService.sendNotification) {
        global.wsService.sendNotification(task.assignedBy.toString(), {
          message: `${employee.fullName} ${statusText} công việc: ${task.content}`,
          type: 'TASK',
          icon: iconClass,
          link: `/work-reports`,
          data: { taskId: task._id, status: status }
        });
      }
      
      console.log('Push notification sent to manager:', task.assignedBy);
      
      // Gửi email thông báo cập nhật (tùy chọn)
      if (manager && manager.email) {
        const mailOptions = {
          from: process.env.EMAIL_USER,
          to: manager.email,
          subject: `Thông báo: Cập nhật trạng thái công việc từ ${employee.fullName}`,
          html: `
            <p>Xin chào ${manager.fullName},</p>
            <p>${employee.fullName} vừa ${statusText} công việc:</p>
            <p><strong>Nội dung:</strong> ${task.content}</p>
            <p><strong>Dự án:</strong> ${task.project || 'Không xác định'}</p>
            <p>Vui lòng truy cập hệ thống để xem chi tiết: <a href="https://quanly.newatlantic.vn/assigned-tasks/manager">Link hệ thống</a></p>
            <p>Trân trọng,<br>Hệ thống quản lý báo cáo</p>
          `,
        };
        await transporter.sendMail(mailOptions);
        console.log('Email sent to manager:', manager.email);
      }
      
    } catch (notifyError) {
      console.error('Lỗi khi gửi thông báo push:', notifyError);
    }
    
    // Populate data trước khi trả về
    const updatedTask = await AssignedTask.findById(taskId)
      .populate('employeeId', 'fullName')
      .populate('assignedBy', 'fullName');
    
    res.status(200).json({ 
      message: 'Cập nhật trạng thái thành công', 
      task: updatedTask 
    });
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật trạng thái' });
  }
});

// GET: Xuất báo cáo Excel
router.get('/export', auth, async (req, res) => {
  if (!['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'REPORT_MANAGER', 'ACCOUNTANT'].includes(req.user.role)) {
    return res.status(403).json({ error: 'Bạn không có quyền truy cập chức năng này' });
  }
  try {
    const { userName, weekStart, status, manager } = req.query;
    let query = {};
    if (userName) {
      query['user.fullName'] = { $regex: new RegExp(userName, 'i') };
    }
    if (weekStart) {
      const startDate = new Date(weekStart).toISOString().split('T')[0];
      query.weekStart = startDate;
    }
    if (status) {
      query = { ...query, 'tasks.status': status };
    }
    if (manager && !['ADMIN', 'SUPER_ADMIN'].includes(req.user.role)) {
      query.managers = new mongoose.Types.ObjectId(manager);
    }
    const reports = await WorkReport.find(query)
      .populate('user', 'fullName company')
      .populate('managers', 'fullName role');
    if (reports.length === 0) {
      return res.status(404).json({ error: 'Không tìm thấy báo cáo nào để xuất.' });
    }
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Work Report');
    worksheet.addRow(['Báo cáo dữ liệu được tạo vào:', new Date().toLocaleString('vi-VN')]);
    worksheet.addRow([`Lọc theo: Tên nhân viên: ${userName || '*'}, Tuần: ${weekStart || '*'}, Trạng thái: ${status || '*'}, Người quản lý: ${manager || '*'}`]);
    worksheet.addRow([]);
    worksheet.columns = [
      { header: 'Tên nhân viên', key: 'fullName', width: 20 },
      { header: 'Công ty', key: 'company', width: 20 },
      { header: 'Người quản lý', key: 'managerNames', width: 30 },
      { header: 'Tuần bắt đầu', key: 'weekStart', width: 15 },
      { header: 'STT', key: 'taskIndex', width: 10 },
      { header: 'Ngày', key: 'date', width: 15 },
      { header: 'Nội dung công việc', key: 'content', width: 30 },
      { header: 'Dự án', key: 'project', width: 20 },
      { header: 'Từ ngày', key: 'startTime', width: 15 },
      { header: 'Đến ngày', key: 'endTime', width: 15 },
      { header: 'Cá nhân phối hợp', key: 'collaborator', width: 20 },
      { header: 'Tiến độ chung', key: 'progress', width: 20 },
      { header: 'Trạng thái', key: 'status', width: 15 }
    ];
    reports.forEach((report, reportIndex) => {
      report.tasks.forEach((task, taskIndex) => {
        worksheet.addRow({
          fullName: report.user?.fullName || 'N/A',
          company: report.user?.company || 'N/A',
          managerNames: report.managers.map(m => m.fullName).join(', ') || 'N/A',
          weekStart: formatDate(report.weekStart),
          taskIndex: taskIndex + 1,
          date: formatDate(task.date),
          content: task.content || 'N/A',
          project: task.project || 'N/A',
          startTime: formatDate(task.startTime),
          endTime: formatDate(task.endTime),
          individual: task.individual || 'N/A',
          collaborator: task.collaborator || 'N/A',
          progress: task.progress || 'N/A',
          status: task.status === 'ongoing' ? 'Tiếp tục' : 'Hoàn thành'
        }); 
      });
    });
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=work_report_${new Date().toISOString().split('T')[0]}.xlsx`);
    await workbook.xlsx.write(res);
    res.end();
  } catch (err) {
    console.error('Lỗi khi xuất báo cáo công việc:', err);
    res.status(500).json({ error: 'Lỗi server khi xuất báo cáo công việc' });
  }
});

// GET: Lấy danh sách công việc được giao (cho tất cả - dành cho quản lý)
router.get('/all-tasks', auth, async (req, res) => {
  try {
    // Lấy thông tin user từ token
    const userId = req.user.id;
    const userRole = req.user.role;
    
    // Tìm tất cả các báo cáo
    const reports = await WorkReport.find()
      .populate('user', 'fullName email role department')
      .populate('managers', 'fullName email role')
      .sort({ createdAt: -1 });
    
    // Tạo mảng chứa tất cả các tasks từ các báo cáo
    let allTasks = [];
    
    // Thêm mỗi task vào mảng kết quả kèm theo thông tin báo cáo
    reports.forEach(report => {
      if (report.tasks && Array.isArray(report.tasks)) {
        report.tasks.forEach(task => {
          allTasks.push({
            ...task.toObject(),
            report: {
              _id: report._id,
              user: report.user,
              managers: report.managers,
              approvalStatus: report.approvalStatus,
              weekStart: report.weekStart,
              createdAt: report.createdAt
            }
          });
        });
      }
    });
    
    // Lọc tasks theo quyền hạn người dùng
    if (userRole === 'REPORT_MANAGER') {
      // REPORT_MANAGER chỉ thấy tasks của các báo cáo mà họ là manager
      allTasks = allTasks.filter(task => {
        if (!task.report || !task.report.managers) return false;
        
        // Kiểm tra xem user hiện tại có phải là manager của báo cáo này không
        return task.report.managers.some(manager => {
          // Nếu manager là một đối tượng (được populate)
          if (typeof manager === 'object' && manager._id) {
            return manager._id.toString() === userId.toString();
          }
          // Nếu manager chỉ là ID
          return manager.toString() === userId.toString();
        });
      });
    } else if (!['ADMIN', 'SUPER_ADMIN'].includes(userRole)) {
      // Người dùng thông thường chỉ thấy tasks của mình
      allTasks = allTasks.filter(task => 
        task.report && 
        task.report.user && 
        task.report.user._id.toString() === userId.toString()
      );
    }
    // ADMIN và SUPER_ADMIN thấy tất cả tasks
    
    // Sắp xếp tasks theo thời gian tạo gần nhất
    allTasks.sort((a, b) => new Date(b.date || b.createdAt) - new Date(a.date || a.createdAt));
    
    res.json({ tasks: allTasks });
  } catch (error) {
    console.error('Error fetching all tasks:', error);
    res.status(500).json({ message: 'Lỗi khi tải danh sách công việc', error: error.message });
  }
});

// Hàm định dạng ngày
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const d = new Date(dateString);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
};

// ==================== TASK COMMENTS API ====================

// Lấy danh sách bình luận của một task
router.get('/task-comments/:taskId', auth, async (req, res) => {
  try {
    const { taskId } = req.params;
    
    // Tìm task trong WorkReport để kiểm tra quyền truy cập
    const report = await WorkReport.findOne({ 'tasks._id': taskId })
      .populate('user', 'fullName')
      .populate('managers', 'fullName');
    
    if (!report) {
      return res.status(404).json({ message: 'Không tìm thấy công việc' });
    }
    
    // Kiểm tra quyền truy cập
    const userId = req.user.id || req.user._id;
    const userRole = req.user.role;
    const isReportOwner = report.user._id.toString() === userId.toString();
    const isManager = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'ACCOUNTANT', 'REPORT_MANAGER'].includes(userRole);
    const isAssignedManager = report.managers.some(manager => manager._id.toString() === userId.toString());
    
    // Kiểm tra xem user có phải là collaborator của task này không
    const task = report.tasks.find(t => t._id.toString() === taskId);
    const isCollaborator = task && task.collaborator && Array.isArray(task.collaborator) && 
      task.collaborator.some(collab => {
        if (typeof collab === 'object' && collab._id) {
          return collab._id.toString() === userId.toString();
        }
        return collab.toString() === userId.toString();
      });
    
    if (!isReportOwner && !isManager && !isAssignedManager && !isCollaborator) {
      return res.status(403).json({ message: 'Không có quyền xem bình luận' });
    }
    
    // Lấy danh sách bình luận
    const comments = await TaskComment.find({ taskId })
      .populate('author', 'fullName')
      .sort({ createdAt: 1 }); // Sắp xếp từ cũ đến mới
    
    res.json({ comments });
  } catch (error) {
    console.error('Error fetching task comments:', error);
    res.status(500).json({ message: 'Lỗi khi tải bình luận', error: error.message });
  }
});

// Thêm bình luận mới cho task
router.post('/task-comments/:taskId', auth, async (req, res) => {
  try {
    const { taskId } = req.params;
    const { content } = req.body;
    
    if (!content || !content.trim()) {
      return res.status(400).json({ message: 'Nội dung bình luận không được để trống' });
    }
    
    // Tìm task trong WorkReport để kiểm tra quyền truy cập
    const report = await WorkReport.findOne({ 'tasks._id': taskId })
      .populate('user', 'fullName')
      .populate('managers', 'fullName');
    
    if (!report) {
      return res.status(404).json({ message: 'Không tìm thấy công việc' });
    }
    
    // Kiểm tra quyền bình luận
    const userId = req.user.id || req.user._id;
    const userRole = req.user.role;
    const isReportOwner = report.user._id.toString() === userId.toString();
    const isManager = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'ACCOUNTANT', 'REPORT_MANAGER'].includes(userRole);
    const isAssignedManager = report.managers.some(manager => manager._id.toString() === userId.toString());
    
    // Kiểm tra xem user có phải là collaborator của task này không
    const task = report.tasks.find(t => t._id.toString() === taskId);
    const isCollaborator = task && task.collaborator && Array.isArray(task.collaborator) && 
      task.collaborator.some(collab => {
        if (typeof collab === 'object' && collab._id) {
          return collab._id.toString() === userId.toString();
        }
        return collab.toString() === userId.toString();
      });
    
    if (!isReportOwner && !isManager && !isAssignedManager && !isCollaborator) {
      return res.status(403).json({ message: 'Không có quyền bình luận' });
    }
    
    // Tạo bình luận mới
    const newComment = new TaskComment({
      taskId,
      reportId: report._id,
      author: userId,
      content: content.trim()
    });
    
    await newComment.save();
    
    // Populate author info
    await newComment.populate('author', 'fullName');
    
    // Gửi thông báo cho các bên liên quan (trừ người bình luận)
    console.log('=== DEBUG TASK COMMENT NOTIFICATION ===');
    console.log('Task ID:', taskId);
    console.log('User ID (commenter):', userId);
    console.log('Report user (owner):', report.user._id);
    console.log('Report managers:', report.managers.map(m => m._id));
    
    const notificationRecipients = [];
    
    // Thêm người tạo báo cáo (nếu không phải người bình luận)
    if (report.user._id.toString() !== userId.toString()) {
      notificationRecipients.push(report.user._id);
      console.log('Added report owner to recipients:', report.user._id);
    } else {
      console.log('Skipped report owner (is commenter):', report.user._id);
    }
    
    // Thêm các quản lý (nếu không phải người bình luận)
    report.managers.forEach(manager => {
      if (manager._id.toString() !== userId.toString()) {
        notificationRecipients.push(manager._id);
        console.log('Added manager to recipients:', manager._id);
      } else {
        console.log('Skipped manager (is commenter):', manager._id);
      }
    });
    
    // Thêm các collaborator của task (nếu không phải người bình luận)
    if (task && task.collaborator && Array.isArray(task.collaborator)) {
      task.collaborator.forEach(collab => {
        const collabId = typeof collab === 'object' ? collab._id : collab;
        if (collabId && collabId.toString() !== userId.toString()) {
          // Kiểm tra xem đã có trong danh sách chưa
          if (!notificationRecipients.some(id => id.toString() === collabId.toString())) {
            notificationRecipients.push(collabId);
            console.log('Added collaborator to recipients:', collabId);
          }
        } else {
          console.log('Skipped collaborator (is commenter):', collabId);
        }
      });
    }
    
    console.log('Final task notification recipients:', notificationRecipients);
    
    // Gửi thông báo
    for (const recipientId of notificationRecipients) {
      const isRecipientReportOwner = report.user._id.toString() === recipientId.toString();
      const isCommenterReportOwner = report.user._id.toString() === userId.toString();
      const isCommenterManager = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'ACCOUNTANT', 'REPORT_MANAGER'].includes(req.user.role);
      
      // Kiểm tra xem người bình luận có phải là collaborator không
      const isCommenterCollaborator = task && task.collaborator && Array.isArray(task.collaborator) && 
        task.collaborator.some(collab => {
          const collabId = typeof collab === 'object' ? collab._id : collab;
          return collabId && collabId.toString() === userId.toString();
        });
      
      let message;
      if (isRecipientReportOwner) {
        if (isCommenterManager) {
          message = `Quản lý ${req.user.fullName} đã bình luận về công việc trong báo cáo của bạn`;
        } else if (isCommenterCollaborator) {
          message = `Cá nhân phối hợp ${req.user.fullName} đã bình luận về công việc trong báo cáo của bạn`;
        } else {
          message = `${req.user.fullName} đã bình luận về công việc trong báo cáo của bạn`;
        }
      } else {
        if (isCommenterReportOwner) {
          message = `${report.user.fullName} đã bình luận về công việc trong báo cáo`;
        } else if (isCommenterManager) {
          message = `Quản lý ${req.user.fullName} đã bình luận về công việc trong báo cáo`;
        } else if (isCommenterCollaborator) {
          message = `Cá nhân phối hợp ${req.user.fullName} đã bình luận về công việc trong báo cáo`;
        } else {
          message = `${req.user.fullName} đã bình luận về công việc trong báo cáo`;
        }
      }
      
      console.log(`Creating task notification for user ${recipientId}:`, message);
      
      try {
        await createNotification(recipientId, {
          message: message,
          type: 'COMMENT',
          icon: 'fas fa-comment',
          link: `/work-reports?reportId=${report._id}&action=viewTaskComments&taskId=${taskId}`
        });
        console.log('Task notification created successfully for:', recipientId);
      } catch (notifError) {
        console.error('Error creating task notification for', recipientId, ':', notifError);
      }
    }

    // Gửi email thông báo cho người nộp báo cáo nếu có email và không phải là người bình luận
    if (report.user._id.toString() !== userId.toString() && report.user.email && report.user.email.trim() !== '' && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
      try {
        const mailOptions = {
          from: process.env.EMAIL_USER,
          to: report.user.email,
          subject: `Thông báo: Có bình luận mới về công việc trong báo cáo của bạn`,
          html: `
            <p>Xin chào ${report.user.fullName},</p>
            <p><strong>Quản lý ${req.user.fullName}</strong> đã bình luận về một công việc trong báo cáo tuần ${formatDate(report.weekStart)} của bạn:</p>
            <p><strong>Bình luận:</strong> ${content}</p>
            <p>Vui lòng truy cập hệ thống để xem chi tiết: <a href="https://quanly.newatlantic.vn/work-reports">Link hệ thống</a></p>
            <p>Trân trọng,<br>Hệ thống quản lý báo cáo</p>
          `,
        };
        await transporter.sendMail(mailOptions);
        console.log('Email thông báo bình luận task đã được gửi đến:', report.user.email);
      } catch (emailError) {
        console.error('Lỗi khi gửi email thông báo bình luận task:', emailError);
        // Không throw lỗi, chỉ ghi log để quá trình vẫn tiếp tục
      }
    }
    
    res.status(201).json({ comment: newComment });
  } catch (error) {
    console.error('Error creating task comment:', error);
    res.status(500).json({ message: 'Lỗi khi thêm bình luận', error: error.message });
  }
});

// Xóa bình luận
router.delete('/task-comments/delete/:commentId', auth, async (req, res) => {
  try {
    const { commentId } = req.params;
    const userId = req.user.id || req.user._id;
    const userRole = req.user.role;
    
    // Tìm bình luận
    const comment = await TaskComment.findById(commentId);
    if (!comment) {
      return res.status(404).json({ message: 'Không tìm thấy bình luận' });
    }
    
    // Kiểm tra quyền xóa (chỉ người tạo hoặc quản lý mới được xóa)
    const isCommentAuthor = comment.author.toString() === userId.toString();
    const isManager = ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'ACCOUNTANT', 'REPORT_MANAGER'].includes(userRole);
    
    if (!isCommentAuthor && !isManager) {
      return res.status(403).json({ message: 'Không có quyền xóa bình luận này' });
    }
    
    await TaskComment.findByIdAndDelete(commentId);
    
    res.json({ message: 'Đã xóa bình luận thành công' });
  } catch (error) {
    console.error('Error deleting task comment:', error);
    res.status(500).json({ message: 'Lỗi khi xóa bình luận', error: error.message });
  }
});

module.exports = router;