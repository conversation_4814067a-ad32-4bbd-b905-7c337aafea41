const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  date: { type: Date, required: true },
  content: { type: String, required: true },
  project: { type: String, required: false },
  startTime: { type: Date, required: true },
  endTime: { type: Date, required: false },
  collaborator: [{
    type: mongoose.Schema.Types.Mixed,
    validate: {
      validator: function(value) {
        // Cho phép ObjectId hoặc string
        return mongoose.Types.ObjectId.isValid(value) || typeof value === 'string';
      },
      message: 'Collaborator phải là ObjectId hợp lệ hoặc chuỗi tên'
    }
  }],
  progress: { type: String, required: false },
  completionPercentage: {
    type: Number,
    required: false,
    min: 0,
    max: 100,
    default: 0,
    validate: {
      validator: function(value) {
        return value >= 0 && value <= 100 && value % 10 === 0;
      },
      message: 'Completion percentage must be between 0-100 and in increments of 10'
    }
  },
  status: { type: String, enum: ['ongoing', 'paused', 'completed'], default: 'ongoing' },
  images: [{ type: String }],
});

const workReportSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  managers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  tasks: [taskSchema],
  comments: [
    {
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      content: { type: String, required: true },
      createdAt: { type: Date, default: Date.now },
    },
  ],
  approvalStatus: {
    type: String,
    enum: ['draft','pending', 'approved', 'paused'],
    default: 'pending',
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  createdAt: { type: Date, default: Date.now },
});

module.exports = mongoose.model('WorkReport', workReportSchema);
