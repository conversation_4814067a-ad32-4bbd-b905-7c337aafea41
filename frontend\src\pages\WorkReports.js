// WorkReports.js
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import API from '../services/api';
import { toast } from 'react-toastify';
import { useNavigate, useLocation } from 'react-router-dom';
import { Modal, Button, Form, Badge, Pagination, Card, Tab, Tabs, Dropdown } from 'react-bootstrap';
import '../css/WorkReports.css';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const d = new Date(dateString);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
};

// Helper function để hiển thị collaborator
const renderCollaborators = (collaborator, employees) => {
  if (!collaborator) return '-';
  
  // Xử lý cả array object (từ populate) và array ID
  if (Array.isArray(collaborator)) {
    if (collaborator.length === 0) return '-';
    
    return collaborator.map((collab, i) => {
      // Nếu đã được populate (có fullName), hiển thị trực tiếp
      if (collab && typeof collab === 'object' && collab.fullName) {
        return (
          <div key={i} className="name-item">
            <i className="fas fa-user me-1"></i>
            {collab.fullName}
          </div>
        );
      }
      
      // Nếu là string (tên thủ công), hiển thị với icon khác
      if (typeof collab === 'string' && !employees.find(emp => String(emp._id) === String(collab))) {
        return (
          <div key={i} className="name-item manual-collaborator">
            <i className="fas fa-user-edit me-1 text-warning"></i>
            {collab}
            <small className="text-muted ms-1">(Thủ công)</small>
          </div>
        );
      }
      
      // Nếu chỉ là ID, tìm trong employees
      const employee = employees.find(emp => String(emp._id) === String(collab));
      return employee ? (
        <div key={i} className="name-item">
          <i className="fas fa-user me-1"></i>
          {employee.fullName}
        </div>
      ) : (
        <div key={i} className="name-item text-muted">
          <i className="fas fa-user-slash me-1"></i>
          Không xác định
        </div>
      );
    });
  } else if (typeof collaborator === 'string') {
    // Backward compatibility: nếu là string, coi như tên người
    return collaborator.split(/[,;]/).map((name, i) => (
      <div key={i} className="name-item manual-collaborator">
        <i className="fas fa-user-edit me-1 text-warning"></i>
        {name.trim()}
        <small className="text-muted ms-1">(Thủ công)</small>
      </div>
    ));
  }
  
  return '-';
};

const backendUrl = process.env.REACT_APP_BACKEND_URL?.replace(/\/api$/, '');

const WorkReports = () => {
  const location = useLocation();
  const [myTasks, setMyTasks] = useState([]);              
  const [myTasksPage, setMyTasksPage] = useState([]);  
  const [filteredReports, setFilteredReports] = useState([]);
  const [managers, setManagers] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [assignedTasks, setAssignedTasks] = useState([]);
  const [managerAssignedTasks, setManagerAssignedTasks] = useState([]);
  const [loadingManagers, setLoadingManagers] = useState(true);
  const [loadingEmployees, setLoadingEmployees] = useState(true);
  const [stats, setStats] = useState([]);
  const [loadingStats, setLoadingStats] = useState(true);
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [employeeFilterSearch, setEmployeeFilterSearch] = useState('');
  const [showEmployeeFilterDropdown, setShowEmployeeFilterDropdown] = useState(false);
  const [draftReports, setDraftReports] = useState([]);
  const [showDeleteDraftModal, setShowDeleteDraftModal] = useState(false);
  const [draftToDelete, setDraftToDelete] = useState(null);
  const [formData, setFormData] = useState({
    managers: [],
    tasks: [
      { date: '', content: '', project: '', startTime: '', endTime: '', collaborator: [], progress: '', completionPercentage: 0, status: 'ongoing' }
    ]
  });
  const [assignTaskForm, setAssignTaskForm] = useState({
    employeeId: '',
    content: '',
    project: '',
    dueDate: '',
    completionPercentage: 0
  });
  const [searchData, setSearchData] = useState({
    userName: '',
    dateFrom: '',
    dateTo: '',
    status: '',
    manager: '',
    project: '',
    month: '',
    year: new Date().getFullYear().toString(),
    filterType: 'date'
  });

  const [editing, setEditing] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [reportToDelete, setReportToDelete] = useState(null);
  const [showAssignTaskModal, setShowAssignTaskModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFormCollapsed, setIsFormCollapsed] = useState(false);

  const reportsPerPage = 6;
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(() => {
    const storedTab = localStorage.getItem('activeTab');
    const storedUser = localStorage.getItem('user');
    const user = storedUser ? JSON.parse(storedUser) : null;
    
    if (user?.role === 'SUPER_ADMIN') {
      return storedTab && ['manage', 'manager-assigned', 'all-tasks'].includes(storedTab) ? storedTab : 'manage';
    } else if (user?.role === 'ADMIN') {
      // ADMIN chỉ có thể truy cập 'manager-assigned' và 'all-tasks', không có 'manage'
      return storedTab && ['manager-assigned', 'all-tasks'].includes(storedTab) ? storedTab : 'all-tasks';
    } else {
      const validTabs = ['submit', 'my-reports', 'assigned', 'draft'];
      return storedTab && validTabs.includes(storedTab) ? storedTab : 'submit';
    }
  });
  const [allReports, setAllReports] = useState([]);
  const [commentData, setCommentData] = useState({});
  const [selectedImages, setSelectedImages] = useState({});
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const storedUser = localStorage.getItem('user');
  const user = storedUser ? JSON.parse(storedUser) : null;
  // Thêm state mới ở đầu component WorkReports
  const [allTasks, setAllTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [expandedReports, setExpandedReports] = useState({});
  const [expandedTasks, setExpandedTasks] = useState({});
  const [taskSearchData, setTaskSearchData] = useState({
    userName: '',
    project: '',
    status: '',
    dateFrom: '',
    dateTo: '',
    month: '',
    year: new Date().getFullYear().toString(),
    filterType: 'date'
  });

  const [taskCurrentPage, setTaskCurrentPage] = useState(1);
  const [taskTotalPages, setTaskTotalPages] = useState(1);
  
  // State for unique filter values
  const [uniqueProjects, setUniqueProjects] = useState([]);
  const [uniqueStatuses, setUniqueStatuses] = useState([]);
  const [uniqueUsers, setUniqueUsers] = useState([]);
  
  // State for user's own projects in my-reports tab
  const [myProjects, setMyProjects] = useState([]);
  const [myStatuses, setMyStatuses] = useState([]);
  
  // State for filtered projects based on selected user
  const [filteredProjectsByUser, setFilteredProjectsByUser] = useState([]);
  const [filteredTaskProjectsByUser, setFilteredTaskProjectsByUser] = useState([]);
  
  // Pagination for additional tabs
  const [assignedCurrentPage, setAssignedCurrentPage] = useState(1);
  const [assignedTotalPages, setAssignedTotalPages] = useState(1);
  const [draftCurrentPage, setDraftCurrentPage] = useState(1);
  const [draftTotalPages, setDraftTotalPages] = useState(1);
  const [managerAssignedCurrentPage, setManagerAssignedCurrentPage] = useState(1);
  const [managerAssignedTotalPages, setManagerAssignedTotalPages] = useState(1);
  const [myReportsCurrentPage, setMyReportsCurrentPage] = useState(1);
  const [myReportsTotalPages, setMyReportsTotalPages] = useState(1);
  const [paginatedAssignedTasks, setPaginatedAssignedTasks] = useState([]);
  const [paginatedDraftReports, setPaginatedDraftReports] = useState([]);
  const [paginatedManagerAssignedTasks, setPaginatedManagerAssignedTasks] = useState([]);
  const [paginatedMyReports, setPaginatedMyReports] = useState([]);
  const [filteredMyTasksCount, setFilteredMyTasksCount] = useState(0);
  
  // State for filtering assigned tasks by employee
  const [managerTaskFilter, setManagerTaskFilter] = useState({
    employeeId: '',
    project: '',
    status: ''
  });
  
  // State for task editing
  const [editingTask, setEditingTask] = useState(null);
  const [showEditTaskModal, setShowEditTaskModal] = useState(false);
  const [editTaskForm, setEditTaskForm] = useState({
    employeeId: '',
    content: '',
    project: '',
    dueDate: '',
    completionPercentage: 0
  });
  
  // State for task deletion
  const [showDeleteTaskModal, setShowDeleteTaskModal] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState(null);
  
  // State for multi-select tasks
  const [selectedTasks, setSelectedTasks] = useState([]);
  const [showBatchDeleteModal, setShowBatchDeleteModal] = useState(false);

  // Add these new state variables at the top of your component with other state declarations
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [showCommentsModal, setShowCommentsModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [showReportDetailModal, setShowReportDetailModal] = useState(false);
  
  // State cho modal xuất báo cáo
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportOptions, setExportOptions] = useState({
    format: 'excel',
    includeComments: true,
    includeImages: false,
    dateRange: 'all',
    customDateFrom: '',
    customDateTo: '',
    reportType: 'all',
    selectedEmployees: [],
    selectedProjects: [],
    selectedStatuses: []
  });

  // State cho báo cáo của tôi trong tab submit
  const [myReportsInSubmit, setMyReportsInSubmit] = useState([]);

  // State cho dropdown dự án của user
  const [userProjects, setUserProjects] = useState([]);
  const [projectDropdownStates, setProjectDropdownStates] = useState({});

  // State cho bình luận công việc
  const [taskComments, setTaskComments] = useState([]);
  const [newTaskComment, setNewTaskComment] = useState('');
  const [loadingTaskComments, setLoadingTaskComments] = useState(false);
  const [submittingTaskComment, setSubmittingTaskComment] = useState(false);

  // State cho search collaborator
  const [collaboratorSearchStates, setCollaboratorSearchStates] = useState({});
  
  // State cho nhập tên collaborator thủ công
  const [manualCollaboratorInputs, setManualCollaboratorInputs] = useState({});

  const handleTabSelect = (key) => {
    setActiveTab(key);
    localStorage.setItem('activeTab', key);
    
    // Reset filters when changing tabs
    const resetSearchData = { 
      userName: '', 
      dateFrom: '', 
      dateTo: '', 
      status: '', 
      manager: '', 
      project: '',
      month: '',
      year: new Date().getFullYear().toString(),
      filterType: 'date'
    };
    
    const resetTaskSearchData = {
      userName: '',
      project: '',
      status: '',
      dateFrom: '',
      dateTo: '',
      month: '',
      year: new Date().getFullYear().toString(),
      filterType: 'date'
    };
    
    // Reset appropriate filters based on the tab
    setSearchData(resetSearchData);
    setTaskSearchData(resetTaskSearchData);
    setManagerTaskFilter({ employeeId: '', project: '', status: '' });
    
    // Reset danh sách dự án về tất cả khi chuyển tab
    setFilteredProjectsByUser(uniqueProjects);
    setFilteredTaskProjectsByUser(uniqueProjects);
    
    // Reset pagination for all tabs
    setCurrentPage(1);
    setTaskCurrentPage(1);
    setAssignedCurrentPage(1);
    setDraftCurrentPage(1);
    setManagerAssignedCurrentPage(1);
    setMyReportsCurrentPage(1);
    
    // Apply reset filters
    if (allReports.length > 0) {
      applyFilterAndPagination(resetSearchData, allReports, 1);
    }
    
    if (allTasks.length > 0 && key === 'all-tasks') {
      applyTaskFilterAndPagination(resetTaskSearchData, allTasks, 1);
    }
    
    // Update paginated data for other tabs
    if (assignedTasks.length > 0) {
      paginateAssignedTasks(1);
    }
    
    if (draftReports.length > 0) {
      paginateDraftReports(1);
    }
    
    if (managerAssignedTasks.length > 0) {
      paginateManagerAssignedTasks(1);
    }
    
    // Update data for my reports in submit tab
    if (allReports.length > 0 && key === 'submit') {
      paginateMyReportsInSubmit();
    }
    
    // Update data for my reports tab
    if (allReports.length > 0 && key === 'my-reports') {
      paginateMyReports(1);
    }
  };

  const [employeeSearch, setEmployeeSearch] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const isHighLevelManager = user && ['ADMIN', 'LEVEL_I_MANAGER', 'LEVEL_II_MANAGER', 'SUPER_ADMIN', 'ACCOUNTANT'].includes(user.role);
  const isReportManager = user && user.role === 'REPORT_MANAGER';
  const isAdminOrSuper = user && ['ADMIN', 'SUPER_ADMIN'].includes(user.role);
  const isSuperAdmin = user && user.role === 'SUPER_ADMIN';
  const canManageReports = isHighLevelManager || isReportManager;

  // Hàm lọc dự án theo nhân viên được chọn cho tab manage
  const updateFilteredProjectsByUser = (selectedUserId) => {
    if (!selectedUserId) {
      setFilteredProjectsByUser(uniqueProjects);
      return;
    }
    
    const userProjects = new Set();
    allReports.forEach(report => {
      if (String(report.user?._id) === selectedUserId) {
        report.tasks.forEach(task => {
          if (task.project) {
            userProjects.add(task.project);
          }
        });
      }
    });
    
    setFilteredProjectsByUser([...userProjects].sort());
  };

  // Hàm lọc dự án theo nhân viên được chọn cho tab all-tasks
  const updateFilteredTaskProjectsByUser = (selectedUserId) => {
    if (!selectedUserId) {
      setFilteredTaskProjectsByUser(uniqueProjects);
      return;
    }
    
    const userProjects = new Set();
    allTasks.forEach(task => {
      if (String(task.report?.user?._id) === selectedUserId) {
        if (task.project) {
          userProjects.add(task.project);
        }
      }
    });
    
    setFilteredTaskProjectsByUser([...userProjects].sort());
  };

  
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
    fetchReports();
    fetchManagers();
    
    // Chỉ fetch employees nếu user có quyền quản lý
    if (canManageReports || user?.role === 'ADMIN') {
      fetchEmployees();
    } else {
      // Với user thường, sử dụng endpoint collaborators để chọn cá nhân phối hợp
      fetchEmployeesForCollaborator();
    }
    
    if (canManageReports) {
      fetchStats();
      fetchManagerAssignedTasks();
    }
    
    // ADMIN, SUPER_ADMIN và REPORT_MANAGER đều có thể truy cập all-tasks
    if (isAdminOrSuper || isReportManager) {
      if (activeTab === 'all-tasks') {
        fetchAllTasks();
      }
    }
    
    fetchAssignedTasks();
  }, [navigate, activeTab, isAdminOrSuper, isReportManager]);

  // Xử lý query parameters từ thông báo
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const reportId = urlParams.get('reportId');
    const action = urlParams.get('action');
    const taskId = urlParams.get('taskId');

    if (reportId) {
      console.log('Processing notification link:', { reportId, action, taskId });
      
      // Đợi một chút để đảm bảo dữ liệu đã được load
      setTimeout(() => {
        // Tìm báo cáo trong danh sách allReports (tìm trong tất cả báo cáo)
        let targetReport = allReports.find(report => report._id === reportId);
        
        // Nếu không tìm thấy trong allReports, tìm trong filteredReports
        if (!targetReport) {
          targetReport = filteredReports.find(report => report._id === reportId);
        }
        
        if (targetReport) {
          console.log('Found target report:', targetReport);
          
          // Xác định tab phù hợp dựa trên vai trò và hành động
          if (action === 'viewComments') {
            // Nếu là quản lý hoặc có quyền quản lý báo cáo
            if (canManageReports) {
              setActiveTab('manage');
            } else {
              // Nếu là collaborator hoặc nhân viên thường, chuyển đến tab my-reports
              setActiveTab('my-reports');
            }
            
            // Mở modal bình luận báo cáo
            setSelectedReport(targetReport);
            setShowCommentsModal(true);
            console.log('Opening report comments modal');
          } else if (action === 'viewTaskComments' && taskId) {
            // Mở modal bình luận task
            const targetTask = targetReport.tasks?.find(task => task._id === taskId);
            if (targetTask) {
              if (canManageReports) {
                setActiveTab('manage');
              } else {
                setActiveTab('my-reports');
              }
              
              setSelectedTask(targetTask);
              setShowTaskModal(true);
              fetchTaskComments(taskId);
              console.log('Opening task comments modal for task:', targetTask);
            }
          } else {
            // Trường hợp mặc định: hiển thị chi tiết báo cáo (cho collaborator)
            if (canManageReports) {
              setActiveTab('manage');
            } else {
              setActiveTab('my-reports');
            }
            
            // Mở modal chi tiết báo cáo cho collaborator
            setSelectedReport(targetReport);
            setShowReportDetailModal(true);
            console.log('Opening report details modal for collaborator');
          }
          
          // Xóa query parameters sau khi xử lý
          const newUrl = window.location.pathname;
          window.history.replaceState({}, '', newUrl);
        } else {
          console.log('Report not found in current list, may need to fetch reports first');
          // Có thể cần fetch lại reports nếu chưa có
          if (allReports.length === 0 && filteredReports.length === 0) {
            console.log('No reports loaded yet, will retry after reports are loaded');
          }
        }
      }, 1000); // Đợi 1 giây để đảm bảo dữ liệu đã được load
    }
  }, [location.search, allReports, filteredReports, canManageReports]);

  // Effect để load user projects từ API
  useEffect(() => {
    console.log('User changed, loading projects for user:', user?.id);
    console.log('Current userProjects state:', userProjects);
    console.log('User object:', user);
    if (user?.id) {
      console.log('Calling loadUserProjectsFromAPI...');
      // Delay một chút để đảm bảo user object đã sẵn sàng
      setTimeout(() => {
        loadUserProjectsFromAPI();
      }, 100);
    } else {
      console.log('No user ID, skipping project load');
    }
  }, [user?.id]);

  // Effect để load projects khi component mount và user đã có
  useEffect(() => {
    if (user?.id && userProjects.length === 0) {
      console.log('Component mounted with user, loading projects...');
      loadUserProjectsFromAPI();
    }
  }, []);

  // Debug effect để theo dõi userProjects state
  useEffect(() => {
    console.log('UserProjects state updated:', userProjects);
  }, [userProjects]);
  
  // Tự động mở rộng báo cáo đầu tiên khi filteredReports thay đổi
  useEffect(() => {
    if (filteredReports.length > 0 && Object.keys(expandedReports).length === 0) {
      setExpandedReports({ [filteredReports[0]._id]: true });
    }
  }, [filteredReports]);
  
  // Tự động mở rộng công việc đầu tiên khi tải trang
  useEffect(() => {
    if (filteredTasks.length > 0 && Object.keys(expandedTasks).length === 0) {
      setExpandedTasks({ [filteredTasks[0]._id]: true });
    }
  }, [filteredTasks]);

  // Khởi tạo danh sách dự án được lọc khi dữ liệu thay đổi
  useEffect(() => {
    setFilteredProjectsByUser(uniqueProjects);
    setFilteredTaskProjectsByUser(uniqueProjects);
  }, [uniqueProjects]);

  // Cập nhật danh sách dự án khi thay đổi tab
  useEffect(() => {
    if (activeTab === 'manage') {
      updateFilteredProjectsByUser(searchData.userName);
    } else if (activeTab === 'all-tasks') {
      updateFilteredTaskProjectsByUser(taskSearchData.userName);
    }
  }, [activeTab, allReports, allTasks]);



    // Thêm function xử lý tìm kiếm và lọc công việc
  const handleTaskSearchChange = (e) => {
    const { name, value } = e.target;
    let newTaskSearchData = { ...taskSearchData, [name]: value };
    
    // Nếu thay đổi nhân viên trong tab all-tasks, reset dự án và cập nhật danh sách dự án
    if (name === 'userName' && activeTab === 'all-tasks') {
      newTaskSearchData.project = ''; // Reset dự án khi thay đổi nhân viên
      updateFilteredTaskProjectsByUser(value);
    }
    
    setTaskSearchData(newTaskSearchData);
    setTaskCurrentPage(1); // Reset về trang 1 khi thay đổi filter
    applyTaskFilterAndPagination(newTaskSearchData, allTasks, 1);
  };
  
  const applyTaskFilterAndPagination = useCallback((search, tasksData, page) => {
    let filtered = [...tasksData];
    
    if (search.userName) {
      if (search.userName.length === 24) {
        filtered = filtered.filter(task => String(task.report?.user?._id) === search.userName);
      } else {
        filtered = filtered.filter(task => 
          task.report?.user?.fullName?.toLowerCase().includes(search.userName.toLowerCase())
        );
      }
    }
    
    if (search.project) {
      filtered = filtered.filter(task => 
        task.project?.toLowerCase().includes(search.project.toLowerCase())
      );
    }
    if (search.status) {
      filtered = filtered.filter(task => task.status === search.status);
    }
  // Lọc theo thời gian dựa vào filterType
  if (search.filterType === 'date') {
    if (search.dateFrom) {
      filtered = filtered.filter(task => new Date(task.date) >= new Date(search.dateFrom));
    }
    if (search.dateTo) {
      filtered = filtered.filter(task => new Date(task.date) <= new Date(search.dateTo).setHours(23,59,59,999));
    }
  } 
  else if (search.filterType === 'month' && search.month) {
    // Lọc theo tháng (bất kỳ năm nào)
    const month = parseInt(search.month, 10);
    filtered = filtered.filter(task => {
      const taskDate = new Date(task.date);
      return taskDate.getMonth() + 1 === month;
    });
  } 
  else if (search.filterType === 'year' && search.year) {
    // Lọc theo năm
    const year = parseInt(search.year, 10);
    filtered = filtered.filter(task => {
      const taskDate = new Date(task.date);
      return taskDate.getFullYear() === year;
    });
  } 
  else if (search.filterType === 'monthYear' && search.month && search.year) {
    // Lọc theo tháng và năm cụ thể
    const month = parseInt(search.month, 10);
    const year = parseInt(search.year, 10);
    filtered = filtered.filter(task => {
      const taskDate = new Date(task.date);
      return taskDate.getMonth() + 1 === month && taskDate.getFullYear() === year;
    });
  }
  
  // Sắp xếp theo thời gian tạo mới nhất lên đầu
  filtered.sort((a, b) => {
    const dateA = new Date(a.date || a.createdAt || 0);
    const dateB = new Date(b.date || b.createdAt || 0);
    return dateB - dateA; // Sắp xếp giảm dần (mới nhất lên đầu)
  });
  
  // Không phân trang cho tab "all-tasks" - hiển thị tất cả
  setFilteredTasks(filtered);
  setTaskTotalPages(1);
}, [reportsPerPage]);
  
  

  
  const handleAssignedPageChange = (page) => {
    setAssignedCurrentPage(page);
    paginateAssignedTasks(page);
  };
  
  const handleDraftPageChange = (page) => {
    setDraftCurrentPage(page);
    paginateDraftReports(page);
  };
  
  const handleManagerAssignedPageChange = (page) => {
    setManagerAssignedCurrentPage(page);
    paginateManagerAssignedTasks(page);
  };

  const handleMyReportsPageChange = (page) => {
    setMyReportsCurrentPage(page);
    paginateMyReports(page);
  };
  
  // Hàm xử lý mở rộng/thu gọn báo cáo
  const toggleReportExpand = (reportId) => {
    setExpandedReports(prev => {
      // Nếu báo cáo đang được mở, thì đóng nó lại
      if (prev[reportId]) {
        return {
          ...prev,
          [reportId]: false
        };
      }
      // Nếu báo cáo đang đóng, thì đóng tất cả các báo cáo khác và mở báo cáo này
      else {
        // Tạo object mới với tất cả các báo cáo đều đóng
        const newState = {};
        // Chỉ mở báo cáo được chọn
        newState[reportId] = true;
        return newState;
      }
    });
  };
  
  // Hàm xử lý mở rộng/thu gọn công việc
  const toggleTaskExpand = (taskId) => {
    setExpandedTasks(prev => {
      // Nếu công việc đang được mở, thì đóng nó lại
      if (prev[taskId]) {
        return {
          ...prev,
          [taskId]: false
        };
      }
      // Nếu công việc đang đóng, thì đóng tất cả các công việc khác và mở công việc này
      else {
        // Tạo object mới với tất cả các công việc đều đóng
        const newState = {};
        // Chỉ mở công việc được chọn
        newState[taskId] = true;
        return newState;
      }
    });
  };
  
  // Hàm xử lý toggle accordion
  const toggleAccordion = (index, e) => {
    e.preventDefault();
    const accordionCollapse = document.querySelector(`#collapseDetails-${index}`);
    const accordionButton = e.currentTarget;
    
    if (accordionCollapse) {
      const isCollapsed = !accordionCollapse.classList.contains('show');
      
      // Toggle the 'show' class on the collapse element
      if (isCollapsed) {
        accordionCollapse.classList.add('show');
        accordionButton.classList.remove('collapsed');
        accordionButton.setAttribute('aria-expanded', 'true');
      } else {
        accordionCollapse.classList.remove('show');
        accordionButton.classList.add('collapsed');
        accordionButton.setAttribute('aria-expanded', 'false');
      }
    }
  };
  
  // Hàm xử lý mở rộng/thu gọn tất cả báo cáo
  const toggleAllReports = (expand) => {
    const newExpandedState = {};
    // Nếu đang mở rộng và có báo cáo, chỉ mở báo cáo đầu tiên
    if (expand && filteredReports.length > 0) {
      newExpandedState[filteredReports[0]._id] = true;
    }
    // Nếu đang thu gọn, đóng tất cả
    setExpandedReports(newExpandedState);
  };
  
  // Hàm xử lý mở modal xuất báo cáo
  const handleOpenExportModal = (tabType) => {
    // Thiết lập các tùy chọn mặc định dựa trên tab hiện tại
    setExportOptions({
      ...exportOptions,
      reportType: tabType || activeTab,
      customDateFrom: searchData.dateFrom || '',
      customDateTo: searchData.dateTo || '',
      selectedEmployees: searchData.userName ? [searchData.userName] : [],
      selectedProjects: searchData.project ? [searchData.project] : [],
      selectedStatuses: searchData.status ? [searchData.status] : []
    });
    setShowExportModal(true);
  };

  // Hàm xử lý thay đổi tùy chọn xuất báo cáo
  const handleExportOptionChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setExportOptions({
        ...exportOptions,
        [name]: checked
      });
    } else if (name === 'selectedEmployees' || name === 'selectedProjects' || name === 'selectedStatuses') {
      // Xử lý cho select nhiều giá trị
      const selectedValues = Array.from(e.target.selectedOptions, option => option.value);
      setExportOptions({
        ...exportOptions,
        [name]: selectedValues
      });
    } else {
      setExportOptions({
        ...exportOptions,
        [name]: value
      });
    }
  };

  // Hàm lọc dữ liệu theo tùy chọn xuất báo cáo
  const filterDataForExport = (data, options) => {
    if (!data || data.length === 0) {
      return [];
    }
    
    console.log('Filtering data for export:', data, options);
    
    let filteredData = [...data];
    
    // Xác định loại dữ liệu đang xử lý
    const isTaskData = ['all-tasks', 'assigned'].includes(options.reportType);
    
    // Lọc theo nhân viên
    if (options.selectedEmployees && options.selectedEmployees.length > 0) {
      filteredData = filteredData.filter(item => {
        if (isTaskData) {
          // Đối với dữ liệu công việc, kiểm tra user trong report hoặc employeeId
          if (item.user && item.user._id) {
            return options.selectedEmployees.includes(item.user._id);
          } else if (item.report && item.report.user && item.report.user._id) {
            return options.selectedEmployees.includes(item.report.user._id);
          } else if (item.employeeId && item.employeeId._id) {
            return options.selectedEmployees.includes(item.employeeId._id);
          }
          return false;
        } else {
          // Đối với báo cáo thông thường
          return item.user && options.selectedEmployees.includes(item.user._id);
        }
      });
    }
    
    // Lọc theo dự án
    if (options.selectedProjects && options.selectedProjects.length > 0) {
      filteredData = filteredData.filter(item => {
        if (isTaskData) {
          // Đối với dữ liệu công việc, kiểm tra project trực tiếp
          if (item.project) {
            return options.selectedProjects.includes(item.project);
          } else if (item.tasks && item.tasks.length > 0) {
            return options.selectedProjects.includes(item.tasks[0].project);
          }
          return false;
        } else {
          // Đối với báo cáo thông thường, kiểm tra project trong tasks
          return item.tasks && item.tasks.some(task => 
            options.selectedProjects.includes(task.project)
          );
        }
      });
    }
    
    // Lọc theo trạng thái
    if (options.selectedStatuses && options.selectedStatuses.length > 0) {
      filteredData = filteredData.filter(item => {
        if (isTaskData) {
          // Đối với dữ liệu công việc, kiểm tra status trực tiếp
          if (item.status) {
            return options.selectedStatuses.includes(item.status);
          } else if (item.tasks && item.tasks.length > 0) {
            return options.selectedStatuses.includes(item.tasks[0].status);
          }
          return false;
        } else {
          // Đối với báo cáo thông thường, kiểm tra approvalStatus hoặc status
          return (
            (item.approvalStatus && options.selectedStatuses.includes(item.approvalStatus)) ||
            (item.status && options.selectedStatuses.includes(item.status))
          );
        }
      });
    }
    
    // Lọc theo khoảng thời gian
    if (options.dateRange === 'custom') {
      if (options.customDateFrom) {
        const fromDate = new Date(options.customDateFrom);
        fromDate.setHours(0, 0, 0, 0);
        
        filteredData = filteredData.filter(item => {
          let dateToCheck;
          
          if (isTaskData) {
            // Đối với dữ liệu công việc, kiểm tra date, dueDate hoặc createdAt
            dateToCheck = item.date || item.dueDate || item.createdAt;
          } else {
            // Đối với báo cáo thông thường, kiểm tra createdAt
            dateToCheck = item.createdAt;
          }
          
          return new Date(dateToCheck) >= fromDate;
        });
      }
      
      if (options.customDateTo) {
        const toDate = new Date(options.customDateTo);
        toDate.setHours(23, 59, 59, 999);
        
        filteredData = filteredData.filter(item => {
          let dateToCheck;
          
          if (isTaskData) {
            // Đối với dữ liệu công việc, kiểm tra date, dueDate hoặc createdAt
            dateToCheck = item.date || item.dueDate || item.createdAt;
          } else {
            // Đối với báo cáo thông thường, kiểm tra createdAt
            dateToCheck = item.createdAt;
          }
          
          return new Date(dateToCheck) <= toDate;
        });
      }
    } else if (options.dateRange === 'thisWeek') {
      const today = new Date();
      const firstDay = new Date(today);
      firstDay.setDate(today.getDate() - today.getDay());
      firstDay.setHours(0, 0, 0, 0);
      
      const lastDay = new Date(firstDay);
      lastDay.setDate(firstDay.getDate() + 6);
      lastDay.setHours(23, 59, 59, 999);
      
      filteredData = filteredData.filter(item => {
        let dateToCheck;
        
        if (isTaskData) {
          // Đối với dữ liệu công việc, kiểm tra date, dueDate hoặc createdAt
          dateToCheck = item.date || item.dueDate || item.createdAt;
        } else {
          // Đối với báo cáo thông thường, kiểm tra createdAt
          dateToCheck = item.createdAt;
        }
        
        const date = new Date(dateToCheck);
        return date >= firstDay && date <= lastDay;
      });
    } else if (options.dateRange === 'thisMonth') {
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59, 999);
      
      filteredData = filteredData.filter(item => {
        let dateToCheck;
        
        if (isTaskData) {
          // Đối với dữ liệu công việc, kiểm tra date, dueDate hoặc createdAt
          dateToCheck = item.date || item.dueDate || item.createdAt;
        } else {
          // Đối với báo cáo thông thường, kiểm tra createdAt
          dateToCheck = item.createdAt;
        }
        
        const date = new Date(dateToCheck);
        return date >= firstDay && date <= lastDay;
      });
    } else if (options.dateRange === 'lastMonth') {
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59, 999);
      
      filteredData = filteredData.filter(item => {
        let dateToCheck;
        
        if (isTaskData) {
          // Đối với dữ liệu công việc, kiểm tra date, dueDate hoặc createdAt
          dateToCheck = item.date || item.dueDate || item.createdAt;
        } else {
          // Đối với báo cáo thông thường, kiểm tra createdAt
          dateToCheck = item.createdAt;
        }
        
        const date = new Date(dateToCheck);
        return date >= firstDay && date <= lastDay;
      });
    }
    
    console.log('Filtered data result:', filteredData);
    return filteredData;
  };

  // Hàm xử lý xuất báo cáo
  const handleExport = () => {
    try {
      let dataToExport;
      let fileName;
      
      // Chuẩn bị dữ liệu cho xuất báo cáo
      const prepareDataForExport = () => {
        // Xác định dữ liệu cần xuất dựa trên loại báo cáo
        switch (exportOptions.reportType) {
          case 'manage':
            // Báo cáo của nhân viên (từ tab quản lý)
            if (!allReports || allReports.length === 0) {
              toast.warning('Không có dữ liệu báo cáo để xuất', { autoClose: 3000 });
              return null;
            }
            return {
              data: filterDataForExport(allReports, exportOptions),
              fileName: 'bao_cao_nhan_vien'
            };
            

          case 'all-tasks':
            // Danh sách tất cả công việc
            if (!allTasks || allTasks.length === 0) {
              toast.warning('Không có dữ liệu công việc để xuất', { autoClose: 3000 });
              return null;
            }
            
            // Chuyển đổi dữ liệu công việc sang định dạng báo cáo
            const formattedTasks = allTasks.map(task => ({
              _id: task._id,
              user: task.report?.user,
              weekStart: task.date,
              createdAt: task.createdAt,
              status: task.status,
              tasks: [{
                _id: task._id,
                date: task.date,
                content: task.content,
                project: task.project,
                progress: task.progress,
                status: task.status,
                images: task.images || []
              }]
            }));
            
            return {
              data: filterDataForExport(formattedTasks, exportOptions),
              fileName: 'danh_sach_cong_viec'
            };
            
          case 'assigned':
            // Công việc được giao
            if (!assignedTasks || assignedTasks.length === 0) {
              toast.warning('Không có công việc được giao để xuất', { autoClose: 3000 });
              return null;
            }
            
            // Chuyển đổi dữ liệu công việc được giao sang định dạng báo cáo
            const formattedAssignedTasks = assignedTasks.map(task => ({
              _id: task._id,
              user: { 
                _id: task.employeeId?._id || user._id || user.id,
                fullName: task.employeeId?.fullName || user.fullName || 'N/A'
              },
              weekStart: task.dueDate,
              createdAt: task.createdAt,
              status: task.status,
              tasks: [{
                _id: task._id,
                date: task.dueDate,
                content: task.content,
                project: task.project,
                progress: '',
                status: task.status,
                images: task.images || []
              }],
              assignedBy: {
                _id: task.assignedBy?._id,
                fullName: task.assignedBy?.fullName || task.managerName || 'N/A'
              }
            }));
            
            return {
              data: filterDataForExport(formattedAssignedTasks, exportOptions),
              fileName: 'cong_viec_duoc_giao'
            };
            
          case 'draft':
            // Báo cáo nháp
            if (!draftReports || draftReports.length === 0) {
              toast.warning('Không có báo cáo nháp để xuất', { autoClose: 3000 });
              return null;
            }
            
            return {
              data: filterDataForExport(draftReports, exportOptions),
              fileName: 'bao_cao_nhap'
            };
            
          default:
            // Mặc định: báo cáo của nhân viên
            return {
              data: filterDataForExport(allReports, exportOptions),
              fileName: 'bao_cao_cong_viec'
            };
        }
      };
      
      // Chuẩn bị dữ liệu
      const exportData = prepareDataForExport();
      
      // Kiểm tra xem có dữ liệu để xuất không
      if (!exportData || !exportData.data || exportData.data.length === 0) {
        toast.warning('Không có dữ liệu phù hợp với bộ lọc để xuất báo cáo', { autoClose: 3000 });
        return;
      }
      
      // Xuất theo định dạng đã chọn
      if (exportOptions.format === 'excel') {
        exportToExcel(exportData.data, exportData.fileName);
      } else {
        exportToPDF(exportData.data, exportData.fileName);
      }
      
      setShowExportModal(false);
    } catch (error) {
      console.error('Error during export:', error);
      toast.error('Có lỗi khi xuất báo cáo: ' + error.message, { autoClose: 3000 });
    }
  };

  // Export functions for reports
  const exportToExcel = (data, fileName = 'employee_reports') => {
    try {
      if (!data || data.length === 0) {
        toast.warning('Không có dữ liệu để xuất báo cáo', { autoClose: 3000 });
        return;
      }
      
      console.log('Exporting data to Excel:', data);
      
      // Hàm chuyển đổi mã trạng thái thành văn bản hiển thị
      function getStatusText(status) {
        switch (status) {
          case 'pending': return 'Chờ xác nhận';
          case 'approved': return 'Đã duyệt';
          case 'paused': return 'Tạm dừng';
          case 'draft': return 'Bản nháp';
          case 'completed': return 'Hoàn thành';
          case 'ongoing': return 'Đang thực hiện';
          case 'accepted': return 'Đã nhận';
          default: return status || 'N/A';
        }
      }
      
      // Create workbook
      const wb = XLSX.utils.book_new();
      
      // Xác định loại dữ liệu đang xử lý
      const isTaskData = ['all-tasks', 'assigned'].includes(exportOptions.reportType);
      
      // Xác định tiêu đề báo cáo dựa trên loại báo cáo
      let reportTitle = 'Báo cáo công việc';
      switch (exportOptions.reportType) {
        case 'manage':
          reportTitle = 'Báo cáo công việc nhân viên';
          break;
        case 'all-tasks':
          reportTitle = 'Danh sách tất cả công việc';
          break;
        case 'assigned':
          reportTitle = 'Công việc được giao từ cấp trên';
          break;
        case 'draft':
          reportTitle = 'Báo cáo công việc nháp';
          break;
      }
      
      // Tạo sheet tổng quan
      let summaryData = [];
      
      if (isTaskData) {
        // Tổng quan cho dữ liệu công việc
        summaryData = data.map((report, index) => {
          const task = report.tasks[0]; // Mỗi báo cáo chỉ có 1 task trong trường hợp này
          return {
            'STT': index + 1,
            'ID': report._id || 'N/A',
            'Nhân viên': report.user?.fullName || 'N/A',
            'Dự án': task.project || 'N/A',
            'Nội dung công việc': task.content || 'N/A',
            'Ngày': formatDate(task.date || report.weekStart),
            'Trạng thái': getStatusText(task.status),
            'Tiến độ': task.progress || 'N/A'
          };
        });
      } else {
        // Tổng quan cho báo cáo
        summaryData = data.map((report, index) => ({
          'STT': index + 1,
          'ID Báo cáo': report._id || 'N/A',
          'Nhân viên': report.user?.fullName || 'N/A',
          'Email': report.user?.email || 'N/A',
          'Ngày tạo': formatDate(report.createdAt),
          'Trạng thái': getStatusText(report.approvalStatus || report.status),
          'Số công việc': report.tasks.length
        }));
      }
      
      // Thêm sheet tổng quan
      const summaryWs = XLSX.utils.json_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(wb, summaryWs, 'Tổng quan');
      
      // Thêm sheet chi tiết công việc
      if (isTaskData) {
        // Đối với dữ liệu công việc, tạo một sheet chi tiết
        const detailsData = data.map((report, index) => {
          const task = report.tasks[0];
          return {
            'STT': index + 1,
            'ID': report._id || 'N/A',
            'Nhân viên': report.user?.fullName || 'N/A',
            'Dự án': task.project || 'N/A',
            'Nội dung công việc': task.content || 'N/A',
            'Ngày': formatDate(task.date || report.weekStart),
            'Trạng thái': getStatusText(task.status),
            'Tiến độ': task.progress || 'N/A',
            'Ghi chú': task.notes || '',
            'Người giao': report.assignedBy?.fullName || ''
          };
        });
        
        const detailsWs = XLSX.utils.json_to_sheet(detailsData);
        XLSX.utils.book_append_sheet(wb, detailsWs, 'Chi tiết công việc');
      } else {
        // Đối với báo cáo, tạo sheet chi tiết cho từng báo cáo
        data.forEach((report, reportIndex) => {
          // Tạo dữ liệu chi tiết cho báo cáo
          const reportDetailData = [
            [`Báo cáo #${reportIndex + 1}: ${report.user?.fullName || 'N/A'}`],
            [`ID: ${report._id}`],
            [`Tuần bắt đầu: ${formatDate(report.weekStart)}`],
            [`Trạng thái: ${getStatusText(report.approvalStatus || report.status)}`],
            [],
            ['STT', 'Ngày', 'Dự án', 'Nội dung công việc', 'Tiến độ', 'Trạng thái']
          ];
          
          // Thêm dữ liệu công việc
          report.tasks.forEach((task, taskIndex) => {
            reportDetailData.push([
              taskIndex + 1,
              formatDate(task.date),
              task.project || 'N/A',
              task.content || 'N/A',
              task.progress || 'N/A',
              getStatusText(task.status)
            ]);
          });
          
          // Thêm bình luận nếu có
          if (exportOptions.includeComments && report.comments && report.comments.length > 0) {
            reportDetailData.push([]);
            reportDetailData.push(['Bình luận:']);
            
            report.comments.forEach((comment, commentIndex) => {
              reportDetailData.push([
                `${commentIndex + 1}. ${comment.user?.fullName || 'N/A'} (${formatDate(comment.createdAt)}): ${comment.content}`
              ]);
            });
          }
          
          const reportWs = XLSX.utils.aoa_to_sheet(reportDetailData);
          XLSX.utils.book_append_sheet(wb, reportWs, `Báo cáo ${reportIndex + 1}`);
        });
      }
      
      // Thêm sheet thống kê nếu có nhiều báo cáo
      if (data.length > 1) {
        // Thống kê theo trạng thái
        const statusStats = {};
        data.forEach(report => {
          let status;
          
          if (isTaskData) {
            status = getStatusText(report.tasks[0].status);
          } else {
            status = getStatusText(report.approvalStatus || report.status);
          }
          
          if (!statusStats[status]) {
            statusStats[status] = 0;
          }
          statusStats[status]++;
        });
        
        // Thống kê theo dự án
        const projectStats = {};
        data.forEach(report => {
          if (isTaskData) {
            const task = report.tasks[0];
            const project = task.project || 'N/A';
            if (!projectStats[project]) {
              projectStats[project] = 0;
            }
            projectStats[project]++;
          } else {
            report.tasks.forEach(task => {
              const project = task.project || 'N/A';
              if (!projectStats[project]) {
                projectStats[project] = 0;
              }
              projectStats[project]++;
            });
          }
        });
        
        // Thống kê theo nhân viên
        const employeeStats = {};
        data.forEach(report => {
          const employeeName = report.user?.fullName || 'N/A';
          if (!employeeStats[employeeName]) {
            employeeStats[employeeName] = 0;
          }
          
          if (isTaskData) {
            employeeStats[employeeName]++;
          } else {
            employeeStats[employeeName] += report.tasks.length;
          }
        });
        
        // Tạo sheet thống kê
        const statsData = [
          [reportTitle],
          [`Ngày xuất báo cáo: ${formatDate(new Date())}`],
          [],
          ['Thống kê theo trạng thái'],
          ['Trạng thái', 'Số lượng'],
          ...Object.entries(statusStats).map(([status, count]) => [status, count]),
          [],
          ['Thống kê theo dự án'],
          ['Dự án', 'Số lượng công việc'],
          ...Object.entries(projectStats).map(([project, count]) => [project, count]),
          []
        ];
        
        // Thêm thống kê theo nhân viên
        statsData.push(
          ['Thống kê theo nhân viên'],
          ['Nhân viên', 'Số lượng công việc'],
          ...Object.entries(employeeStats).map(([employee, count]) => [employee, count])
        );
        
        const statsWs = XLSX.utils.aoa_to_sheet(statsData);
        XLSX.utils.book_append_sheet(wb, statsWs, 'Thống kê');
      }
      
      // Generate Excel file
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // Save file
      saveAs(blob, `${fileName}_${new Date().toISOString().split('T')[0]}.xlsx`);
      
      toast.success('Xuất file Excel thành công!', { autoClose: 3000 });
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('Có lỗi khi xuất file Excel: ' + error.message, { autoClose: 3000 });
    }
  };
  
  // Hàm chuyển đổi chuỗi tiếng Việt sang không dấu
  const removeVietnameseTones = (str) => {
    if (!str) return '';
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
    str = str.replace(/Đ/g, "D");
    return str;
  }

  const exportToPDF = (data, fileName = 'employee_reports') => {
    try {
      if (!data || data.length === 0) {
        toast.warning('Không có dữ liệu để xuất báo cáo', { autoClose: 3000 });
        return;
      }
      
      console.log('Exporting data to PDF:', data);
      
      // Create new PDF document
      const doc = new jsPDF('landscape');
      
      // Xác định tiêu đề báo cáo dựa trên loại báo cáo
      let reportTitle = 'Báo cáo công việc';
      switch (exportOptions.reportType) {
        case 'manage':
          reportTitle = 'Báo cáo công việc nhân viên';
          break;
        case 'all-tasks':
          reportTitle = 'Danh sách tất cả công việc';
          break;
        case 'assigned':
          reportTitle = 'Công việc được giao từ cấp trên';
          break;
        case 'draft':
          reportTitle = 'Báo cáo công việc nháp';
          break;
      }
      
      // Add title
      doc.setFontSize(18);
      doc.text(removeVietnameseTones(reportTitle), 14, 22);
      
      // Add date
      doc.setFontSize(12);
      doc.text(removeVietnameseTones(`Ngay xuat bao cao: ${formatDate(new Date())}`), 14, 30);
      
      // Add filter information if any
      let filterText = 'Bo loc: ';
      if (exportOptions.selectedEmployees && exportOptions.selectedEmployees.length > 0) {
        const employeeNames = exportOptions.selectedEmployees.map(id => {
          const user = uniqueUsers.find(u => u._id === id);
          return user ? removeVietnameseTones(user.fullName) : id;
        }).join(', ');
        filterText += `Nhan vien: ${employeeNames}, `;
      }
      
      if (exportOptions.selectedProjects && exportOptions.selectedProjects.length > 0) {
        filterText += `Du an: ${exportOptions.selectedProjects.map(p => removeVietnameseTones(p)).join(', ')}, `;
      }
      
      if (exportOptions.selectedStatuses && exportOptions.selectedStatuses.length > 0) {
        filterText += `Trang thai: ${exportOptions.selectedStatuses.map(s => removeVietnameseTones(getStatusText(s))).join(', ')}, `;
      }
      
      if (exportOptions.dateRange === 'custom') {
        if (exportOptions.customDateFrom) {
          filterText += `Tu ngay: ${formatDate(exportOptions.customDateFrom)}, `;
        }
        if (exportOptions.customDateTo) {
          filterText += `Den ngay: ${formatDate(exportOptions.customDateTo)}, `;
        }
      } else if (exportOptions.dateRange !== 'all') {
        filterText += `Thoi gian: ${
          exportOptions.dateRange === 'thisWeek' ? 'Tuan nay' :
          exportOptions.dateRange === 'thisMonth' ? 'Thang nay' :
          exportOptions.dateRange === 'lastMonth' ? 'Thang truoc' : 'Tat ca'
        }, `;
      }
      
      if (filterText !== 'Bo loc: ') {
        doc.text(removeVietnameseTones(filterText.slice(0, -2)), 14, 38);
      }
      
      // Xác định cấu trúc bảng tổng quan dựa trên loại báo cáo
      let summaryTableColumn = [];
      let summaryTableRows = [];
      
      // Tùy chỉnh cấu trúc bảng tổng quan theo loại báo cáo
      switch (exportOptions.reportType) {
        case 'all-tasks':
        case 'assigned':
          // Bảng tổng quan cho công việc
          summaryTableColumn = [
            { header: removeVietnameseTones('STT'), dataKey: 'stt' },
            { header: removeVietnameseTones('Nhan vien'), dataKey: 'employee' },
            { header: removeVietnameseTones('Du an'), dataKey: 'project' },
            { header: removeVietnameseTones('Noi dung'), dataKey: 'content' },
            { header: removeVietnameseTones('Ngay'), dataKey: 'date' },
            { header: removeVietnameseTones('Trang thai'), dataKey: 'status' }
          ];
          
          summaryTableRows = data.map((report, index) => {
            const task = report.tasks[0]; // Mỗi báo cáo chỉ có 1 task trong trường hợp này
            return {
              stt: index + 1,
              employee: removeVietnameseTones(report.user?.fullName || 'N/A'),
              project: removeVietnameseTones(task.project || 'N/A'),
              content: removeVietnameseTones(task.content || 'N/A'),
              date: formatDate(task.date || report.weekStart),
              status: removeVietnameseTones(getStatusText(task.status))
            };
          });
          break;
          
        default:
          // Bảng tổng quan cho báo cáo
          summaryTableColumn = [
            { header: removeVietnameseTones('STT'), dataKey: 'stt' },
            { header: removeVietnameseTones('Nhan vien'), dataKey: 'employee' },
            { header: removeVietnameseTones('Tuan bat dau'), dataKey: 'weekStart' },
            { header: removeVietnameseTones('So cong viec'), dataKey: 'taskCount' },
            { header: removeVietnameseTones('Trang thai'), dataKey: 'status' }
          ];
          
          summaryTableRows = data.map((report, index) => ({
            stt: index + 1,
            employee: removeVietnameseTones(report.user?.fullName || 'N/A'),
            weekStart: formatDate(report.weekStart),
            taskCount: report.tasks.length,
            status: removeVietnameseTones(getStatusText(report.approvalStatus || report.status))
          }));
      }
      
      // Hàm chuyển đổi mã trạng thái thành văn bản hiển thị
      function getStatusText(status) {
        switch (status) {
          case 'pending': return 'Chờ xác nhận';
          case 'approved': return 'Đã duyệt';
          case 'paused': return 'Tạm dừng';
          case 'draft': return 'Bản nháp';
          case 'completed': return 'Hoàn thành';
          case 'ongoing': return 'Đang thực hiện';
          case 'accepted': return 'Đã nhận';
          default: return status || 'N/A';
        }
      }
      
      // Vẽ bảng tổng quan
      const autoTable = require('jspdf-autotable').default;
      autoTable(doc, {
        startY: 45,
        head: [summaryTableColumn.map(col => col.header)],
        body: summaryTableRows.map(row => 
          summaryTableColumn.map(col => row[col.dataKey])
        ),
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { overflow: 'linebreak', cellWidth: 'auto' },
        columnStyles: { 0: { cellWidth: 10 } }
      });
      
      // Add detailed report for each employee/task
      let yPos = 45;
      // Kiểm tra xem bảng tổng quan đã được tạo chưa
      if (doc.lastAutoTable) {
        yPos = doc.lastAutoTable.finalY + 15;
      }
      
      // Xử lý chi tiết khác nhau cho từng loại báo cáo
      if (['all-tasks', 'assigned'].includes(exportOptions.reportType)) {
        // Chi tiết cho danh sách công việc
        data.forEach((report, reportIndex) => {
          const task = report.tasks[0];
          
          // Check if we need a new page
          if (yPos > 180) {
            doc.addPage();
            yPos = 20;
          }
          
          // Add task header
          doc.setFontSize(14);
          doc.text(removeVietnameseTones(`${reportIndex + 1}. Chi tiet cong viec:`), 14, yPos);
          yPos += 10;
          
          // Add task details
          doc.setFontSize(12);
          const taskDetails = [
            removeVietnameseTones(`Nhan vien: ${report.user?.fullName || 'N/A'}`),
            removeVietnameseTones(`Du an: ${task.project || 'N/A'}`),
            removeVietnameseTones(`Noi dung: ${task.content || 'N/A'}`),
            removeVietnameseTones(`Ngay: ${formatDate(task.date || report.weekStart)}`),
            removeVietnameseTones(`Trang thai: ${getStatusText(task.status)}`),
          ];
          
          if (task.progress) {
            taskDetails.push(removeVietnameseTones(`Tien do: ${task.progress}`));
          }
          
          if (exportOptions.reportType === 'assigned') {
            taskDetails.push(removeVietnameseTones(`Han chot: ${formatDate(report.weekStart)}`));
          }
          
          taskDetails.forEach((detail, idx) => {
            doc.text(detail, 20, yPos + (idx * 8));
          });
          
          yPos += (taskDetails.length * 8) + 15;
        });
      } else {
        // Chi tiết cho báo cáo công việc
        data.forEach((report, reportIndex) => {
          // Check if we need a new page
          if (yPos > 180) {
            doc.addPage();
            yPos = 20;
          }
          
          // Add employee name as section header
          doc.setFontSize(14);
          doc.text(removeVietnameseTones(`${reportIndex + 1}. Bao cao cua: ${report.user?.fullName || 'N/A'}`), 14, yPos);
          
          // Add tasks table
          const taskTableColumn = [
            { header: removeVietnameseTones('STT'), dataKey: 'stt' },
            { header: removeVietnameseTones('Ngay'), dataKey: 'date' },
            { header: removeVietnameseTones('Du an'), dataKey: 'project' },
            { header: removeVietnameseTones('Noi dung'), dataKey: 'content' },
            { header: removeVietnameseTones('Tien do'), dataKey: 'progress' },
            { header: removeVietnameseTones('Hoan thanh'), dataKey: 'completion' },
            { header: removeVietnameseTones('Trang thai'), dataKey: 'status' }
          ];
          
          const taskTableRows = report.tasks.map((task, index) => ({
            stt: index + 1,
            date: formatDate(task.date),
            project: removeVietnameseTones(task.project || 'N/A'),
            content: removeVietnameseTones(task.content || 'N/A'),
            progress: removeVietnameseTones(task.progress || 'N/A'),
            completion: `${task.completionPercentage || 0}%`,
            status: removeVietnameseTones(getStatusText(task.status))
          }));
          
          autoTable(doc, {
            startY: yPos + 5,
            head: [taskTableColumn.map(col => col.header)],
            body: taskTableRows.map(row => [
              row.stt,
              row.date,
              row.project,
              row.content,
              row.progress,
              row.completion,
              row.status
            ]),
            theme: 'grid',
            headStyles: { fillColor: [52, 152, 219], textColor: 255 },
            styles: { overflow: 'linebreak', cellWidth: 'auto' },
            columnStyles: { 
              0: { cellWidth: 10 },
              3: { cellWidth: 'auto' }
            }
          });
          
          if (doc.lastAutoTable) {
            yPos = doc.lastAutoTable.finalY + 15;
          } else {
            yPos += 15;
          }
          
          // Add comments if enabled
          if (exportOptions.includeComments && report.comments && report.comments.length > 0) {
            // Check if we need a new page
            if (yPos > 180) {
              doc.addPage();
              yPos = 20;
            }
            
            doc.setFontSize(12);
            doc.text(removeVietnameseTones('Binh luan:'), 14, yPos);
            yPos += 10;
            
            report.comments.forEach((comment, idx) => {
              const commentText = removeVietnameseTones(`${idx + 1}. ${comment.user?.fullName || 'N/A'} (${formatDate(comment.createdAt)}): ${comment.content}`);
              
              // Wrap long comments
              const textLines = doc.splitTextToSize(commentText, 260);
              doc.text(textLines, 14, yPos);
              yPos += 6 * textLines.length;
              
              // Check if we need a new page
              if (yPos > 280) {
                doc.addPage();
                yPos = 20;
              }
            });
          }
        });
      }
      
      // Add statistics page if there are multiple reports
      if (data.length > 1) {
        doc.addPage();
        
        doc.setFontSize(16);
        doc.text(removeVietnameseTones('Thong ke bao cao'), 14, 20);
        
        // Thống kê theo trạng thái
        const statusStats = {};
        data.forEach(report => {
          let status;
          
          if (['all-tasks', 'assigned'].includes(exportOptions.reportType)) {
            status = removeVietnameseTones(getStatusText(report.tasks[0].status));
          } else {
            status = removeVietnameseTones(getStatusText(report.approvalStatus || report.status));
          }
          
          if (!statusStats[status]) {
            statusStats[status] = 0;
          }
          statusStats[status]++;
        });
        
        doc.setFontSize(14);
        doc.text(removeVietnameseTones('Thong ke theo trang thai'), 14, 30);
        
        autoTable(doc, {
          startY: 35,
          head: [[removeVietnameseTones('Trang thai'), removeVietnameseTones('So luong')]],
          body: Object.entries(statusStats).map(([status, count]) => [status, count]),
          theme: 'grid',
          headStyles: { fillColor: [41, 128, 185], textColor: 255 },
          styles: { overflow: 'linebreak' }
        });
        
        // Thống kê theo dự án
        const projectStats = {};
        data.forEach(report => {
          if (['all-tasks', 'assigned'].includes(exportOptions.reportType)) {
            const task = report.tasks[0];
            const project = removeVietnameseTones(task.project || 'N/A');
            if (!projectStats[project]) {
              projectStats[project] = 0;
            }
            projectStats[project]++;
          } else {
            report.tasks.forEach(task => {
              const project = removeVietnameseTones(task.project || 'N/A');
              if (!projectStats[project]) {
                projectStats[project] = 0;
              }
              projectStats[project]++;
            });
          }
        });
        
        doc.setFontSize(14);
        let statsYPos = 50;
        if (doc.lastAutoTable) {
          statsYPos = doc.lastAutoTable.finalY + 15;
        }
        doc.text(removeVietnameseTones('Thong ke theo du an'), 14, statsYPos);
        
        autoTable(doc, {
          startY: statsYPos + 5,
          head: [[removeVietnameseTones('Du an'), removeVietnameseTones('So luong cong viec')]],
          body: Object.entries(projectStats).map(([project, count]) => [project, count]),
          theme: 'grid',
          headStyles: { fillColor: [41, 128, 185], textColor: 255 },
          styles: { overflow: 'linebreak' }
        });
        
        // Thống kê theo nhân viên
        if (!['my-reports'].includes(exportOptions.reportType)) {
          const employeeStats = {};
          data.forEach(report => {
            const employeeName = removeVietnameseTones(report.user?.fullName || 'N/A');
            if (!employeeStats[employeeName]) {
              employeeStats[employeeName] = 0;
            }
            
            if (['all-tasks', 'assigned'].includes(exportOptions.reportType)) {
              employeeStats[employeeName]++;
            } else {
              employeeStats[employeeName] += report.tasks.length;
            }
          });
          
          doc.setFontSize(14);
          let empStatsYPos = statsYPos + 30;
          if (doc.lastAutoTable) {
            empStatsYPos = doc.lastAutoTable.finalY + 15;
          }
          doc.text(removeVietnameseTones('Thong ke theo nhan vien'), 14, empStatsYPos);
          
          autoTable(doc, {
            startY: empStatsYPos + 5,
            head: [[removeVietnameseTones('Nhan vien'), removeVietnameseTones('So luong cong viec')]],
            body: Object.entries(employeeStats).map(([employee, count]) => [employee, count]),
            theme: 'grid',
            headStyles: { fillColor: [41, 128, 185], textColor: 255 },
            styles: { overflow: 'linebreak' }
          });
        }
      }
      
      // Thêm hình ảnh nếu được chọn
      if (exportOptions.includeImages) {
        let hasImages = false;
        
        // Kiểm tra xem có hình ảnh nào không
        data.forEach(report => {
          if (['all-tasks', 'assigned'].includes(exportOptions.reportType)) {
            const task = report.tasks[0];
            if (task.images && task.images.length > 0) {
              hasImages = true;
            }
          } else {
            report.tasks.forEach(task => {
              if (task.images && task.images.length > 0) {
                hasImages = true;
              }
            });
          }
        });
        
        if (hasImages) {
          doc.addPage();
          doc.setFontSize(16);
          doc.text('Hình ảnh đính kèm', 14, 20);
          
          let yPos = 30;
          
          // Thêm hình ảnh từ các báo cáo
          data.forEach((report, reportIndex) => {
            if (['all-tasks', 'assigned'].includes(exportOptions.reportType)) {
              const task = report.tasks[0];
              if (task.images && task.images.length > 0) {
                // Thêm tiêu đề cho hình ảnh
                if (yPos > 180) {
                  doc.addPage();
                  yPos = 20;
                }
                
                doc.setFontSize(14);
                doc.text(`Hình ảnh cho công việc: ${task.content || 'N/A'}`, 14, yPos);
                yPos += 10;
                
                // Thêm các hình ảnh
                task.images.forEach((imageUrl, imageIndex) => {
                  try {
                    // Chỉ thêm hình ảnh nếu URL hợp lệ
                    if (imageUrl && imageUrl.startsWith('http')) {
                      // Thêm hình ảnh vào PDF
                      // Lưu ý: Đây chỉ là mô phỏng, thực tế cần xử lý bất đồng bộ để tải hình ảnh
                      doc.text(`[Hình ảnh ${imageIndex + 1}]: ${imageUrl}`, 20, yPos);
                      yPos += 10;
                      
                      if (yPos > 280) {
                        doc.addPage();
                        yPos = 20;
                      }
                    }
                  } catch (imgError) {
                    console.error('Error adding image to PDF:', imgError);
                  }
                });
                
                yPos += 10;
              }
            } else {
              // Thêm tiêu đề cho báo cáo
              if (yPos > 180) {
                doc.addPage();
                yPos = 20;
              }
              
              doc.setFontSize(14);
              doc.text(`Hình ảnh cho báo cáo của: ${report.user?.fullName || 'N/A'}`, 14, yPos);
              yPos += 10;
              
              // Duyệt qua từng task để tìm hình ảnh
              report.tasks.forEach((task, taskIndex) => {
                if (task.images && task.images.length > 0) {
                  doc.setFontSize(12);
                  doc.text(`Công việc ${taskIndex + 1}: ${task.content || 'N/A'}`, 20, yPos);
                  yPos += 8;
                  
                  // Thêm các hình ảnh
                  task.images.forEach((imageUrl, imageIndex) => {
                    try {
                      // Chỉ thêm hình ảnh nếu URL hợp lệ
                      if (imageUrl && imageUrl.startsWith('http')) {
                        // Thêm hình ảnh vào PDF
                        doc.text(`[Hình ảnh ${imageIndex + 1}]: ${imageUrl}`, 25, yPos);
                        yPos += 10;
                        
                        if (yPos > 280) {
                          doc.addPage();
                          yPos = 20;
                        }
                      }
                    } catch (imgError) {
                      console.error('Error adding image to PDF:', imgError);
                    }
                  });
                  
                  yPos += 5;
                }
              });
              
              yPos += 10;
            }
          });
        }
      }
      
      // Save PDF
      doc.save(`${fileName}_${new Date().toISOString().split('T')[0]}.pdf`);
      
      toast.success('Xuất file PDF thành công!', { autoClose: 3000 });
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast.error('Có lỗi khi xuất file PDF', { autoClose: 3000 });
    }
  };
  
  // Handle manager task filter change
  const handleManagerTaskFilterChange = (e) => {
    const { name, value } = e.target;
    const newFilter = { ...managerTaskFilter, [name]: value };
    setManagerTaskFilter(newFilter);
    filterAndPaginateManagerTasks(newFilter, 1);
    setManagerAssignedCurrentPage(1);
  };
  
  // Function to paginate assigned tasks
  const paginateAssignedTasks = useCallback((page) => {
    // Sắp xếp theo thời gian tạo mới nhất lên đầu
    const sortedTasks = [...assignedTasks].sort((a, b) => {
      const dateA = new Date(a.createdAt || 0);
      const dateB = new Date(b.createdAt || 0);
      return dateB - dateA; // Sắp xếp giảm dần (mới nhất lên đầu)
    });
    
    const startIndex = (page - 1) * reportsPerPage;
    const endIndex = startIndex + reportsPerPage;
    setPaginatedAssignedTasks(sortedTasks.slice(startIndex, endIndex));
    setAssignedTotalPages(Math.ceil(sortedTasks.length / reportsPerPage));
  }, [assignedTasks, reportsPerPage]);
  
  // Function to paginate draft reports
  const paginateDraftReports = useCallback((page) => {
    // Sắp xếp theo thời gian tạo mới nhất lên đầu
    const sortedDrafts = [...draftReports].sort((a, b) => {
      const dateA = new Date(a.updatedAt || a.createdAt || 0);
      const dateB = new Date(b.updatedAt || b.createdAt || 0);
      return dateB - dateA; // Sắp xếp giảm dần (mới nhất lên đầu)
    });
    
    const startIndex = (page - 1) * reportsPerPage;
    const endIndex = startIndex + reportsPerPage;
    setPaginatedDraftReports(sortedDrafts.slice(startIndex, endIndex));
    setDraftTotalPages(Math.ceil(sortedDrafts.length / reportsPerPage));
  }, [draftReports, reportsPerPage]);
  
  // Function to filter and paginate manager assigned tasks
  const filterAndPaginateManagerTasks = useCallback((filter, page = 1) => {
    let filtered = [...managerAssignedTasks];
    
    // Apply filters
    if (filter.employeeId) {
      filtered = filtered.filter(task => 
        String(task.employeeId?._id) === String(filter.employeeId)
      );
    }
    
    if (filter.project) {
      filtered = filtered.filter(task => 
        task.project?.toLowerCase().includes(filter.project.toLowerCase())
      );
    }
    
    if (filter.status) {
      filtered = filtered.filter(task => task.status === filter.status);
    }
    
    // Sắp xếp theo thời gian tạo mới nhất lên đầu
    filtered.sort((a, b) => {
      const dateA = new Date(a.createdAt || 0);
      const dateB = new Date(b.createdAt || 0);
      return dateB - dateA; // Sắp xếp giảm dần (mới nhất lên đầu)
    });
    
    // Update pagination
    const totalFiltered = filtered.length;
    const totalPages = Math.ceil(totalFiltered / reportsPerPage) || 1;
    setManagerAssignedTotalPages(totalPages);
    
    if (page > totalPages) {
      setManagerAssignedCurrentPage(1);
      page = 1;
    }
    
    // Slice for current page
    const startIndex = (page - 1) * reportsPerPage;
    const endIndex = startIndex + reportsPerPage;
    setPaginatedManagerAssignedTasks(filtered.slice(startIndex, endIndex));
    
    return filtered;
  }, [managerAssignedTasks, reportsPerPage]);
  
  // Function to paginate manager assigned tasks
  const paginateManagerAssignedTasks = useCallback((page) => {
    filterAndPaginateManagerTasks(managerTaskFilter, page);
  }, [filterAndPaginateManagerTasks, managerTaskFilter]);
  
  const fetchManagers = async () => {
    try {
      setLoadingManagers(true);
      const res = await API.get('/workReports/users/managers');
      setManagers(Array.isArray(res.data) ? res.data : []);
    } catch (error) {
      console.error('Error fetching managers:', error);
      toast.error('Lỗi khi tải danh sách quản lý', { autoClose: 3000 });
    } finally {
      setLoadingManagers(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      setLoadingEmployees(true);
      // Sử dụng endpoint collaborators để lấy tất cả user (bao gồm cả ADMIN)
      const collaboratorsRes = await API.get('/workReports/users/collaborators');
      const collaboratorsList = Array.isArray(collaboratorsRes.data) ? collaboratorsRes.data : [];
      
      // Fallback: nếu endpoint collaborators không hoạt động, sử dụng cách cũ nhưng bao gồm ADMIN
      if (collaboratorsList.length === 0) {
        const employeesRes = await API.get('/workReports/users/employees');
        const employeesList = Array.isArray(employeesRes.data) ? employeesRes.data : [];
        const managersRes = await API.get('/workReports/users/managers');
        const managersList = Array.isArray(managersRes.data) ? managersRes.data : [];
        const allUsersMap = new Map();
        
        // Bao gồm tất cả employees (không loại trừ ADMIN)
        employeesList.forEach(emp => {
          if (String(emp._id) !== String(user._id || user.id)) { // Chỉ loại trừ chính user hiện tại
            allUsersMap.set(emp._id, emp);
          }
        });
        
        // Bao gồm tất cả managers (không loại trừ ADMIN)
        managersList.forEach(manager => {
          if (String(manager._id) !== String(user._id || user.id)) { // Chỉ loại trừ chính user hiện tại
            allUsersMap.set(manager._id, manager);
          }
        });
        
        const combinedUsers = Array.from(allUsersMap.values());
        setEmployees(combinedUsers);
      } else {
        setEmployees(collaboratorsList);
      }
    } catch (error) {
      console.error('Error fetching employees and managers:', error);
      toast.error('Lỗi khi tải danh sách người dùng', { autoClose: 3000 });
    } finally {
      setLoadingEmployees(false);
    }
  };

  // Hàm fetch employees cho user thường (để chọn collaborator)
  const fetchEmployeesForCollaborator = async () => {
    try {
      setLoadingEmployees(true);
      // Sử dụng endpoint mới để lấy danh sách collaborator
      const collaboratorsRes = await API.get('/workReports/users/collaborators');
      const collaboratorsList = Array.isArray(collaboratorsRes.data) ? collaboratorsRes.data : [];
      setEmployees(collaboratorsList);
    } catch (error) {
      console.log('Error fetching collaborator list:', error);
      setEmployees([]);
    } finally {
      setLoadingEmployees(false);
    }
  };

  const fetchAssignedTasks = async () => {
    try {
      const res = await API.get('/workReports/assigned-tasks', { params: { userId: user._id || user.id } });
      setAssignedTasks(Array.isArray(res.data) ? res.data : []);
    } catch (error) {
      console.error('Error fetching assigned tasks:', error);
      toast.error('Lỗi khi tải công việc được giao', { autoClose: 3000 });
    }
  };

  // Hàm để lấy danh sách nhân viên đã được giao việc
  const getAssignedEmployees = (tasks) => {
    const uniqueEmployees = new Map();
    
    tasks.forEach(task => {
      if (task.employeeId && task.employeeId._id) {
        uniqueEmployees.set(task.employeeId._id, {
          _id: task.employeeId._id,
          fullName: task.employeeId.fullName
        });
      }
    });
    
    return Array.from(uniqueEmployees.values());
  };

  const fetchManagerAssignedTasks = async () => {
    try {
      const res = await API.get('/workReports/assigned-tasks/manager');
      const tasks = Array.isArray(res.data) ? res.data : [];
      setManagerAssignedTasks(tasks);
      
      // Apply initial filtering and pagination
      filterAndPaginateManagerTasks(managerTaskFilter, 1);
    } catch (error) {
      console.error('Error fetching manager assigned tasks:', error);
      toast.error('Lỗi khi tải danh sách công việc đã giao', { autoClose: 3000 });
    }
  };

  // Function to extract unique values from reports data
  const extractUniqueValues = (data) => {
    // Extract unique projects
    const projects = new Set();
    // Extract unique statuses
    const statuses = new Set(['ongoing', 'paused', 'completed']);
    // Extract unique users
    const users = new Map();
    
    data.forEach(report => {
      // Extract from report tasks
      if (report.tasks && Array.isArray(report.tasks)) {
        report.tasks.forEach(task => {
          if (task.project) projects.add(task.project);
          if (task.status) statuses.add(task.status);
        });
      }
      
      // Add user info
      if (report.user) {
        const userId = report.user._id || report.user;
        const userName = report.user.fullName || 'Không xác định';
        users.set(userId, { _id: userId, fullName: userName });
      }
    });
    
    return {
      projects: Array.from(projects).sort(),
      statuses: Array.from(statuses).sort(),
      users: Array.from(users.values()).sort((a, b) => a.fullName.localeCompare(b.fullName))
    };
  };

  const fetchReports = async () => {
    try {
      let endpoint;
      if (canManageReports && activeTab === 'manage') {
        endpoint = '/workReports/manager';
      } else {
        endpoint = '/workReports/my-reports';
      }
      const res = await API.get(endpoint, { params: { userId: user._id || user.id } });
      const reportsData = res.data && res.data.reports ? res.data.reports : [];
      
      console.log('Fetched reports data:', reportsData);
      console.log('Sample report managers:', reportsData[0]?.managers);
  
      reportsData.sort((a, b) => new Date(b.weekStart) - new Date(a.weekStart));
  
      // Extract unique values for filters
      const { projects, statuses, users } = extractUniqueValues(reportsData);
      setUniqueProjects(projects);
      setUniqueStatuses(statuses);
      setUniqueUsers(users);
      
      setAllReports(reportsData);
      if (activeTab === 'my-reports') {
        setFilteredReports(reportsData);
        setTotalPages(Math.ceil(reportsData.length / reportsPerPage));
      } else {
        applyFilterAndPagination(searchData, reportsData, currentPage);
      }
      
      // Fetch user's projects for dropdown
      if (user?.id) {
        setTimeout(() => {
          loadUserProjectsFromAPI();
        }, 200);
      }
    } catch (error) {
      console.error('Error fetching reports:', error);
      toast.error('Lỗi khi tải báo cáo công việc', { autoClose: 3000 });
    }
  };

  // Function để load dự án từ API
  const loadUserProjectsFromAPI = async () => {
    try {
      console.log('Loading user projects from API for user:', user?.id);
      const response = await API.get('/workReports/user-projects');
      console.log('API Response:', response);
      console.log('Loaded projects from API:', response.data);
      setUserProjects(response.data);
    } catch (error) {
      console.error('Error loading user projects from API:', error);
      console.error('Error details:', error.response?.data);
      
      // Fallback: tạo danh sách mẫu
      const sampleProjects = [
        'Dự án Website',
        'Dự án Mobile App', 
        'Dự án ERP',
        'Dự án CRM',
        'Dự án Marketing'
      ];
      console.log('Creating sample projects:', sampleProjects);
      setUserProjects(sampleProjects);
      
      // Thêm các dự án mẫu vào database
      for (const project of sampleProjects) {
        try {
          console.log('Adding sample project:', project);
          const addResponse = await API.post('/workReports/user-projects', { projectName: project });
          console.log('Added project response:', addResponse.data);
        } catch (addError) {
          console.error('Error adding sample project:', project, addError.response?.data);
        }
      }
    }
  };

  // Function để thêm dự án mới vào danh sách
  const addProjectToUserList = async (newProject) => {
    if (!newProject || !newProject.trim()) {
      console.log('Invalid project name:', newProject);
      return;
    }
    
    const trimmedProject = newProject.trim();
    console.log('Adding project:', trimmedProject);
    console.log('Current projects:', userProjects);
    
    if (userProjects.includes(trimmedProject)) {
      console.log('Project already exists:', trimmedProject);
      return;
    }

    try {
      console.log('Sending API request to add project:', trimmedProject);
      const response = await API.post('/workReports/user-projects', { projectName: trimmedProject });
      console.log('Add project API response:', response.data);
      
      const updatedProjects = [...userProjects, trimmedProject].sort();
      setUserProjects(updatedProjects);
      console.log('Updated projects list:', updatedProjects);
      toast.success('Thêm dự án thành công');
    } catch (error) {
      console.error('Error adding project to API:', error);
      console.error('Error response:', error.response?.data);
      toast.error('Lỗi khi thêm dự án: ' + (error.response?.data?.error || error.message));
    }
  };

  // Function để xóa dự án khỏi danh sách
  const removeProjectFromUserList = async (projectToRemove) => {
    try {
      await API.delete(`/workReports/user-projects/${encodeURIComponent(projectToRemove)}`);
      const updatedProjects = userProjects.filter(project => project !== projectToRemove);
      setUserProjects(updatedProjects);
      console.log('Removed project:', projectToRemove);
    } catch (error) {
      console.error('Error removing project from API:', error);
      toast.error('Lỗi khi xóa dự án');
    }
  };

  // Function để cập nhật thời gian sử dụng dự án
  const updateProjectUsage = async (projectName) => {
    try {
      await API.put(`/workReports/user-projects/${encodeURIComponent(projectName)}/use`);
    } catch (error) {
      console.error('Error updating project usage:', error);
    }
  };


  
    const extractUniqueTaskValues = (data) => {
      const users = new Map();
      data.forEach(task => {
        if (task.report && task.report.user) {
          const userId = task.report.user._id || task.report.user;
          const userName = task.report.user.fullName || 'Không xác định';
          const userRole = task.report.user.role || ''; // Kiểm tra vai trò của người dùng
          if (userRole !== 'SUPER_ADMIN') { // Loại bỏ SUPER_ADMIN nếu không cần
            users.set(userId, { _id: userId, fullName: userName });
          }
        }
      });
      return {
        projects: Array.from(new Set(data.map(task => task.project).filter(Boolean))).sort(),
        statuses: Array.from(new Set(data.map(task => task.status).filter(Boolean))).sort(),
        users: Array.from(users.values()).sort((a, b) => a.fullName.localeCompare(b.fullName))
      };
    };

  const fetchAllTasks = async () => {
    try {
      const res = await API.get('/workReports/all-tasks', { 
        params: { 
          userId: user._id || user.id 
        } 
      });
      let tasksData = res.data && res.data.tasks ? res.data.tasks : [];
      
      // Nếu là REPORT_MANAGER, chỉ hiển thị công việc của nhân viên đã chọn họ làm người quản lý
      if (isReportManager && !isAdminOrSuper) {
        console.log('Filtering tasks for REPORT_MANAGER');
        tasksData = tasksData.filter(task => {
          // Kiểm tra nếu task có report và report có managers
          if (task.report && task.report.managers && Array.isArray(task.report.managers)) {
            // Kiểm tra xem manager IDs có chứa ID của người dùng hiện tại không
            return task.report.managers.some(manager => {
              const managerId = typeof manager === 'object' ? manager._id : manager;
              return String(managerId) === String(user._id || user.id);
            });
          }
          return false; // Nếu không có thông tin managers, không hiển thị
        });
      }
      
      // Sắp xếp công việc theo ngày mới nhất
      tasksData.sort((a, b) => new Date(b.date) - new Date(a.date));
      
      // Extract unique values for filters
      const { projects, statuses, users } = extractUniqueTaskValues(tasksData);
      setUniqueProjects(projects);
      setUniqueStatuses(statuses);
      setUniqueUsers(users);
      
      setAllTasks(tasksData);
      applyTaskFilterAndPagination(taskSearchData, tasksData, taskCurrentPage);
    } catch (error) {
      console.error('Error fetching all tasks:', error);
      toast.error('Lỗi khi tải danh sách công việc', { autoClose: 3000 });
    }
  };

  
  const fetchStats = async () => {
    try {
      setLoadingStats(true);
      const res = await API.get('/workReports/stats/manager', { params: { userId: user._id || user.id } });
      setStats(Array.isArray(res.data) ? res.data : []);
    } catch (error) {
      console.error('Error fetching stats:', error);
      toast.error('Lỗi khi tải thống kê', { autoClose: 5000 });
    } finally {
      setLoadingStats(false);
    }
  };

  const fetchDraftReports = async () => {
    try {
      const res = await API.get('/workReports/drafts');
      setDraftReports(res.data.reports || []);
    } catch (error) {
      console.error('Error fetching draft reports:', error);
      toast.error('Lỗi khi tải danh sách Lưu nháp', { autoClose: 3000 });
    }
  };
  
  useEffect(() => {
    if (activeTab === 'draft') {
      fetchDraftReports();
    }
  }, [activeTab]);

  // Functions cho bình luận công việc
  const fetchTaskComments = async (taskId) => {
    try {
      setLoadingTaskComments(true);
      const res = await API.get(`/workReports/task-comments/${taskId}`);
      setTaskComments(res.data.comments || []);
    } catch (error) {
      console.error('Error fetching task comments:', error);
      toast.error('Lỗi khi tải bình luận', { autoClose: 3000 });
    } finally {
      setLoadingTaskComments(false);
    }
  };

  const submitTaskComment = async () => {
    if (!newTaskComment.trim() || !selectedTask) return;
    
    try {
      setSubmittingTaskComment(true);
      const res = await API.post(`/workReports/task-comments/${selectedTask._id}`, {
        content: newTaskComment.trim()
      });
      
      // Thêm comment mới vào danh sách
      setTaskComments(prev => [...prev, res.data.comment]);
      setNewTaskComment('');
      toast.success('Đã thêm bình luận. Thông báo đã được gửi đến tất cả người liên quan.', { autoClose: 3000 });
    } catch (error) {
      console.error('Error submitting task comment:', error);
      toast.error('Lỗi khi thêm bình luận', { autoClose: 3000 });
    } finally {
      setSubmittingTaskComment(false);
    }
  };

  const deleteTaskComment = async (commentId) => {
    try {
      await API.delete(`/workReports/task-comments/delete/${commentId}`);
      setTaskComments(prev => prev.filter(comment => comment._id !== commentId));
      toast.success('Đã xóa bình luận', { autoClose: 2000 });
    } catch (error) {
      console.error('Error deleting task comment:', error);
      toast.error('Lỗi khi xóa bình luận', { autoClose: 3000 });
    }
  };

  // TEST FUNCTION - Gửi thông báo test
  const sendTestNotification = async () => {
    try {
      const response = await API.post('/workReports/test-notification');
      toast.success('Đã gửi thông báo test!', { autoClose: 3000 });
      console.log('Test notification response:', response.data);
    } catch (error) {
      console.error('Error sending test notification:', error);
      toast.error('Lỗi khi gửi thông báo test', { autoClose: 3000 });
    }
  };

  // Kiểm tra quyền bình luận
  const canCommentOnTask = (task) => {
    if (!user || !task) {
      console.log('canCommentOnTask: No user or task');
      return false;
    }
    
    // Người tạo báo cáo có thể bình luận
    const isReportOwner = task.report?.user?._id === user.id || task.report?.user?._id === user._id;
    
    // Cấp quản lý có thể bình luận
    const isManager = canManageReports;
    
    // Cá nhân phối hợp có thể bình luận
    const isCollaborator = task.collaborator && Array.isArray(task.collaborator) && 
      task.collaborator.some(collab => {
        const collabId = typeof collab === 'object' ? (collab._id || collab.id) : collab;
        return String(collabId) === String(user.id) || String(collabId) === String(user._id);
      });
    
    console.log('canCommentOnTask check:', {
      userId: user.id || user._id,
      taskId: task._id,
      reportOwnerId: task.report?.user?._id,
      isReportOwner,
      isManager,
      isCollaborator,
      collaborators: task.collaborator,
      canComment: isReportOwner || isManager || isCollaborator
    });
    
    return isReportOwner || isManager || isCollaborator;
  };

  // Load comments khi mở modal và reset khi đóng
  useEffect(() => {
    if (showTaskModal && selectedTask) {
      fetchTaskComments(selectedTask._id);
    } else if (!showTaskModal) {
      // Reset comments khi đóng modal
      setTaskComments([]);
      setNewTaskComment('');
    }
  }, [showTaskModal, selectedTask]);
  
  // Add support for horizontal scrolling with Shift+mouse wheel
  useEffect(() => {
    // Function to handle the wheel event
    const handleWheel = (event) => {
      // Only process if Shift key is pressed
      if (event.shiftKey) {
        // Find all table containers
        const containers = document.querySelectorAll('.task-table-container');
        
        // Process each container
        containers.forEach(container => {
          // Check if this container is in the viewport
          const rect = container.getBoundingClientRect();
          const isVisible = (
            rect.top < window.innerHeight &&
            rect.bottom > 0 &&
            rect.left < window.innerWidth &&
            rect.right > 0
          );
          
          // Only process visible containers
          if (isVisible) {
            // Prevent the default scroll behavior
            event.preventDefault();
            
            // Calculate scroll amount (use deltaY since most mice only have vertical wheels)
            const scrollAmount = event.deltaY;
            
            // Scroll horizontally instead of vertically
            container.scrollLeft += scrollAmount;
          }
        });
      }
    };
    
    // Add the event listener with the 'passive: false' option to allow preventDefault()
    document.addEventListener('wheel', handleWheel, { passive: false });
    
    // Clean up function to remove the event listener when component unmounts
    return () => {
      document.removeEventListener('wheel', handleWheel);
    };
  }, []);
  


  const openDeleteDraftModal = (draft) => {
    console.log("Open delete modal with draft:", draft);
    setDraftToDelete(draft);
    setShowDeleteDraftModal(true);
  };
  
  const handleChange = (e, index) => {
    const { name, value, type, checked } = e.target;
    if (name === 'managers') {
      if (checked) {
        const selectedManager = managers.find(manager => String(manager._id) === String(value));
        if (selectedManager && selectedManager.role === 'EMPLOYEE') {
          toast.error('Không thể chọn nhân viên làm người quản lý báo cáo', { autoClose: 3000 });
          return; 
        }
        setFormData(prev => ({
          ...prev,
          managers: [...prev.managers, String(value)]
        }));
      } else {
        setFormData(prev => ({
          ...prev,
          managers: prev.managers.filter(id => String(id) !== String(value))
        }));
      }
    } else if (index !== undefined) {
      const newTasks = [...formData.tasks];
      newTasks[index][name] = value;
      setFormData(prev => ({ ...prev, tasks: newTasks }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Hàm xử lý chọn collaborator
  const handleCollaboratorChange = (e, taskIndex) => {
    const { value, checked } = e.target;
    const newTasks = [...formData.tasks];
    
    if (checked) {
      // Thêm collaborator
      if (!newTasks[taskIndex].collaborator.includes(value)) {
        newTasks[taskIndex].collaborator = [...newTasks[taskIndex].collaborator, value];
      }
    } else {
      // Xóa collaborator
      newTasks[taskIndex].collaborator = newTasks[taskIndex].collaborator.filter(id => id !== value);
    }
    
    setFormData(prev => ({ ...prev, tasks: newTasks }));
  };

  // Hàm xử lý thêm collaborator thủ công
  const handleAddManualCollaborator = (taskIndex) => {
    const manualName = manualCollaboratorInputs[`task-${taskIndex}`]?.trim();
    if (!manualName) {
      toast.warning('Vui lòng nhập tên cá nhân phối hợp');
      return;
    }

    const newTasks = [...formData.tasks];
    
    // Kiểm tra xem tên đã tồn tại chưa
    if (newTasks[taskIndex].collaborator.includes(manualName)) {
      toast.warning('Tên này đã được thêm vào danh sách');
      return;
    }

    // Thêm tên vào danh sách collaborator
    newTasks[taskIndex].collaborator = [...newTasks[taskIndex].collaborator, manualName];
    setFormData(prev => ({ ...prev, tasks: newTasks }));

    // Xóa input
    setManualCollaboratorInputs(prev => ({
      ...prev,
      [`task-${taskIndex}`]: ''
    }));

    toast.success('Đã thêm cá nhân phối hợp thủ công');
  };

  // Hàm xử lý xóa collaborator (cả hệ thống và thủ công)
  const handleRemoveCollaborator = (taskIndex, collaboratorValue) => {
    const newTasks = [...formData.tasks];
    newTasks[taskIndex].collaborator = newTasks[taskIndex].collaborator.filter(
      collab => collab !== collaboratorValue
    );
    setFormData(prev => ({ ...prev, tasks: newTasks }));
  };

  const handleAssignTaskChange = (e) => {
    const { name, value } = e.target;
    setAssignTaskForm(prev => ({ ...prev, [name]: value }));
  };

  const handleAssignTaskSubmit = async (e) => {
    e.preventDefault();
    setButtonDisabled(true);
    try {
      const payload = { ...assignTaskForm, assignedBy: user._id || user.id };
      await API.post('/workReports/assign-task', payload);
      toast.success('Giao việc thành công. Nhân viên sẽ nhận được thông báo trên hệ thống.', { autoClose: 3000 });
      setAssignTaskForm({ employeeId: '', content: '', project: '', dueDate: '' });
      setShowAssignTaskModal(false);
      fetchAssignedTasks();
      fetchManagerAssignedTasks();
    } catch (error) {
      console.error('Error assigning task:', error);
      toast.error('Lỗi khi giao việc', { autoClose: 3000 });
    } finally {
      setButtonDisabled(false);
      navigate('/work-reports/');
    }
  };
  
  // Handler for opening edit task modal
  const handleEditTask = (task) => {
    setEditingTask(task);
    setEditTaskForm({
      employeeId: task.employeeId?._id || '',
      content: task.content || '',
      project: task.project || '',
      dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : ''
    });
    setEmployeeSearch(task.employeeId?.fullName || '');
    setShowEditTaskModal(true);
  };
  
  // Handler for edit task form changes
  const handleEditTaskChange = (e) => {
    const { name, value } = e.target;
    setEditTaskForm(prev => ({ ...prev, [name]: value }));
  };
  
  // Handler for submitting edit task form
  const handleEditTaskSubmit = async (e) => {
    e.preventDefault();
    setButtonDisabled(true);
    try {
      const payload = { ...editTaskForm };
      await API.put(`/workReports/assigned-tasks/manager/${editingTask._id}`, payload);
      toast.success('Cập nhật công việc thành công. Nhân viên sẽ nhận được thông báo.', { autoClose: 3000 });
      setEditTaskForm({ employeeId: '', content: '', project: '', dueDate: '' });
      setShowEditTaskModal(false);
      setEditingTask(null);
      fetchManagerAssignedTasks();
    } catch (error) {
      console.error('Error updating task:', error);
      toast.error('Lỗi khi cập nhật công việc', { autoClose: 3000 });
    } finally {
      setButtonDisabled(false);
    }
  };
  
  // Handler for opening delete task modal
  const handleDeleteTaskConfirm = (task) => {
    setTaskToDelete(task);
    setShowDeleteTaskModal(true);
  };
  
  // Handler for deleting a task
  const handleDeleteTask = async () => {
    setButtonDisabled(true);
    try {
      await API.delete(`/workReports/assigned-tasks/manager/${taskToDelete._id}`);
      toast.success('Xóa công việc thành công. Nhân viên sẽ nhận được thông báo.', { autoClose: 3000 });
      setShowDeleteTaskModal(false);
      setTaskToDelete(null);
      fetchManagerAssignedTasks();
    } catch (error) {
      console.error('Error deleting task:', error);
      toast.error('Lỗi khi xóa công việc', { autoClose: 3000 });
    } finally {
      setButtonDisabled(false);
    }
  };
  
  // Handler for selecting/deselecting a task
  const handleTaskSelection = (taskId) => {
    // Ensure taskId is a valid string
    if (typeof taskId !== 'string' || taskId.trim() === '') {
      console.error('Invalid task ID:', taskId);
      return;
    }
    
    setSelectedTasks(prev => {
      if (prev.includes(taskId)) {
        return prev.filter(id => id !== taskId);
      } else {
        return [...prev, taskId];
      }
    });
  };
  
  // Handler for selecting/deselecting all tasks
  const handleSelectAllTasks = () => {
    if (selectedTasks.length === paginatedManagerAssignedTasks.length) {
      // If all are selected, deselect all
      setSelectedTasks([]);
    } else {
      // Otherwise, select all
      const validTaskIds = paginatedManagerAssignedTasks
        .map(task => task._id)
        .filter(id => typeof id === 'string' && id.trim() !== '');
      
      setSelectedTasks(validTaskIds);
    }
  };
  
  // Handler for opening batch delete modal
  const handleBatchDeleteConfirm = () => {
    if (selectedTasks.length === 0) {
      toast.warning('Vui lòng chọn ít nhất một công việc để xóa', { autoClose: 3000 });
      return;
    }
    setShowBatchDeleteModal(true);
  };
  
  // Handler for deleting multiple tasks
  const handleBatchDeleteTasks = async () => {
    setButtonDisabled(true);
    try {
      // Log the data being sent for debugging
      console.log('Selected tasks before filtering:', selectedTasks);
      
      // Make sure all IDs are valid strings
      const validTaskIds = selectedTasks.filter(id => typeof id === 'string' && id.trim() !== '');
      console.log('Valid task IDs after filtering:', validTaskIds);
      
      if (validTaskIds.length === 0) {
        toast.error('Không có ID công việc hợp lệ để xóa', { autoClose: 3000 });
        setButtonDisabled(false);
        return;
      }
      
      // Instead of using the batch endpoint, delete tasks one by one
      let successCount = 0;
      let errorCount = 0;
      
      for (const taskId of validTaskIds) {
        try {
          console.log(`Deleting task with ID: ${taskId}`);
          await API.delete(`/workReports/assigned-tasks/manager/${taskId}`);
          successCount++;
        } catch (err) {
          console.error(`Error deleting task ${taskId}:`, err);
          errorCount++;
        }
      }
      
      if (successCount > 0) {
        toast.success(`Đã xóa ${successCount} công việc thành công. Các nhân viên sẽ nhận được thông báo.`, { autoClose: 3000 });
      }
      
      if (errorCount > 0) {
        toast.warning(`Không thể xóa ${errorCount} công việc. Vui lòng thử lại sau.`, { autoClose: 3000 });
      }
      
      setShowBatchDeleteModal(false);
      setSelectedTasks([]);
      fetchManagerAssignedTasks();
    } catch (error) {
      console.error('Error in batch delete process:', error);
      toast.error('Lỗi khi xóa các công việc', { autoClose: 3000 });
    } finally {
      setButtonDisabled(false);
    }
  };
  

  const handleTaskStatusUpdate = async (taskId, newStatus) => {
    setTimeout(() => {
      window.location.reload();
    }, 1000);
    try {
      await API.put(`/workReports/assigned-tasks/${taskId}`, { status: newStatus });
      const msg = newStatus === 'accepted' 
                  ? 'Bạn đã nhận được công việc từ cấp trên' 
                  : 'Công việc đã được đánh dấu hoàn thành';
      toast.success(msg, { autoClose: 3000 });
      fetchAssignedTasks();
      if (canManageReports) fetchManagerAssignedTasks();
    } catch (error) {
      console.error('Error updating task status:', error);
      toast.error('Lỗi khi cập nhật trạng thái công việc', { autoClose: 3000 });
    }
  };

  const addTask = () => {
    setFormData(prev => ({
      ...prev,
      tasks: [
        ...prev.tasks,
        { date: '', content: '', project: '', startTime: '', endTime: '', individual: '', collaborator: [], progress: '', completionPercentage: 0, status: 'ongoing' }
      ]
    }));
  };

  const removeTask = (index) => {
    setFormData(prev => ({
      ...prev,
      tasks: prev.tasks.filter((_, i) => i !== index)
    }));
  };

  const handleSaveDraft = async () => {
    setButtonDisabled(true);
    try {
      const payload = {
        weekStart: formData.weekStart || '',
        managers: formData.managers || [],
        tasks: formData.tasks.map(task => ({
          date: task.date || new Date(),
          content: task.content || '',
          project: task.project || '',
          startTime: task.startTime || new Date(),
          endTime: task.endTime || null,
          individual: task.individual || '',
          collaborator: task.collaborator || [],
          progress: task.progress || '',
          status: task.status || 'ongoing',
          images: task.images || [],
          completionPercentage: task.completionPercentage || 0
        }))
      };
  
      let response;
      if (editing) {
        response = await API.put(`/workReports/draft/${editing}`, payload);
      } else {
        response = await API.post('/workReports/draft', payload);
      }
      
      toast.success(response.data.message || 'Công việc đã được lưu thành nháp!', { autoClose: 3000 });
      fetchReports();
      if (activeTab === 'draft') {
        fetchDraftReports();
      }
      setActiveTab('draft');
    } catch (error) {
      console.error('Error saving draft report:', error.response?.data || error.message);
      toast.error(error.response?.data?.error || 'Lỗi khi lưu công việc', { autoClose: 3000 });
    } finally {
      setButtonDisabled(false);
    }
  };

const handleSearchChange = (e) => {
  const { name, value } = e.target;
  let newSearchData = { ...searchData, [name]: value };
  
  // Nếu thay đổi nhân viên trong tab manage, reset dự án và cập nhật danh sách dự án
  if (name === 'userName' && activeTab === 'manage') {
    newSearchData.project = ''; // Reset dự án khi thay đổi nhân viên
    updateFilteredProjectsByUser(value);
  }
  
  setSearchData(newSearchData);

  if (activeTab === 'submit') {
    // Khi đang ở “Báo cáo của tôi”, chỉ cần reset trang về 1.
    // Phân trang & lọc sẽ được useEffect tự động chạy lại (nhờ đã thêm searchData vào deps).
    // Không cần làm gì thêm vì đã xóa phân trang
  } else if (activeTab === 'my-reports') {
    // Tab "my-reports": reset trang về 1, useEffect sẽ tự động chạy lại
    setMyReportsCurrentPage(1);
  } else {
    // Các tab khác: dùng applyFilterAndPagination ngay
    // Với tab "manage" không cần phân trang nên page = 1 là đủ
    setCurrentPage(1); // Reset về trang 1 khi thay đổi filter
    applyFilterAndPagination(newSearchData, allReports, 1);
  }
};


  const applyFilterAndPagination = useCallback((search, reportsData, page) => {
    let filtered = [...reportsData];
    
    // Lọc theo tên người dùng
    if (search.userName) {
        if (/^[0-9a-fA-F]{24}$/.test(search.userName)) {
              filtered = filtered.filter(report => String(report.user?._id) === search.userName);
            } else {
              filtered = filtered.filter(report =>
                report.user?.fullName?.toLowerCase().includes(search.userName.toLowerCase())
             );
          }
    }
    
    // Lọc theo dự án
    if (search.project) {
      filtered = filtered.filter(report => report.tasks.some(task => task.project?.toLowerCase().includes(search.project.toLowerCase())));
    }
    
    // Lọc theo trạng thái
    if (search.status) {
      filtered = filtered.filter(report => report.tasks.some(task => task.status === search.status));
    }
    
    // Lọc theo quản lý
    if (search.manager && !isAdminOrSuper) {
      filtered = filtered.filter(report => report.managers.some(m => String(m._id) === String(search.manager)));
    }
    
    // Lọc theo thời gian dựa vào filterType
    if (search.filterType === 'date') {
      // Logic lọc theo ngày cụ thể
      if (search.dateFrom) {
        filtered = filtered.filter(report => 
          report.tasks.some(task => new Date(task.date) >= new Date(search.dateFrom))
        );
      }
      if (search.dateTo) {
        filtered = filtered.filter(report => 
          report.tasks.some(task => new Date(task.date) <= new Date(search.dateTo).setHours(23,59,59,999))
        );
      }
    } 
    else if (search.filterType === 'month' && search.month) {
      // Lọc theo tháng (bất kỳ năm nào)
      const month = parseInt(search.month, 10);
      filtered = filtered.filter(report => 
        report.tasks.some(task => {
          const taskDate = new Date(task.date);
          return taskDate.getMonth() + 1 === month;
        })
      );
    } 
    else if (search.filterType === 'year' && search.year) {
      // Lọc theo năm
      const year = parseInt(search.year, 10);
      filtered = filtered.filter(report => 
        report.tasks.some(task => {
          const taskDate = new Date(task.date);
          return taskDate.getFullYear() === year;
        })
      );
    } 
    else if (search.filterType === 'monthYear' && search.month && search.year) {
      // Lọc theo tháng và năm cụ thể
      const month = parseInt(search.month, 10);
      const year = parseInt(search.year, 10);
      filtered = filtered.filter(report => 
        report.tasks.some(task => {
          const taskDate = new Date(task.date);
          return taskDate.getMonth() + 1 === month && taskDate.getFullYear() === year;
        })
      );
    }
    
    // Sắp xếp theo thời gian tạo mới nhất lên đầu
    filtered.sort((a, b) => {
      const dateA = new Date(a.updatedAt || a.createdAt || 0);
      const dateB = new Date(b.updatedAt || b.createdAt || 0);
      return dateB - dateA; // Sắp xếp giảm dần (mới nhất lên đầu)
    });

    // Phân trang cho tab my-reports (theo task)
    if (activeTab === 'submit') {
      // Tạo danh sách tất cả tasks từ các reports đã lọc
      const allFilteredTasks = [];
      filtered.forEach(report => {
        report.tasks.forEach(task => {
          allFilteredTasks.push({...task, report: report});
        });
      });
      
      // Sắp xếp tasks theo ngày cập nhật
      allFilteredTasks.sort((a, b) => {
        const dateA = new Date(a.date || a.createdAt || 0);
        const dateB = new Date(b.date || b.createdAt || 0);
        return dateB - dateA;
      });
      
      // Phân trang theo tasks
      const totalTasks = allFilteredTasks.length;
      const newTotalPages = Math.ceil(totalTasks / reportsPerPage) || 1;
      setTotalPages(newTotalPages);
      
      if (page > newTotalPages) page = newTotalPages;
      
      const startIndex = (page - 1) * reportsPerPage;
      const endIndex = startIndex + reportsPerPage;
      const pageTasks = allFilteredTasks.slice(startIndex, endIndex);
      
      // Lấy các reports chứa tasks trong trang hiện tại
      const reportIds = [...new Set(pageTasks.map(task => task.report._id))];
      const paginatedReports = filtered.filter(report => reportIds.includes(report._id));
      
      setFilteredReports(paginatedReports);
    } else if (activeTab === 'manage') {
      // Tab "Quản lý báo cáo của nhân viên" - hiển thị tất cả không phân trang
      setFilteredReports(filtered);
      setTotalPages(1);
    } else {
      // Phân trang thông thường cho các tab khác
      const totalFiltered = filtered.length;
      const newTotalPages = Math.ceil(totalFiltered / reportsPerPage) || 1;
      if (page > newTotalPages) page = newTotalPages;
      setTotalPages(newTotalPages);
      const startIndex = (page - 1) * reportsPerPage;
      const endIndex = startIndex + reportsPerPage;
      setFilteredReports(filtered.slice(startIndex, endIndex));
    }
  }, [activeTab, canManageReports, user, reportsPerPage, isAdminOrSuper]);
  
  const filteredReportsMemo = useMemo(() => {
    return filteredReports;
  }, [filteredReports]);  
  

  const handleSubmit = async (e) => {
    e.preventDefault();
    setButtonDisabled(true);
    try {
      // Đảm bảo mảng managers chứa các chuỗi ID hợp lệ và duy nhất
      const managerIds = formData.managers
        .map(id => typeof id === 'string' ? id.trim() : String(id).trim())
        .filter(id => id); // Loại bỏ ID trống
  
      // Tạo mảng quản lý duy nhất
      const uniqueManagerIds = [...new Set(managerIds)];
      
      // Thêm ID người dùng hiện tại nếu họ là quản lý báo cáo
      if (isReportManager) {
        const currentUserId = String(user._id || user.id).trim();
        if (!uniqueManagerIds.includes(currentUserId)) {
          uniqueManagerIds.push(currentUserId);
        }
      }
      
      // Tạo payload với danh sách quản lý đã xử lý
      const payload = { 
        ...formData, 
        managers: uniqueManagerIds,
        approvalStatus: 'pending', // Đảm bảo trạng thái là pending (không phải draft)
      };
      
      // Log để gỡ lỗi
      console.log('Gửi payload:', payload);
      console.log('Managers in payload:', uniqueManagerIds);
      
      let response;
      
      // Kiểm tra nếu đang cập nhật bản nháp
      const isDraftReport = editing && draftReports.some(draft => draft._id === editing);
      
      if (editing) {
        const isDraftReport = draftReports.some(draft => draft._id === editing);
        if (isDraftReport) {
          // Đang cập nhật từ bản nháp thành báo cáo chính thức
          const finalPayload = {
            ...payload,
            approvalStatus: 'pending',
            isDraft: false
          };
          response = await API.put(`/workReports/${editing}`, finalPayload);
          toast.success('Chuyển bản nháp thành báo cáo chính thức thành công', { autoClose: 3000 });
          await fetchDraftReports();
          await fetchReports();
          setActiveTab('my-reports');
        
        } else {
          // Sửa báo cáo đã nộp (KHÔNG phải nháp)
          response = await API.put(`/workReports/${editing}`, payload);
          toast.success('Cập nhật báo cáo thành công', { autoClose: 3000 });
          setActiveTab('my-reports');
          await fetchReports();
        }
      } else {
        response = await API.post('/workReports', payload);
        toast.success('Gửi báo cáo công việc thành công. Quản lý sẽ nhận được thông báo trên hệ thống.', { autoClose: 3000 });
        toast.info('Báo cáo của bạn đã được nộp thành công.', { autoClose: 3000 });
        const hasCompletedTasks = payload.tasks.some(task => task.status === 'completed');
        if (hasCompletedTasks) {
          toast.info('Một số công việc đã được đánh dấu hoàn thành. Thông báo đã được gửi đến quản lý.', { autoClose: 3000 });
        }
      }
      
      // Reload danh sách dự án sau khi submit thành công
      loadUserProjectsFromAPI();
      
      setFormData({
        weekStart: '',
        managers: [],
        tasks: [{ date: '', content: '', project: '', startTime: '', endTime: '', individual: '', collaborator: '', progress: '', status: 'ongoing' }]
      });
      setEditing(null);
      fetchReports();
      // Cập nhật lại danh sách bản nháp nếu vừa chuyển từ nháp thành chính thức
      if (isDraftReport) {
        fetchDraftReports();
      }
      if (canManageReports) fetchStats();
    } catch (error) {
      console.error('submitting report:', error);
      toast.error('Lỗi khi lưu báo cáo công việc', { autoClose: 3000 });
    } finally {
      setButtonDisabled(false);
    }
  };

  const handleEdit = (report) => {
    console.log('handleEdit called with report:', report);
    
    if (!report) {
      console.error('Report is null or undefined');
      toast.error('Không tìm thấy thông tin báo cáo', { autoClose: 3000 });
      return;
    }
    
    if (!report.user) {
      console.error('Report.user is null or undefined');
      toast.error('Không tìm thấy thông tin người tạo báo cáo', { autoClose: 3000 });
      return;
    }
  
    // Lấy currentUserId từ user._id hoặc user.id
    const currentUserId = user._id || user.id;
    if (!currentUserId) {
      toast.error('Không tìm thấy thông tin người dùng hiện tại', { autoClose: 3000 });
      return;
    }
  
    // Lấy id của người tạo báo cáo, hỗ trợ cả trường hợp object hoặc string
    const reportUserId = (typeof report.user === "object" && report.user._id)
      ? String(report.user._id)
      : String(report.user);
  
    console.log('Report user (normalized):', reportUserId);
    console.log('Current user ID:', String(currentUserId));
    console.log('Approval status:', report.approvalStatus);
  
    // Cho phép sửa nếu là chủ báo cáo hoặc có quyền quản lý (không phụ thuộc vào trạng thái duyệt)
    if (canManageReports || reportUserId === String(currentUserId)) {
      try {
        console.log('Report data:', report);
        console.log('Report managers:', report.managers);
        console.log('Report tasks:', report.tasks);
        
        setEditing(report._id);
        
        // Helper function to safely convert date
        const safeFormatDate = (dateValue) => {
          if (!dateValue) return '';
          try {
            // If it's already in YYYY-MM-DD format, return as is
            if (typeof dateValue === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
              return dateValue;
            }
            // Otherwise, try to convert to Date and format
            const date = new Date(dateValue);
            if (isNaN(date.getTime())) return '';
            return date.toISOString().split('T')[0];
          } catch (error) {
            console.error('Error formatting date:', error, dateValue);
            return '';
          }
        };

        // Safely format weekStart
        let weekStartFormatted = '';
        try {
          weekStartFormatted = report.weekStart ? new Date(report.weekStart).toISOString().split('T')[0] : '';
        } catch (error) {
          console.error('Error formatting weekStart:', error);
          weekStartFormatted = '';
        }

        // Safely format managers
        const managersFormatted = (report.managers || []).map(m => {
          try {
            return (m && m._id ? String(m._id) : String(m));
          } catch (error) {
            console.error('Error formatting manager:', error, m);
            return '';
          }
        }).filter(Boolean);

        // Safely format tasks
        const tasksFormatted = (report.tasks || []).map(task => {
          try {
            // Xử lý collaborator - chuyển từ object/string thành array ID
            let collaboratorArray = [];
            if (task.collaborator) {
              if (Array.isArray(task.collaborator)) {
                // Nếu là array, xử lý từng phần tử
                collaboratorArray = task.collaborator.map(collab => {
                  if (collab && typeof collab === 'object' && collab._id) {
                    // Nếu là object đã được populate, lấy _id
                    return String(collab._id);
                  } else if (typeof collab === 'string') {
                    // Nếu là string ID
                    return String(collab);
                  }
                  return null;
                }).filter(Boolean);
              } else if (typeof task.collaborator === 'string') {
                // Nếu là string, tách thành array (backward compatibility)
                collaboratorArray = task.collaborator.split(/[,;]/).map(id => id.trim()).filter(Boolean);
              }
            }
            
            return {
              ...task,
              date: safeFormatDate(task.date),
              startTime: safeFormatDate(task.startTime),
              endTime: safeFormatDate(task.endTime),
              collaborator: collaboratorArray
            };
          } catch (error) {
            console.error('Error formatting task:', error, task);
            return {
              date: '',
              content: task?.content || '',
              project: task?.project || '',
              startTime: '',
              endTime: '',
              individual: task?.individual || '',
              collaborator: [],
              progress: task?.progress || '',
              status: task?.status || 'ongoing'
            };
          }
        });

        console.log('Setting form data for edit:', {
          weekStart: weekStartFormatted,
          managers: managersFormatted,
          tasks: tasksFormatted
        });
        
        setFormData({
          weekStart: weekStartFormatted,
          managers: managersFormatted,
          tasks: tasksFormatted
        });
        setActiveTab('submit');
        
        // Scroll to top of the page to show the form
        setTimeout(() => {
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }, 100);
      } catch (error) {
        console.error('Error setting form data:', error);
        toast.error('Có lỗi khi chuẩn bị chỉnh sửa báo cáo', { autoClose: 3000 });
      }
    } else {
      toast.warn('Bạn không có quyền sửa báo cáo này', { autoClose: 3000 });
    }
  };
  
    const handleCopyReport = (report) => {
    try {
      console.log('Copying report:', report);
      
      if (!report) {
        console.error('Report is null or undefined');
        toast.error('Không tìm thấy thông tin báo cáo để sao chép', { autoClose: 3000 });
        return;
      }
      
      const currentDate = new Date().toISOString().split('T')[0];
      
      // Safely handle managers array
      const managerIds = (report.managers || []).map(m => (m && m._id ? String(m._id) : String(m)));
      
      // Safely handle tasks array
      const copiedTasks = (report.tasks || []).map(task => {
        // Helper function to safely convert date
        const safeFormatDate = (dateValue) => {
          if (!dateValue) return '';
          try {
            // If it's already in YYYY-MM-DD format, return as is
            if (typeof dateValue === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
              return dateValue;
            }
            // Otherwise, try to convert to Date and format
            const date = new Date(dateValue);
            if (isNaN(date.getTime())) return '';
            return date.toISOString().split('T')[0];
          } catch (error) {
            console.error('Error formatting date:', error, dateValue);
            return '';
          }
        };

        return {
          ...task,
          date: currentDate,
          _id: undefined,
          startTime: safeFormatDate(task?.startTime),
          endTime: safeFormatDate(task?.endTime),
          status: 'ongoing',
          content: task?.content || '',
          project: task?.project || '',
          individual: task?.individual || '',
          collaborator: task?.collaborator || '',
          progress: task?.progress || '',
          completionPercentage: task?.completionPercentage || 0
        };
      });
  
      console.log('Setting form data for copy:', {
        weekStart: currentDate, 
        managers: managerIds,
        tasks: copiedTasks
      });
      
      setFormData({
        weekStart: currentDate, 
        managers: managerIds,
        tasks: copiedTasks
      });
  
      setEditing(null);
      
      setActiveTab('submit');
      
      // Scroll to top of the page to show the form
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }, 100);
      
      toast.success('Đã sao chép báo cáo, vui lòng cập nhật thông tin và nộp lại', { autoClose: 3000 });
    } catch (error) {
      console.error('Error copying report:', error);
      toast.error('Có lỗi khi sao chép báo cáo', { autoClose: 3000 });
    }
  };

  const openDeleteModal = (report) => {
    // Cho phép người tạo báo cáo hoặc SUPER_ADMIN xóa báo cáo
    const isOwner = report.user && (report.user._id === user.id || report.user._id === user._id);
    
    if (!isSuperAdmin && !isOwner) {
      toast.warn('Bạn không có quyền xóa báo cáo này', { autoClose: 3000 });
      return;
    }
    setReportToDelete(report);
    setShowDeleteModal(true);
  };

  const handleCompleteTask = async (task) => {
    try {
      if (!task || !task.report) {
        toast.error('Không tìm thấy thông tin công việc', { autoClose: 3000 });
        return;
      }

      const reportId = task.report._id;
      const taskId = task._id;

      // Gọi API để cập nhật trạng thái task thành completed
      await API.put(`/workReports/${reportId}`, {
        taskId: taskId,
        status: 'completed'
      });

      toast.success('Đã đánh dấu công việc hoàn thành', { autoClose: 3000 });
      
      // Đóng modal
      setShowTaskModal(false);
      
      // Refresh dữ liệu
      fetchReports();
      if (canManageReports) fetchStats();
      
    } catch (error) {
      console.error('Error completing task:', error);
      toast.error('Lỗi khi cập nhật trạng thái công việc', { autoClose: 3000 });
    }
  };

  const handleResumeTask = async (task) => {
    try {
      if (!task || !task.report) {
        toast.error('Không tìm thấy thông tin công việc', { autoClose: 3000 });
        return;
      }

      const reportId = task.report._id;
      const taskId = task._id;

      // Gọi API để cập nhật trạng thái task thành ongoing
      await API.put(`/workReports/${reportId}`, {
        taskId: taskId,
        status: 'ongoing'
      });

      toast.success('Đã tiếp tục công việc', { autoClose: 3000 });
      
      // Đóng modal
      setShowTaskModal(false);
      
      // Refresh dữ liệu
      fetchReports();
      if (canManageReports) fetchStats();
      
    } catch (error) {
      console.error('Error resuming task:', error);
      toast.error('Lỗi khi cập nhật trạng thái công việc', { autoClose: 3000 });
    }
  };
  

  const handleDelete = async () => {
    try {
      await API.delete(`/workReports/${reportToDelete._id}`);
      toast.success('Xóa báo cáo thành công', { autoClose: 3000 });
      fetchReports();
      if (canManageReports) fetchStats();
      setShowDeleteModal(false);
      setReportToDelete(null);
    } catch (error) {
      toast.error('Lỗi khi xóa báo cáo', { autoClose: 3000 });
    }
  };
  

  const handlePageChange = (page) => {
    setCurrentPage(page);
    applyFilterAndPagination(searchData, allReports, page);
  };

  const handleDownloadReport = async () => {
    try {
      const queryParams = { userName: searchData.userName, weekStart: searchData.weekStart, dateFrom: searchData.dateFrom, dateTo: searchData.dateTo, status: searchData.status, project: searchData.project, manager: searchData.manager };
      const response = await API.get('/workReports/export', { responseType: 'blob', params: queryParams, headers: { 'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' } });
      const url = window.URL.createObjectURL(response.data);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `work_report_${new Date().toISOString().split('T')[0]}.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Báo cáo công việc đã được tải xuống thành công!', { autoClose: 3000 });
    } catch (error) {
      console.error('Error downloading work report:', error);
      toast.error('Lỗi khi tải báo cáo công việc', { autoClose: 5000 });
    }
  };

  const handleCommentChange = (reportId, value) => {
    setCommentData(prev => ({ ...prev, [reportId]: value }));
  };

  const handleAddComment = async (reportId) => {
    try {
      const commentText = commentData[reportId]?.trim();
      if (!commentText) {
        toast.error('Vui lòng nhập nội dung bình luận', { autoClose: 3000 });
        return;
      }

      // Tạo FormData để gửi cả text và ảnh
      const formData = new FormData();
      formData.append('content', commentText);
      
      // Thêm ảnh nếu có
      const images = selectedImages[reportId];
      if (images && images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          formData.append('images', images[i]);
        }
      }

      await API.post(`/workReports/${reportId}/comments`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      
      toast.success('Thêm bình luận thành công. Thông báo đã được gửi đến tất cả người liên quan.', { autoClose: 3000 });
      setCommentData(prev => ({ ...prev, [reportId]: '' }));
      setSelectedImages(prev => ({ ...prev, [reportId]: [] }));
      
      // Refresh reports data
      await fetchReports();
      
      // Nếu modal bình luận hoặc modal chi tiết báo cáo đang mở, cập nhật selectedReport
      if ((showCommentsModal || showReportDetailModal) && selectedReport && selectedReport._id === reportId) {
        const updatedReports = await API.get('/workReports');
        const updatedReport = updatedReports.data.find(r => r._id === reportId);
        if (updatedReport) {
          setSelectedReport(updatedReport);
        }
      }
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error(error.response?.data?.error || 'Lỗi khi thêm bình luận', { autoClose: 3000 });
    }
  };

  const handleDeleteComment = async (reportId, commentId) => {
    try {
      const report = filteredReports.find(r => r._id === reportId);
      const comment = report.comments.find(c => c._id === commentId);
      const canDelete = String(comment.user) === String(user._id) || isAdminOrSuper;
      if (!canDelete) {
        toast.warn('Bạn không có quyền xóa bình luận này', { autoClose: 3000 });
        return;
      }
      await API.delete(`/workReports/${reportId}/comments/${commentId}`);
      toast.success('Xóa bình luận thành công', { autoClose: 3000 });
      
      // Refresh reports data
      await fetchReports();
      
      // Nếu modal bình luận hoặc modal chi tiết báo cáo đang mở, cập nhật selectedReport
      if ((showCommentsModal || showReportDetailModal) && selectedReport && selectedReport._id === reportId) {
        const updatedReports = await API.get('/workReports');
        const updatedReport = updatedReports.data.find(r => r._id === reportId);
        if (updatedReport) {
          setSelectedReport(updatedReport);
        }
      }
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast.error('Lỗi khi xóa bình luận', { autoClose: 3000 });
    }
  };

  const handleCompleteWithImages = async (reportId, taskId, images) => {
    try {
      const report = filteredReports.find(r => r._id === reportId);
      if (!report) {
        toast.error('Không tìm thấy báo cáo', { autoClose: 3000 });
        return;
      }
      
      if (String(report.user?._id || report.user) !== String(user._id || user.id) && !canManageReports) {
        toast.error('Bạn không có quyền cập nhật trạng thái báo cáo này', { autoClose: 3000 });
        return;
      }
      
      if (images && images.length > 0) {
        const formData = new FormData();
        for (let i = 0; i < images.length; i++) {
          formData.append('images', images[i]);
        }
        await API.post(`/workReports/${reportId}/tasks/${taskId}/upload`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
      }
      await API.put(`/workReports/${reportId}`, { taskId, status: 'completed' });
      toast.success('Đã cập nhật trạng thái thành Hoàn thành và upload ảnh (nếu có)', { autoClose: 3000 });
      fetchReports();
      if (canManageReports) fetchStats();
      setSelectedImages(prev => ({ ...prev, [`${reportId}-${taskId}`]: null }));
  
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error completing task with images:', error);
      toast.error('Lỗi khi cập nhật trạng thái hoặc upload ảnh', { autoClose: 3000 });
    }
  };

  const handleComplete = async (reportId, taskId) => {
    try {
      const report = allReports.find(r => r._id === reportId);
      if (!report) {
        toast.error('Không tìm thấy báo cáo', { autoClose: 3000 });
        return;
      }
      if (String(report.user?._id) === String(user._id) && !canManageReports) {
        toast.error('Bạn không có quyền cập nhật trạng thái báo cáo này', { autoClose: 3000 });
        return;
      }
      await API.put(`/workReports/${reportId}`, { taskId, status: 'completed' });
      toast.success('Đã cập nhật trạng thái thành Hoàn thành. Thông báo đã được gửi đến quản lý.', { autoClose: 3000 });
      fetchReports();
      if (canManageReports) fetchStats();
    } catch (error) {
      console.error('Error completing task:', error);
      toast.error('Lỗi khi cập nhật trạng thái', { autoClose: 3000 });
    }
  };
  

  const handleApproveReport = async (reportId) => {
    try {
      const response = await API.post(`/workReports/${reportId}/approve`);
      if (response.data && response.data.message) {
        toast.success(response.data.message, { autoClose: 3000 });
        fetchReports();
        if (canManageReports) fetchStats();
      } else {
        throw new Error('Phản hồi không hợp lệ từ server');
      }
    } catch (error) {
      console.error('Lỗi khi duyệt báo cáo:', error);
      const errorMessage = error.response?.data?.error || 'Lỗi khi duyệt báo cáo';
      toast.error(errorMessage, { autoClose: 3000 });
    }
  };
  

  const handleImageChange = (key, files) => {
    if (files && files.length > 0) {
      setSelectedImages(prev => ({
        ...prev,
        [key]: files
      }));
    } else {
      setSelectedImages(prev => ({
        ...prev,
        [key]: null
      }));
    }
  };

  const handleTaskCompleteWithImages = async (taskId, images) => {
    try {
      if (!taskId) {
        toast.error('Không tìm thấy thông tin công việc', { autoClose: 3000 });
        return;
      }
      if (images && images.length > 0) {
        const formData = new FormData();
        for (let i = 0; i < images.length; i++) {
          formData.append('images', images[i]);
        }
        await API.post(`/workReports/assigned-tasks/${taskId}/upload`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
      }
      await API.put(`/workReports/assigned-tasks/${taskId}`, { status: 'completed' });
      toast.success('Công việc đã được đánh dấu hoàn thành', { autoClose: 3000 });
      fetchAssignedTasks();
      if (canManageReports) fetchManagerAssignedTasks();
      setSelectedImages(prev => ({ ...prev, [taskId]: null }));
    } catch (error) {
      console.error('Error completing assigned task with images:', error);
      toast.error('Lỗi khi cập nhật trạng thái công việc', { autoClose: 3000 });
    }
  };

  const openImageModal = (imageSrc) => {
    setSelectedImage(imageSrc);
    setShowImageModal(true);
  };

    useEffect(() => {
      applyTaskFilterAndPagination(taskSearchData, allTasks, taskCurrentPage);
    }, [taskSearchData, allTasks, taskCurrentPage, applyTaskFilterAndPagination]);
    
    // Effect for paginating assigned tasks
    useEffect(() => {
      if (assignedTasks.length > 0) {
        paginateAssignedTasks(assignedCurrentPage);
      }
    }, [assignedTasks, assignedCurrentPage, paginateAssignedTasks]);
    
    // Effect for paginating draft reports
    useEffect(() => {
      if (draftReports.length > 0) {
        paginateDraftReports(draftCurrentPage);
      }
    }, [draftReports, draftCurrentPage, paginateDraftReports]);
    
    // Effect for paginating manager assigned tasks
    useEffect(() => {
      if (managerAssignedTasks.length > 0) {
        paginateManagerAssignedTasks(managerAssignedCurrentPage);
      }
    }, [managerAssignedTasks, managerAssignedCurrentPage, paginateManagerAssignedTasks]);
  
  const paginateMyReportsInSubmit = useCallback(() => {
    if (!user || !allReports.length) {
      setMyReportsInSubmit([]);
      return;
    }
    
    const currentUserId = user._id || user.id;
    
    // Lọc báo cáo của user hiện tại
    let userReports = allReports.filter(report => {
      const reportUserId = report.user._id || report.user.id || report.user;
      return String(reportUserId) === String(currentUserId);
    });
    
    console.log('User reports for submit tab:', userReports);
    console.log('Sample report managers in submit:', userReports[0]?.managers);
    
    // Tạo mảng chứa tất cả các tasks của user với thông tin báo cáo
    let allUserTasks = [];
    userReports.forEach(report => {
      report.tasks.forEach(task => {
        // Đối với mỗi task, thêm reference đến report gốc
        const taskWithReport = {
          ...task,
          reportId: report._id,
          reportWeekStart: report.weekStart,
          reportApprovalStatus: report.approvalStatus,
          reportUser: report.user,
          report: {
            ...report,
            user: report.user,
            managers: report.managers
          }
        };
        
        console.log('Created task with report for submit tab:', taskWithReport);
        console.log('Task report managers:', taskWithReport.report.managers);
        console.log('Original report managers:', report.managers);
        
        allUserTasks.push(taskWithReport);
      });
    });
    
    // Áp dụng bộ lọc lên mảng tasks
    let filteredTasks = allUserTasks;
    
    // Lọc theo trạng thái
    if (searchData.status) {
      filteredTasks = filteredTasks.filter(task => task.status === searchData.status);
    }
    
    // Lọc theo dự án
    if (searchData.project) {
      filteredTasks = filteredTasks.filter(task => 
        task.project && task.project.toLowerCase().includes(searchData.project.toLowerCase())
      );
    }
    
    // Lọc theo thời gian
    if (searchData.filterType === 'date') {
      if (searchData.dateFrom) {
        filteredTasks = filteredTasks.filter(task => 
          new Date(task.date) >= new Date(searchData.dateFrom)
        );
      }
      if (searchData.dateTo) {
        filteredTasks = filteredTasks.filter(task => 
          new Date(task.date) <= new Date(searchData.dateTo).setHours(23,59,59,999)
        );
      }
    } else if (searchData.filterType === 'month' && searchData.month) {
      const month = parseInt(searchData.month, 10);
      filteredTasks = filteredTasks.filter(task => {
        const taskDate = new Date(task.date);
        return taskDate.getMonth() + 1 === month;
      });
    } else if (searchData.filterType === 'year' && searchData.year) {
      const year = parseInt(searchData.year, 10);
      filteredTasks = filteredTasks.filter(task => {
        const taskDate = new Date(task.date);
        return taskDate.getFullYear() === year;
      });
    } else if (searchData.filterType === 'monthYear' && searchData.month && searchData.year) {
      const month = parseInt(searchData.month, 10);
      const year = parseInt(searchData.year, 10);
      filteredTasks = filteredTasks.filter(task => {
        const taskDate = new Date(task.date);
        return taskDate.getMonth() + 1 === month && taskDate.getFullYear() === year;
      });
    }
    
    // Sắp xếp tasks theo ngày mới nhất
    filteredTasks.sort((a, b) => new Date(b.date) - new Date(a.date));
    
    // Nhóm tất cả tasks theo báo cáo (không phân trang)
    const tasksByReport = {};
    filteredTasks.forEach(task => {
      if (!tasksByReport[task.reportId]) {
        tasksByReport[task.reportId] = {
          _id: task.reportId,
          user: task.reportUser,
          weekStart: task.reportWeekStart,
          approvalStatus: task.reportApprovalStatus,
          managers: task.report?.managers || [], // Thêm managers từ task.report
          tasks: []
        };
      }
      tasksByReport[task.reportId].tasks.push(task);
    });
    
    // Chuyển từ object sang array để render
    const reportsToDisplay = Object.values(tasksByReport);
    
    setMyReportsInSubmit(reportsToDisplay);
}, [allReports, user, searchData]);

// Modify the paginateMyReports function to show all tasks without pagination
const paginateMyReports = useCallback((page = 1) => {
  if (!user || !allReports.length) {
    setPaginatedMyReports([]);
    setMyReportsTotalPages(1);
    return;
  }
  
  const currentUserId = user._id || user.id;
  
  // Create array with all tasks related to user
  let allUserTasks = [];
  
  allReports.forEach(report => {
    const reportUserId = report.user._id || report.user.id || report.user;
    const isAuthor = String(reportUserId) === String(currentUserId);
    
    report.tasks.forEach(task => {
      let shouldInclude = false;
      let userRole = '';
      
      // Check if user is report author
      if (isAuthor) {
        shouldInclude = true;
        userRole = 'author';
      } else {
        // Check if user is collaborator in task
        if (task.collaborator && Array.isArray(task.collaborator)) {
          const isCollaborator = task.collaborator.some(collab => 
            (typeof collab === 'object' && collab._id === currentUserId) ||
            (typeof collab === 'string' && collab === currentUserId)
          );
          if (isCollaborator) {
            shouldInclude = true;
            userRole = 'collaborator';
          }
        }
      }
      
      if (shouldInclude) {
        const taskWithReport = {
          ...task,
          reportId: report._id,
          reportWeekStart: report.weekStart,
          reportApprovalStatus: report.approvalStatus,
          reportUser: report.user,
          userRole: userRole,
          isAuthor: userRole === 'author',
          report: {
            ...report,
            user: report.user,
            managers: report.managers
          }
        };
        allUserTasks.push(taskWithReport);
      }
    });
  });
  
  // Apply filters to tasks array
  let filteredTasks = allUserTasks;
  
  // Filter by status
  if (searchData.status) {
    filteredTasks = filteredTasks.filter(task => task.status === searchData.status);
  }
  
  // Filter by project
  if (searchData.project) {
    filteredTasks = filteredTasks.filter(task => 
      task.project && task.project === searchData.project
    );
  }
  
  // Filter by time
  if (searchData.filterType === 'date') {
    if (searchData.dateFrom) {
      filteredTasks = filteredTasks.filter(task => 
        new Date(task.date) >= new Date(searchData.dateFrom)
      );
    }
    if (searchData.dateTo) {
      filteredTasks = filteredTasks.filter(task => 
        new Date(task.date) <= new Date(searchData.dateTo).setHours(23,59,59,999)
      );
    }
  } else if (searchData.filterType === 'month' && searchData.month) {
    const month = parseInt(searchData.month, 10);
    filteredTasks = filteredTasks.filter(task => {
      const taskDate = new Date(task.date);
      return taskDate.getMonth() + 1 === month;
    });
  } else if (searchData.filterType === 'year' && searchData.year) {
    const year = parseInt(searchData.year, 10);
    filteredTasks = filteredTasks.filter(task => {
      const taskDate = new Date(task.date);
      return taskDate.getFullYear() === year;
    });
  } else if (searchData.filterType === 'monthYear' && searchData.month && searchData.year) {
    const month = parseInt(searchData.month, 10);
    const year = parseInt(searchData.year, 10);
    filteredTasks = filteredTasks.filter(task => {
      const taskDate = new Date(task.date);
      return taskDate.getMonth() + 1 === month && taskDate.getFullYear() === year;
    });
  }
  
  // Sort tasks by newest date
  filteredTasks.sort((a, b) => new Date(b.date) - new Date(a.date));
  
  // Save total count of filtered tasks
  setFilteredMyTasksCount(filteredTasks.length);
  
  // Group tasks by report
  const tasksByReport = {};
  filteredTasks.forEach(task => {
    if (!tasksByReport[task.reportId]) {
      tasksByReport[task.reportId] = {
        _id: task.reportId,
        user: task.reportUser,
        weekStart: task.reportWeekStart,
        approvalStatus: task.reportApprovalStatus,
        managers: task.report?.managers || [],
        tasks: []
      };
    }
    tasksByReport[task.reportId].tasks.push(task);
  });
  
  // Convert from object to array
  const allReportsToDisplay = Object.values(tasksByReport);
  
  // Show all tasks without pagination
  setPaginatedMyReports(allReportsToDisplay);
  setMyReportsTotalPages(1);
  setMyReportsCurrentPage(1);
}, [allReports, user, searchData]);



useEffect(() => {
  if (activeTab === 'submit' && allReports.length > 0 && user) {
    // Khi allReports, user, activeTab hoặc searchData thay đổi
    paginateMyReportsInSubmit();
  }
}, [
  allReports,
  user,
  activeTab,
  paginateMyReportsInSubmit,
  searchData
]);

// Effect to update user's projects and statuses for my-reports tab
useEffect(() => {
  if (allReports.length > 0 && user) {
    const currentUserId = user._id || user.id;
    const userProjectsSet = new Set();
    const userStatusesSet = new Set();
    
    allReports.forEach(report => {
      const reportUserId = report.user._id || report.user.id || report.user;
      const isAuthor = String(reportUserId) === String(currentUserId);
      
      report.tasks.forEach(task => {
        let shouldInclude = false;
        
        // Kiểm tra nếu user là tác giả báo cáo
        if (isAuthor) {
          shouldInclude = true;
        } else {
          // Kiểm tra nếu user là collaborator trong task
          if (task.collaborator && Array.isArray(task.collaborator)) {
            const isCollaborator = task.collaborator.some(collab => 
              (typeof collab === 'object' && collab._id === currentUserId) ||
              (typeof collab === 'string' && collab === currentUserId)
            );
            if (isCollaborator) {
              shouldInclude = true;
            }
          }
        }
        
        if (shouldInclude) {
          if (task.project) {
            userProjectsSet.add(task.project);
          }
          if (task.status) {
            userStatusesSet.add(task.status);
          }
        }
      });
    });
    
    setMyProjects([...userProjectsSet].sort());
    setMyStatuses([...userStatusesSet].sort());
  }
}, [allReports, user]);

// Effect for my-reports tab
useEffect(() => {
  if (activeTab === 'my-reports' && allReports.length > 0 && user) {
    paginateMyReports(myReportsCurrentPage);
  }
}, [
  allReports,
  user,
  activeTab,
  paginateMyReports,
  searchData,
  myReportsCurrentPage
]);



// Debug effect
useEffect(() => {
  console.log('UserProjects state changed:', userProjects);
}, [userProjects]);

// Removed the useEffect that logs projectDropdownStates to prevent infinite loops
// console.log('ProjectDropdownStates changed:', projectDropdownStates);
// }, [projectDropdownStates]);

// Effect để đóng dropdown khi click bên ngoài
useEffect(() => {
  const handleClickOutside = (event) => {
    // Kiểm tra xem click có phải bên ngoài dropdown không
    if (!event.target.closest('.project-input-container')) {
      setProjectDropdownStates({});
    }
  };

  document.addEventListener('mousedown', handleClickOutside);
  return () => {
    document.removeEventListener('mousedown', handleClickOutside);
  };
}, []);


  // Functions để xử lý dropdown dự án
  const handleProjectDropdownToggle = (taskIndex) => {
    console.log('Toggle dropdown for task:', taskIndex);
    console.log('Current dropdown states:', projectDropdownStates);
    console.log('User projects:', userProjects);
    
    // Nếu đang mở dropdown và chưa có dự án, tự động load
    if (!projectDropdownStates[taskIndex] && userProjects.length === 0 && user?.id) {
      console.log('Auto-loading projects when opening dropdown...');
      loadUserProjectsFromAPI();
    }
    
    setProjectDropdownStates(prev => {
      const newState = {
        ...prev,
        [taskIndex]: !prev[taskIndex]
      };
      console.log('New dropdown states:', newState);
      return newState;
    });
  };

  const handleProjectSelect = (taskIndex, project) => {
    const updatedTasks = [...formData.tasks];
    updatedTasks[taskIndex].project = project;
    setFormData({ ...formData, tasks: updatedTasks });
    
    // Cập nhật thời gian sử dụng dự án
    updateProjectUsage(project);
    
    // Đóng dropdown
    setProjectDropdownStates(prev => ({
      ...prev,
      [taskIndex]: false
    }));
  };

  const handleProjectInputChange = (e, taskIndex) => {
    const { value } = e.target;
    const updatedTasks = [...formData.tasks];
    updatedTasks[taskIndex].project = value;
    setFormData({ ...formData, tasks: updatedTasks });
    
    // Hiển thị dropdown khi người dùng nhập
    setProjectDropdownStates(prev => ({
      ...prev,
      [taskIndex]: true
    }));
  };

  const handleProjectKeyDown = (e, taskIndex) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const projectValue = formData.tasks[taskIndex].project;
      if (projectValue && projectValue.trim()) {
        addProjectToUserList(projectValue.trim());
        // Đóng dropdown sau khi thêm
        setProjectDropdownStates(prev => ({
          ...prev,
          [taskIndex]: false
        }));
      }
    }
  };

  // Add this new function before the return statement
  const openTaskModal = (task) => {
    console.log('openTaskModal called with task:', task);
    setSelectedTask(task);
    setShowTaskModal(true);
    console.log('State after openTaskModal: selectedTask =', task, ', showTaskModal =', true);
  };
  
  // Make openTaskModal accessible to the DOM for responsive cards
  useEffect(() => {
    // Make the function globally accessible
    window.openTaskModalFn = openTaskModal;
    
    // Create a direct click handler for responsive cards
    window.handleResponsiveDetailClick = (reportId, taskId) => {
      console.log('Global handler called with:', { reportId, taskId });
      
      // Try to find the task in our data
      let foundTask = null;
      
      // Look in all reports
      if (allReports && allReports.length > 0) {
        for (const report of allReports) {
          if (report._id === reportId) {
            for (const task of report.tasks || []) {
              if (task._id === taskId) {
                foundTask = { ...task, report: report };
                break;
              }
            }
          }
        }
      }
      
      // Look in all tasks
      if (!foundTask && allTasks && allTasks.length > 0) {
        foundTask = allTasks.find(t => t._id === taskId);
      }
      
      // Look in paginated reports
      if (!foundTask && paginatedMyReports && paginatedMyReports.length > 0) {
        for (const report of paginatedMyReports) {
          if (report._id === reportId) {
            for (const task of report.tasks || []) {
              if (task._id === taskId) {
                foundTask = { ...task, report: report };
                break;
              }
            }
          }
        }
      }
      
      // Look in filtered tasks
      if (!foundTask && filteredTasks && filteredTasks.length > 0) {
        foundTask = filteredTasks.find(t => t._id === taskId);
      }
      
      // If we found a task, open the modal
      if (foundTask) {
        console.log('Found task:', foundTask);
        openTaskModal(foundTask);
      } else {
        console.log('Task not found in data, trying direct row click');
        
        // Try to find and click the row directly
        const rows = document.querySelectorAll(`tr[data-report-id="${reportId}"][data-task-id="${taskId}"]`);
        if (rows.length > 0) {
          console.log('Found matching row, clicking it');
          rows[0].click();
        } else {
          console.log('No matching row found');
          
          // Last resort: try to find any row with the task ID
          const taskRows = document.querySelectorAll(`tr[data-task-id="${taskId}"]`);
          if (taskRows.length > 0) {
            console.log('Found row with matching task ID, clicking it');
            taskRows[0].click();
          } else {
            console.log('No row with matching task ID found');
            
            // Show an alert to the user
            alert('Không thể mở chi tiết công việc. Vui lòng thử lại sau.');
          }
        }
      }
    };
    
    // Add a click handler to all task-card-action buttons
    const handleResponsiveCardClick = () => {
      const actionButtons = document.querySelectorAll('.task-card-action');
      actionButtons.forEach(button => {
        if (!button.hasAttribute('data-handler-attached')) {
          button.setAttribute('data-handler-attached', 'true');
          
          // Add a direct click handler
          button.addEventListener('click', (e) => {
            e.stopPropagation();
            console.log('Task card action button clicked');
            
            // Get data attributes directly from the button
            const buttonReportId = button.getAttribute('data-report-id');
            const buttonTaskId = button.getAttribute('data-task-id');
            
            if (buttonReportId && buttonTaskId) {
              console.log('Button has data attributes:', { reportId: buttonReportId, taskId: buttonTaskId });
              
              if (window.handleResponsiveDetailClick) {
                // Use our global handler
                window.handleResponsiveDetailClick(buttonReportId, buttonTaskId);
                return;
              }
            }
            
            // Fallback: try to find the parent card and corresponding row
            const card = button.closest('.task-card');
            if (!card) return;
            
            // Find the original row that this card was created from
            const cardIndex = Array.from(card.parentNode.children).indexOf(card);
            const tableContainer = card.closest('.task-table-container');
            if (!tableContainer) return;
            
            const table = tableContainer.querySelector('.task-table');
            if (!table) return;
            
            const rows = table.querySelectorAll('tbody tr');
            if (!rows || cardIndex >= rows.length) return;
            
            const row = rows[cardIndex];
            console.log('Found corresponding row:', row);
            
            if (row && typeof row.click === 'function') {
              // Fallback to native click
              row.click();
            }
          });
        }
      });
    };
    
    // Run the handler when DOM changes
    const observer = new MutationObserver(handleResponsiveCardClick);
    observer.observe(document.body, { childList: true, subtree: true });
    
    // Initial run
    setTimeout(handleResponsiveCardClick, 500);
    setTimeout(handleResponsiveCardClick, 1000);
    setTimeout(handleResponsiveCardClick, 2000);
    
    return () => {
      delete window.openTaskModalFn;
      delete window.handleResponsiveDetailClick;
      observer.disconnect();
    };
  }, [allReports, allTasks, paginatedMyReports, filteredTasks]);

  // First, let's complete the CustomPagination component implementation
  const CustomPagination = ({ currentPage, totalPages, onPageChange }) => {
    const getPageNumbers = () => {
      const pageNumbers = [];
      const maxVisiblePages = 10;
      
      if (totalPages <= maxVisiblePages) {
        // If total pages is less than or equal to maxVisiblePages, show all pages
        for (let i = 1; i <= totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        // Always show first page
        pageNumbers.push(1);
        
        // Calculate start and end of visible pages
        let startPage = Math.max(2, currentPage - 2);
        let endPage = Math.min(totalPages - 1, currentPage + 2);
        
        // Adjust if we're near the start
        if (currentPage <= 4) {
          endPage = Math.min(totalPages - 1, maxVisiblePages - 1);
        }
        
        // Adjust if we're near the end
        if (currentPage >= totalPages - 3) {
          startPage = Math.max(2, totalPages - maxVisiblePages + 2);
        }
        
        // Add ellipsis after first page if needed
        if (startPage > 2) {
          pageNumbers.push('...');
        }
        
        // Add middle pages
        for (let i = startPage; i <= endPage; i++) {
          pageNumbers.push(i);
        }
        
        // Add ellipsis before last page if needed
        if (endPage < totalPages - 1) {
          pageNumbers.push('...');
        }
        
        // Always show last page
        pageNumbers.push(totalPages);
      }
      
      return pageNumbers;
    };
  
    return (
      <Pagination className="pagination justify-content-center">
        <Pagination.Prev 
          onClick={() => onPageChange(currentPage - 1)} 
          disabled={currentPage === 1} 
        />
        {getPageNumbers().map((page, index) => (
          page === '...' ? (
            <Pagination.Ellipsis key={`ellipsis-${index}`} disabled />
          ) : (
            <Pagination.Item 
              key={page} 
              active={page === currentPage} 
              onClick={() => onPageChange(page)}
            >
              {page}
            </Pagination.Item>
          )
        ))}
        <Pagination.Next 
          onClick={() => onPageChange(currentPage + 1)} 
          disabled={currentPage === totalPages} 
        />
      </Pagination>
    );
  };

  return (
    
    <div className="work-reports-page container">
      <header className="page-header text-center my-4">
        <h1>Báo cáo công việc</h1>
        <p>{canManageReports ? 'Quản lý và nộp báo cáo công việc' : 'Gửi và quản lý báo cáo công việc hàng tuần'}</p>
      </header>

      <Tabs activeKey={activeTab} onSelect={handleTabSelect} className="mb-4">
        {/* Tab Nộp báo cáo */}
        <Tab eventKey="submit" title="Nộp báo cáo">
          <Card className="mb-3">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h2>Báo cáo công việc cá nhân</h2>
              <div className="d-flex gap-2">
                <Button variant="outline-secondary" size="sm" onClick={() => setIsFormCollapsed(!isFormCollapsed)}>
                  {isFormCollapsed ? 'Mở rộng' : 'Thu nhỏ'}
                </Button>
              </div>
            </Card.Header>
            <Card.Body style={{ display: isFormCollapsed ? 'none' : 'block' }}>
              <form className="work-report-form compact-form" onSubmit={handleSubmit}>
                <Form.Group className="mb-3">
                  <Form.Label>Người quản lý (Chọn nhiều nếu cần)</Form.Label>
                  <div className="mb-2">
                    {formData.managers && formData.managers.length > 0 && (
                      <div className="selected-list">
                        <small className="selected-label">Đã chọn:</small>
                        {formData.managers.map(managerId => {
                          const manager = managers.find(m => String(m._id) === String(managerId));
                          return manager ? (
                            <Badge key={managerId} bg="success" className="me-1 mb-1">
                              {manager.fullName}
                              <button
                                type="button"
                                className="btn-close btn-close-white ms-1"
                                style={{ fontSize: '0.7em' }}
                                onClick={() => {
                                  setFormData(prev => ({
                                    ...prev,
                                    managers: prev.managers.filter(id => id !== managerId)
                                  }));
                                }}
                              ></button>
                            </Badge>
                          ) : null;
                        })}
                      </div>
                    )}
                  </div>
                  {loadingManagers ? (
                    <p>Đang tải danh sách quản lý...</p>
                  ) : managers.length === 0 ? (
                    <p>Không có người quản lý nào trong hệ thống</p>
                  ) : (
                    <>
                      <Form.Control
                        type="text"
                        placeholder="Tìm kiếm người quản lý..."
                        className="mb-2"
                        onChange={(e) => {
                          setFormData((prev) => ({ ...prev, managerSearchTerm: e.target.value }));
                        }}
                      />
                      <div
                        className="manager-checkboxes"
                        style={{
                          maxHeight: '150px',
                          overflowY: 'auto',
                          border: '1px solid #d1d5db',
                          borderRadius: '8px',
                          padding: '10px',
                        }}
                      >
                        {managers
                          .filter((manager) => {
                            // Backend đã filter chỉ trả về managers, chỉ cần filter theo search term
                            const matchesSearch =
                              !formData.managerSearchTerm ||
                              manager.fullName
                                .toLowerCase()
                                .includes(formData.managerSearchTerm.toLowerCase());
                            return matchesSearch;
                          })
                          .map((manager) => {
                            let roleInVietnamese = '';
                            switch (manager.role) {
                              case 'REPORT_MANAGER':
                                roleInVietnamese = 'Quản lý báo cáo';
                                break;
                              case 'LEVEL_II_MANAGER':
                                roleInVietnamese = 'Nhân sự';
                                break;
                              case 'ADMIN':
                                roleInVietnamese = 'Quản trị viên';
                                break;
                              case 'SUPER_ADMIN':
                                roleInVietnamese = 'Siêu quản trị viên';
                                break;
                              case 'EMPLOYEE':
                                roleInVietnamese = 'Nhân viên';
                                break;
                              default:
                                roleInVietnamese = manager.role;
                            }
                            const isChecked = formData.managers.includes(String(manager._id));
                            
                            return (
                              <Form.Check
                                key={manager._id}
                                type="checkbox"
                                id={`manager-${manager._id}`}
                                label={`${manager.fullName}`}
                                name="managers"
                                value={manager._id}
                                checked={isChecked}
                                onChange={handleChange}
                                required={formData.managers.length === 0}
                              />
                            );
                          })}
                      </div>
                    </>
                  )}
                </Form.Group>

                <h5 className="mb-3">Nhiệm vụ trong tuần</h5>
                <div className="compact-tasks-container">
                  {formData.tasks.map((task, index) => (
                    <Card key={index} className="compact-task-card mb-2">
                      <Card.Header className="d-flex justify-content-between align-items-center py-2">
                        <span>
                          <strong>Công việc #{index + 1}</strong>
                        </span>
                        <Button variant="danger" size="sm" onClick={() => removeTask(index)}>
                          <i className="fas fa-trash"></i>
                        </Button>
                      </Card.Header>
                      <Card.Body className="horizontal-card-layout">
                        <div className="task-main-info">
                          <div className="task-dates-container d-flex mb-2">
                            <div className="task-field me-3">
                              <Form.Label>Ngày cập nhật</Form.Label>
                              <Form.Control
                                type="date"
                                name="date"
                                value={task.date}
                                onChange={(e) => handleChange(e, index)}
                                required
                              />
                            </div>
                            <div className="task-field me-3">
                              <Form.Label>Từ ngày</Form.Label>
                              <Form.Control
                                type="date"
                                name="startTime"
                                value={task.startTime}
                                onChange={(e) => handleChange(e, index)}
                                required
                              />
                            </div>
                            <div className="task-field">
                              <Form.Label>Đến ngày</Form.Label>
                              <Form.Control
                                type="date"
                                name="endTime"
                                value={task.endTime}
                                onChange={(e) => handleChange(e, index)}
                              />
                            </div>
                          </div>
                          <div className="task-project-info d-flex mb-2">
                            <div className="task-field me-3">
                              <Form.Label>Dự án</Form.Label>
                              <div className="project-input-container">
                                <Form.Control
                                  type="text"
                                  name="project"
                                  value={task.project || ''}
                                  onChange={(e) => handleProjectInputChange(e, index)}
                                  onKeyDown={(e) => handleProjectKeyDown(e, index)}
                                  onFocus={() => {
                                    console.log('Input focused for task:', index);
                                    if (userProjects.length > 0) {
                                      setProjectDropdownStates(prev => ({
                                        ...prev,
                                        [index]: true
                                      }));
                                    }
                                  }}
                                  placeholder="Nhập tên dự án hoặc chọn từ danh sách (Enter để lưu)"
                                  autoComplete="off"
                                />
                                <i 
                                  className="fas fa-chevron-down dropdown-icon"
                                  onClick={() => {
                                    console.log('Arrow clicked for task:', index);
                                    handleProjectDropdownToggle(index);
                                  }}
                                ></i>
                                {projectDropdownStates[index] && (
                                  <div className="project-dropdown">
                                    {console.log('Rendering dropdown for task:', index, 'userProjects:', userProjects)}
                                    {userProjects.length > 0 ? (
                                      <>
                                        {userProjects
                                          .filter(project => 
                                            project.toLowerCase().includes((task.project || '').toLowerCase())
                                          )
                                          .map((project, projectIndex) => (
                                            <div
                                              key={projectIndex}
                                              className="project-dropdown-item d-flex justify-content-between align-items-center"
                                            >
                                              <span 
                                                onClick={() => handleProjectSelect(index, project)}
                                                style={{ flex: 1, cursor: 'pointer' }}
                                              >
                                                {project}
                                              </span>
                                              <i 
                                                className="fas fa-times text-danger ms-2"
                                                style={{ cursor: 'pointer', fontSize: '0.8rem' }}
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  removeProjectFromUserList(project);
                                                }}
                                                title="Xóa dự án khỏi danh sách"
                                              ></i>
                                            </div>
                                          ))
                                        }
                                        {userProjects.filter(project => 
                                          project.toLowerCase().includes((task.project || '').toLowerCase())
                                        ).length === 0 && task.project && (
                                          <div className="project-dropdown-empty">
                                            Không tìm thấy dự án phù hợp
                                          </div>
                                        )}
                                        {userProjects.filter(project => 
                                          project.toLowerCase().includes((task.project || '').toLowerCase())
                                        ).length === 0 && !task.project && (
                                          <div className="project-dropdown-empty">
                                            Nhập để tìm kiếm dự án
                                          </div>
                                        )}
                                      </>
                                    ) : (
                                      <div className="project-dropdown-empty">
                                        Chưa có dự án nào được lưu
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="task-field">
                              <Form.Label>Trạng thái</Form.Label>
                              <Form.Select
                                name="status"
                                value={task.status}
                                onChange={(e) => handleChange(e, index)}
                                required
                              >
                                <option value="ongoing">Tiếp tục</option>
                                <option value="paused">Tạm dừng</option>
                                <option value="completed">Hoàn thành</option>
                              </Form.Select>
                            </div>
                          </div>
                        </div>

                        <div className="task-details-container">
                          <div className="task-content-section mb-2">
                            <div className="task-content-field mb-2">
                              <Form.Label>Nội dung công việc</Form.Label>
                              <Form.Control
                                as="textarea"
                                rows={3}
                                name="content"
                                value={task.content}
                                onChange={(e) => handleChange(e, index)}
                                placeholder="Nội dung công việc chi tiết (có thể liệt kê nhiều việc trong cùng dự án)"
                                required
                              />
                            </div>
                            <div className="task-progress-field">
                              <Form.Label>Tiến độ chi tiết</Form.Label>
                              <Form.Control
                                as="textarea"
                                rows={3}
                                name="progress"
                                value={task.progress}
                                onChange={(e) => handleChange(e, index)}
                                placeholder="Tiến độ chi tiết cho từng nội dung công việc"
                              />
                            </div>
                            <div className="task-completion-field mt-2">
                              <Form.Label>Phần trăm hoàn thành</Form.Label>
                              <Form.Select
                                name="completionPercentage"
                                value={task.completionPercentage || 0}
                                onChange={(e) => handleChange(e, index)}
                                className="completion-percentage-select"
                              >
                                  <option value={0}>0% - Chưa bắt đầu</option>
                                  <option value={10}>10% - Khởi động</option>
                                  <option value={20}>20% - Lập kế hoạch</option>
                                  <option value={30}>30% - Triển khai</option>
                                  <option value={40}>40% - Đang xử lý</option>
                                  <option value={50}>50% - Giữa tiến độ</option>
                                  <option value={60}>60% - Tiến độ tốt</option>
                                  <option value={70}>70% - Gần hoàn thành</option>
                                  <option value={80}>80% - Kiểm tra chất lượng</option>
                                  <option value={90}>90% - Hoàn thiện</option>
                                  <option value={100}>100% - Đã hoàn thành</option>
                              </Form.Select>
                            </div>
                          </div>

                          <div className="task-people-section d-flex">
                            <div className="task-field">
                              <Form.Label>Cá nhân phối hợp</Form.Label>
                              <div className="collaborator-selection">
                                <div className="selected-collaborators mb-2">
                                  {task.collaborator && Array.isArray(task.collaborator) && task.collaborator.length > 0 && (
                                    <div className="selected-list">
                                      <small className="selected-label">Đã chọn:</small>
                                      {task.collaborator.map((collabValue, collabIndex) => {
                                        const employee = employees.find(emp => emp && emp._id && String(emp._id) === String(collabValue));
                                        
                                        if (employee) {
                                          // Hiển thị người dùng hệ thống
                                          return (
                                            <Badge key={`${collabValue}-${collabIndex}`} bg="primary" className="me-1 mb-1">
                                              <i className="fas fa-user me-1"></i>
                                              {employee.fullName || 'Không có tên'}
                                              <button
                                                type="button"
                                                className="btn-close btn-close-white ms-1"
                                                style={{ fontSize: '0.7em' }}
                                                onClick={() => handleRemoveCollaborator(index, collabValue)}
                                              ></button>
                                            </Badge>
                                          );
                                        } else if (typeof collabValue === 'string') {
                                          // Hiển thị tên thủ công
                                          return (
                                            <Badge key={`${collabValue}-${collabIndex}`} bg="warning" text="dark" className="me-1 mb-1">
                                              <i className="fas fa-user-edit me-1"></i>
                                              {collabValue}
                                              <small className="ms-1">(Thủ công)</small>
                                              <button
                                                type="button"
                                                className="btn-close ms-1"
                                                style={{ fontSize: '0.7em' }}
                                                onClick={() => handleRemoveCollaborator(index, collabValue)}
                                              ></button>
                                            </Badge>
                                          );
                                        }
                                        return null;
                                      })}
                                    </div>
                                  )}
                                </div>
                                <Dropdown 
                                  show={collaboratorSearchStates[`task-${index}`]?.show || false}
                                  onToggle={(isOpen) => {
                                    setCollaboratorSearchStates(prev => ({
                                      ...prev,
                                      [`task-${index}`]: { 
                                        ...prev[`task-${index}`], 
                                        show: isOpen,
                                        search: isOpen ? (prev[`task-${index}`]?.search || '') : ''
                                      }
                                    }));
                                  }}
                                >
                                  <Dropdown.Toggle variant="outline-secondary" size="sm">
                                    <i className="fas fa-plus me-1"></i>
                                    Thêm cá nhân phối hợp ({(employees || []).length})
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu style={{ maxHeight: '250px', overflowY: 'auto', minWidth: '300px' }}>
                                    <div className="px-3 py-2">
                                      <Form.Control
                                        type="text"
                                        placeholder="Tìm kiếm theo tên hoặc email..."
                                        size="sm"
                                        value={collaboratorSearchStates[`task-${index}`]?.search || ''}
                                        onChange={(e) => {
                                          setCollaboratorSearchStates(prev => ({
                                            ...prev,
                                            [`task-${index}`]: { 
                                              ...prev[`task-${index}`], 
                                              search: e.target.value 
                                            }
                                          }));
                                        }}
                                        onClick={(e) => e.stopPropagation()}
                                      />
                                    </div>
                                    <Dropdown.Divider />
                                    {(() => {
                                      const searchTerm = collaboratorSearchStates[`task-${index}`]?.search || '';
                                      const filteredEmployees = (employees || []).filter(emp => {
                                        if (!emp || !emp._id) return false;
                                        
                                        const matchesSearch = !searchTerm || 
                                          (emp.fullName && emp.fullName.toLowerCase().includes(searchTerm.toLowerCase())) ||
                                          (emp.email && emp.email.toLowerCase().includes(searchTerm.toLowerCase()));
                                        const isNotCurrentUser = String(emp._id) !== String(user._id || user.id);
                                        const isNotSelected = !(task.collaborator || []).includes(String(emp._id));
                                        return matchesSearch && isNotCurrentUser && isNotSelected;
                                      });

                                      return filteredEmployees.length > 0 ? (
                                        filteredEmployees.map(employee => (
                                          <Dropdown.Item
                                            key={employee._id}
                                            onClick={() => {
                                              handleCollaboratorChange({ target: { value: String(employee._id), checked: true } }, index);
                                              setCollaboratorSearchStates(prev => ({
                                                ...prev,
                                                [`task-${index}`]: { 
                                                  ...prev[`task-${index}`], 
                                                  show: false,
                                                  search: ''
                                                }
                                              }));
                                            }}
                                          >
                                            <div className="d-flex align-items-center">
                                              <div className="flex-grow-1">
                                                <div className="fw-bold">{employee.fullName || 'Không có tên'}</div>
                                                <small className="text-muted d-block">{employee.email || 'Không có email'}</small>
                                                {employee.roleDisplay && (
                                                  <small className="text-info">
                                                    <i className="fas fa-user-tag me-1"></i>
                                                    {employee.roleDisplay}
                                                  </small>
                                                )}
                                              </div>
                                            </div>
                                          </Dropdown.Item>
                                        ))
                                      ) : (
                                        <Dropdown.Item disabled>
                                          {loadingEmployees ? 'Đang tải danh sách...' : 
                                           (employees || []).length === 0 ? 'Chưa có dữ liệu người dùng' : 
                                           searchTerm ? 'Không tìm thấy kết quả phù hợp' :
                                           'Không có người nào khả dụng'}
                                        </Dropdown.Item>
                                      );
                                    })()}
                                  </Dropdown.Menu>
                                </Dropdown>
                                
                                {/* Phần nhập tên thủ công */}
                                <div className="manual-collaborator-input mt-2">
                                  <div className="d-flex gap-2">
                                    <Form.Control
                                      type="text"
                                      placeholder="Hoặc nhập tên cá nhân không có trong hệ thống..."
                                      size="sm"
                                      value={manualCollaboratorInputs[`task-${index}`] || ''}
                                      onChange={(e) => {
                                        setManualCollaboratorInputs(prev => ({
                                          ...prev,
                                          [`task-${index}`]: e.target.value
                                        }));
                                      }}
                                      onKeyPress={(e) => {
                                        if (e.key === 'Enter') {
                                          e.preventDefault();
                                          handleAddManualCollaborator(index);
                                        }
                                      }}
                                    />
                                    <Button
                                      variant="outline-warning"
                                      size="sm"
                                      onClick={() => handleAddManualCollaborator(index)}
                                      disabled={!manualCollaboratorInputs[`task-${index}`]?.trim()}
                                    >
                                      <i className="fas fa-user-plus me-1"></i>
                                      Thêm
                                    </Button>
                                  </div>
                                  <small className="text-muted mt-1 d-block">
                                    <i className="fas fa-info-circle me-1"></i>
                                    Dành cho cá nhân không có tài khoản trong hệ thống
                                  </small>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Card.Body>
                    </Card>
                  ))}
                </div>

                <div className="button-group d-flex justify-content-between mt-3">
                  <Button variant="primary" onClick={addTask}>
                    <i className="fas fa-plus me-1"></i> TẠO CÔNG VIỆC
                  </Button>
                  <div>
                    <Button
                      variant="secondary"
                      onClick={handleSaveDraft}
                      disabled={buttonDisabled}
                      className="me-2"
                    >
                      {buttonDisabled ? 'Đang lưu...' : 'Lưu nháp'}
                    </Button>
                    <Button
                      variant="primary"
                      type="submit"
                      disabled={loadingManagers || managers.length === 0 || buttonDisabled}
                    >
                      {buttonDisabled
                        ? 'Đang xử lý...'
                        : editing
                        ? 'CẬP NHẬT & NỘP'
                        : 'NỘP BÁO CÁO'}
                    </Button>
                  </div>
                </div>
              </form>
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="my-reports" title="Báo cáo của tôi">
          <Card className="mb-3">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h2>Báo cáo của tôi</h2>
              <div className="export-buttons">
                <Button variant="success" onClick={() => handleOpenExportModal('my-reports')}>
                  <i className="fas fa-file-export me-2"></i> Xuất báo cáo
                </Button>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="horizontal-filter-section mb-3">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div className="filter-title">Lọc báo cáo của bạn</div>
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    onClick={() => {
                      const resetData = {
                        userName: '',
                        weekStart: '',
                        dateFrom: '',
                        dateTo: '',
                        status: '',
                        manager: '',
                        project: '',
                        month: '',
                        year: new Date().getFullYear().toString(),
                        filterType: 'date',
                      };
                      setSearchData(resetData);
                      setMyReportsCurrentPage(1);
                    }}
                  >
                    <i className="fas fa-undo me-1"></i>
                    Đặt lại bộ lọc
                  </Button>
                </div>
                
                <Form>
                  <div className="horizontal-filter-container">
                    <div className="horizontal-filter-group">
                      <Form.Label>Trạng thái</Form.Label>
                      <Form.Select name="status" value={searchData.status} onChange={handleSearchChange}>
                        <option value="">Tất cả trạng thái</option>
                        {myStatuses.map((status) => (
                          <option key={status} value={status}>
                            {status === 'ongoing'
                              ? 'Tiếp tục'
                              : status === 'paused'
                              ? 'Tạm dừng'
                              : status === 'completed'
                              ? 'Hoàn thành'
                              : status}
                          </option>
                        ))}
                      </Form.Select>
                    </div>

                    <div className="horizontal-filter-group">
                      <Form.Label>Dự án</Form.Label>
                      <Form.Select name="project" value={searchData.project || ''} onChange={handleSearchChange}>
                        <option value="">Tất cả dự án</option>
                        {myProjects.map((project) => (
                          <option key={project} value={project}>
                            {project}
                          </option>
                        ))}
                      </Form.Select>
                    </div>

                    <div className="horizontal-filter-group">
                      <Form.Label>Lọc theo thời gian</Form.Label>
                      <Form.Select
                        name="filterType"
                        value={searchData.filterType}
                        onChange={(e) => {
                          const newFilterType = e.target.value;
                          setSearchData((prev) => ({
                            ...prev,
                            filterType: newFilterType,
                            dateFrom: '',
                            dateTo: '',
                            month: newFilterType === 'date' ? '' : prev.month,
                            year: prev.year,
                          }));
                          handleSearchChange(e);
                        }}
                      >
                        <option value="date">Theo ngày cụ thể</option>
                        <option value="month">Theo tháng</option>
                        <option value="year">Theo năm</option>
                        <option value="monthYear">Theo tháng và năm</option>
                      </Form.Select>
                    </div>
                  </div>

                  <div className="horizontal-date-filters">
                    {searchData.filterType === 'date' && (
                      <>
                        <div className="horizontal-filter-group">
                          <Form.Label>Từ ngày</Form.Label>
                          <Form.Control
                            type="date"
                            name="dateFrom"
                            value={searchData.dateFrom}
                            onChange={handleSearchChange}
                          />
                        </div>
                        <div className="horizontal-filter-group">
                          <Form.Label>Đến ngày</Form.Label>
                          <Form.Control
                            type="date"
                            name="dateTo"
                            value={searchData.dateTo}
                            onChange={handleSearchChange}
                          />
                        </div>
                      </>
                    )}

                    {(searchData.filterType === 'month' || searchData.filterType === 'monthYear') && (
                      <div className="horizontal-filter-group">
                        <Form.Label>Tháng</Form.Label>
                        <Form.Select name="month" value={searchData.month} onChange={handleSearchChange}>
                          <option value="">Chọn tháng</option>
                          {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                            <option key={month} value={month}>
                              {month}
                            </option>
                          ))}
                        </Form.Select>
                      </div>
                    )}

                    {(searchData.filterType === 'year' || searchData.filterType === 'monthYear') && (
                      <div className="horizontal-filter-group">
                        <Form.Label>Năm</Form.Label>
                        <Form.Select name="year" value={searchData.year} onChange={handleSearchChange}>
                          <option value="">Chọn năm</option>
                          {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map((year) => (
                            <option key={year} value={year}>
                              {year}
                            </option>
                          ))}
                        </Form.Select>
                      </div>
                    )}
                  </div>
                </Form>
              </div>

              <div className="task-table-container">
                <div className="reports-header-controls mb-3">
                  <div className="total-count">
                    Tổng số công việc:{" "}
                    <strong>{filteredMyTasksCount}</strong>
                    {/* {myReportsTotalPages > 1 && (
                      <span className="ms-3">
                        (Đang xem trang {myReportsCurrentPage}/{myReportsTotalPages}, hiển thị{" "}
                        {paginatedMyReports.reduce((total, report) => total + report.tasks.length, 0)}{" "}
                        công việc)
                      </span>
                    )} */}
                  </div>
                  
                </div>

                {(() => {
                  // Sử dụng dữ liệu đã được lọc và phân trang
                  const allMyTasks = [];
                  paginatedMyReports.forEach(report => {
                    report.tasks.forEach(task => {
                      allMyTasks.push({ 
                        ...task, 
                        report: report,
                        isAuthor: task.userRole === 'author'
                      });
                    });
                  });
                  

                  
                  return allMyTasks.length > 0 ? (
                    <>
                      <table className="task-table">
                        <thead>
                          <tr>
                            <th>STT</th>
                            <th></th>
                            <th>Trạng thái</th>
                            <th>Dự án</th>
                            <th>Nội dung</th>
                            <th>Thời gian</th>
                            <th>Cá nhân phối hợp</th>
                            <th>Tiến độ</th>
                            <th>Hoàn thành</th>
                          </tr>
                        </thead>
                        <tbody>
                          {allMyTasks.map((taskData, index) => {
                            const { report, isAuthor, ...task } = taskData;
                            return (
                              <tr
                                key={`${report._id}-${task._id || index}`}
                                onClick={() => {
                                  openTaskModal({ ...task, report: report });
                                }}
                                style={{ cursor: "pointer" }}
                                title="Nhấp để xem chi tiết"
                                data-report-id={report._id}
                                data-task-id={task._id}
                              >
                                <td className="stt-cell">{index + 1}</td>
                                <td className="user-cell">
                                  <div className="user-name">
                                  </div>
                                  <div className="update-date">
                                    Cập nhật: {formatDate(task.date)}
                                  </div>
                                  <div className="approval-status">
                                    <Badge className={`status-${report.approvalStatus}`}>
                                      {report.approvalStatus === "pending"
                                        ? "Chờ duyệt"
                                        : "Đã duyệt"}
                                    </Badge>
                                  </div>
                                  {!isAuthor && (
                                    <div className="report-author text-muted small">
                                      Báo cáo của: {report.user?.fullName}
                                    </div>
                                  )}
                                </td>

                                <td className="status-cell">
                                  <div
                                    className={`status-badge status-${task.status}`}
                                  >
                                    {task.status === "pending"
                                      ? "Chờ xác nhận"
                                      : task.status === "accepted"
                                      ? "Đã nhận"
                                      : task.status === "ongoing"
                                      ? "Tiếp tục"
                                      : task.status === "paused"
                                      ? "Tạm dừng"
                                      : task.status === "completed"
                                      ? "Hoàn thành"
                                      : "Hoàn thành"}
                                  </div>
                                </td>

                                <td className="project-cell">
                                  <div className="preserve-whitespace">
                                    {task.project || "N/A"}
                                  </div>
                                </td>

                                <td className="content-cell">
                                  {task.content && task.content.length > 50 ? (
                                    <div className="expandable-content">
                                      <div className="content-preview">
                                        {task.content.slice(0, 50)}...
                                        <button
                                          className="expand-btn"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            e.currentTarget.parentElement.style.display =
                                              "none";
                                            e.currentTarget.parentElement.nextElementSibling.style.display =
                                              "block";
                                          }}
                                        >
                                          Xem thêm
                                        </button>
                                      </div>
                                      <div
                                        className="content-full"
                                        style={{ display: "none" }}
                                      >
                                        <div className="preserve-whitespace">
                                          {task.content}
                                        </div>
                                        <button
                                          className="collapse-btn"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            e.currentTarget.parentElement.style.display =
                                              "none";
                                            e.currentTarget.parentElement.previousElementSibling.style.display =
                                              "block";
                                          }}
                                        >
                                          Thu gọn
                                        </button>
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="preserve-whitespace">
                                      {task.content}
                                    </div>
                                  )}
                                </td>

                                <td className="time-cell">
                                  <div className="time-range">
                                    <div>Từ: {formatDate(task.startTime)}</div>
                                    <div>Đến: {formatDate(task.endTime)}</div>
                                  </div>
                                </td>

                                <td className="collaborator-cell">
                                  <div className="preserve-whitespace">
                                    {renderCollaborators(task.collaborator, employees)}
                                  </div>
                                </td>

                                <td className="progress-cell">
                                  {task.progress && task.progress.length > 50 ? (
                                    <div className="expandable-content">
                                      <div className="content-preview">
                                        {task.progress.slice(0, 50).replace(/\n/g, " ")}...
                                        <button
                                          className="expand-btn"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            e.currentTarget.parentElement.style.display =
                                              "none";
                                            e.currentTarget.parentElement.nextElementSibling.style.display =
                                              "block";
                                          }}
                                        >
                                          Xem thêm
                                        </button>
                                      </div>
                                      <div
                                        className="content-full"
                                        style={{ display: "none" }}
                                      >
                                        <div className="preserve-whitespace">
                                          {task.progress}
                                        </div>
                                        <button
                                          className="collapse-btn"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            e.currentTarget.parentElement.style.display =
                                              "none";
                                            e.currentTarget.parentElement.previousElementSibling.style.display =
                                              "block";
                                          }}
                                        >
                                          Thu gọn
                                        </button>
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="horizontal-text">
                                      {task.progress
                                        ? task.progress.replace(/\n/g, " ")
                                        : "-"}
                                    </div>
                                  )}
                                </td>

                                <td className="completion-percentage-cell">
                                  <div className="completion-percentage-display">
                                    <div className="completion-badge">
                                      <span className="percentage-text">
                                        {task.completionPercentage || 0}%
                                      </span>
                                    </div>
                                    <div className="progress-bar-container">
                                      <div
                                        className="progress-bar-fill"
                                        style={{
                                          width: `${task.completionPercentage || 0}%`,
                                          backgroundColor:
                                            (task.completionPercentage || 0) === 100 ? '#28a745' :
                                            (task.completionPercentage || 0) >= 80 ? '#20c997' :
                                            (task.completionPercentage || 0) >= 60 ? '#ffc107' :
                                            (task.completionPercentage || 0) >= 40 ? '#fd7e14' :
                                            (task.completionPercentage || 0) >= 20 ? '#dc3545' : '#6c757d'
                                        }}
                                      ></div>
                                    </div>
                                  </div>
                                </td>

                              </tr>
                            );
                          })}
                      </tbody>
                    </table>

                  </>
                ) : (
                  <div className="no-tasks">Không có báo cáo nào để hiển thị.</div>
                );
              })()}
              
              {/* Mobile Card Layout */}
              <div className="task-cards d-block d-md-none">
                {(() => {
                  const allMyTasks = [];
                  paginatedMyReports.forEach(report => {
                    report.tasks.forEach(task => {
                      allMyTasks.push({ 
                        ...task, 
                        report: report,
                        isAuthor: task.userRole === 'author'
                      });
                    });
                  });
                  
                  return allMyTasks.map((taskData, index) => {
                    const { report, isAuthor, ...task } = taskData;
                    return (
                      <div key={`${report._id}-${task._id || index}`} className="task-card">
                        <div className="task-card-number">{index + 1}</div>
                        <div className="task-card-header">
                          <div className="task-card-title">
                            <div className="user-name">{report.user?.fullName || 'N/A'}</div>
                            <div className="update-date">Cập nhật: {formatDate(task.date)}</div>
                          </div>
                          <div className={`task-card-status status-${task.status}`}>
                            {task.status === "ongoing" ? "Tiếp tục" : 
                             task.status === "paused" ? "Tạm dừng" : 
                             task.status === "completed" ? "Hoàn thành" : "Khác"}
                          </div>
                        </div>
                        <div className="task-card-body">
                          <div className="task-card-row">
                            <div className="task-card-label">
                              <i className="fas fa-project-diagram"></i>
                              Dự án
                            </div>
                            <div className="task-card-value">{task.project || 'N/A'}</div>
                          </div>
                          <div className="task-card-row">
                            <div className="task-card-label">
                              <i className="fas fa-tasks"></i>
                              Nội dung
                            </div>
                            <div className="task-card-value content">{task.content || 'N/A'}</div>
                          </div>
                          <div className="task-card-row">
                            <div className="task-card-label">
                              <i className="fas fa-clock"></i>
                              Thời gian
                            </div>
                            <div className="task-card-value time">
                              <div>Từ: {formatDate(task.startTime)}</div>
                              <div>Đến: {formatDate(task.endTime)}</div>
                            </div>
                          </div>
                          <div className="task-card-row">
                            <div className="task-card-label">
                              <i className="fas fa-users"></i>
                              Cá nhân phối hợp
                            </div>
                            <div className="task-card-value collaborator">
                              {renderCollaborators(task.collaborator, employees)}
                            </div>
                          </div>
                          <div className="task-card-row">
                          </div>
                        </div>
                        <div className="task-card-footer">
                          <button 
                            className="task-card-action"
                            onClick={() => openTaskModal({ ...task, report: report })}
                          >
                            <i className="fas fa-eye"></i>
                            Xem chi tiết
                          </button>
                        </div>
                      </div>
                    );
                  });
                })()}
              </div>
              
              {/* Phân trang cho tab my-reports */}
              {myReportsTotalPages > 1 && (
                <div className="d-flex justify-content-center mt-3">
                  <Pagination>
                    <Pagination.First 
                      onClick={() => handleMyReportsPageChange(1)}
                      disabled={myReportsCurrentPage === 1}
                    />
                    <Pagination.Prev 
                      onClick={() => handleMyReportsPageChange(myReportsCurrentPage - 1)}
                      disabled={myReportsCurrentPage === 1}
                    />
                    
                    {Array.from({ length: myReportsTotalPages }, (_, i) => i + 1).map(page => (
                      <Pagination.Item
                        key={page}
                        active={page === myReportsCurrentPage}
                        onClick={() => handleMyReportsPageChange(page)}
                      >
                        {page}
                      </Pagination.Item>
                    ))}
                    
                    <Pagination.Next 
                      onClick={() => handleMyReportsPageChange(myReportsCurrentPage + 1)}
                      disabled={myReportsCurrentPage === myReportsTotalPages}
                    />
                    <Pagination.Last 
                      onClick={() => handleMyReportsPageChange(myReportsTotalPages)}
                      disabled={myReportsCurrentPage === myReportsTotalPages}
                    />
                  </Pagination>
                </div>
              )}
              </div>
            </Card.Body>
          </Card>
        </Tab>
      
        {user.role !== 'ADMIN' && (
        <Tab eventKey="assigned" title="Công việc nhận từ cấp trên">
          <Card className="mb-3">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h2>Công việc nhận từ cấp trên</h2>
              <div className="export-buttons">
                <Button variant="success" onClick={() => handleOpenExportModal('assigned')}>
                  <i className="fas fa-file-export me-2"></i> Xuất báo cáo
                </Button>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="reports-header-controls">
                <div className="total-count">
                  Tổng số công việc: <strong>{assignedTasks.length}</strong>
                  <span className="ms-3">
                    (Đang xem trang {assignedCurrentPage}/{assignedTotalPages}, hiển thị{" "}
                    {paginatedAssignedTasks.length}{" "}
                    công việc)
                  </span>
                </div>
              </div>
              {paginatedAssignedTasks.map((task, index) => (
                <Card key={task._id} className="mb-2">
                  <Card.Body>
                    <div className="stt-badge">STT: {index + 1}</div>
                    <p style={{ whiteSpace: 'pre-line' }}>
                      <strong>Nội dung:</strong> {task.content}
                    </p>
                    <p style={{ whiteSpace: 'pre-line' }}>
                      <strong>Dự án:</strong> {task.project}
                    </p>
                    <p><strong>Hạn chót:</strong> {formatDate(task.dueDate)}</p>
                    <p><strong>Giao bởi:</strong> {task.assignedBy?.fullName || 'Không xác định'}</p>
                    <p>
                      <strong>Trạng thái:</strong>
                      <span className={`status-badge status-${task.status}`}>
                        {task.status === 'pending'
                          ? 'Chờ xác nhận'
                          : task.status === 'accepted'
                            ? 'Đã nhận'
                            : task.status === 'ongoing'
                              ? 'Tiếp tục'
                              : task.status === 'paused'
                                ? 'Tạm dừng'
                                : 'Hoàn thành'}
                      </span>
                    </p>
                    {task.images && task.images.length > 0 && (
                      <div className="mt-2">
                        <strong>Ảnh hoàn thành:</strong>
                        {task.images.map((img, idx) => (
                          <img
                            key={idx}
                            src={img.startsWith('http') ? img : `${backendUrl}${img}`}
                            alt="Task Image"
                            loading="lazy"
                            style={{ width: '100px', marginRight: '10px', cursor: 'pointer' }}
                            onClick={() => openImageModal(img.startsWith('http') ? img : `${backendUrl}${img}`)}
                          />
                        ))}
                      </div>
                    )}
                    {task.status === 'pending' && (
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleTaskStatusUpdate(task._id, 'accepted')}
                        className="me-2"
                      >
                        Nhận việc
                      </Button>
                    )}
                    {task.status === 'accepted' && (
                      <div>
                        <Form.Group className="mb-2">
                          <Form.Label>Upload ảnh hoàn thành</Form.Label>
                          <Form.Control
                            type="file"
                            multiple
                            onChange={(e) => handleImageChange(task._id, e.target.files)}
                          />
                        </Form.Group>
                        <Button
                          variant="outline-success"
                          size="sm"
                          onClick={() => handleTaskCompleteWithImages(task._id, selectedImages[task._id])}
                        >
                          Hoàn thành
                        </Button>
                      </div>
                    )}
                  </Card.Body>
                </Card>
              ))}
            </Card.Body>
          </Card>
          
          {assignedTotalPages > 1 && (
            <CustomPagination 
              currentPage={assignedCurrentPage}
              totalPages={assignedTotalPages}
              onPageChange={handleAssignedPageChange}
            />
          )}
        </Tab>
        )}

        {user.role !== 'ADMIN' && (
          <Tab eventKey="draft" title="Lưu nháp">
            <Card className="mb-3">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <div>
                  <h2>Lưu nháp</h2>
                  <p className="text-muted mb-0">Đây là danh sách công việc bạn đã lưu tạm thời. Chỉ có bạn mới nhìn thấy chúng. Tiếp tục chỉnh sửa và nộp khi hoàn thành.</p>
                </div>
                <div className="export-buttons">
                  <Button variant="success" onClick={() => handleOpenExportModal('draft')}>
                    <i className="fas fa-file-export me-2"></i> Xuất báo cáo
                  </Button>
                </div>
              </Card.Header>
              <Card.Body>
                {draftReports.length > 0 ? (
                  <div className="draft-reports-list">
                    <div className="reports-header-controls">
                      <div className="total-count">
                        Tổng số báo cáo đã lưu: <strong>{draftReports.length}</strong>
                        <span className="ms-3">
                          (Đang xem trang {draftCurrentPage}/{draftTotalPages}, hiển thị {paginatedDraftReports.length} báo cáo)
                        </span>
                      </div>
                    </div>
                    {paginatedDraftReports.map((report, index) => (
                      <Card key={report._id} className="mb-3 draft-card">
                        <Card.Header className="d-flex flex-column flex-md-row justify-content-between align-items-md-center">
                          <div className="mb-2 mb-md-0">
                            <h5>
                              <span className="stt-badge me-2">STT: {index + 1}</span>
                            </h5>
                            <div className="draft-meta">
                              <span className="d-block d-md-inline-block me-md-3 mb-1 mb-md-0">
                                <i className="fas fa-calendar me-1"></i> Ngày lưu: {new Date(report.createdAt).toLocaleString()}
                              </span>
                              <span className="d-block d-md-inline-block me-md-3 mb-1 mb-md-0">
                                <i className="fas fa-tasks me-1"></i> Số công việc: {report.tasks?.length || 0}
                              </span>
                              {report.updatedAt && report.updatedAt !== report.createdAt && (
                                <span className="d-block d-md-inline-block">
                                  <i className="fas fa-edit me-1"></i> Cập nhật lúc: {new Date(report.updatedAt).toLocaleString()}
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="draft-actions align-self-start align-self-md-center">
                            <Button 
                              variant="outline-primary" 
                              size="sm" 
                              onClick={() => handleEdit(report)}
                            >
                              <i className="fas fa-edit me-1"></i> Tiếp tục chỉnh sửa
                            </Button>
                          </div>
                        </Card.Header>
                        <Card.Body>
                          <div className="tasks-summary">
                            <h6 className="mb-3">Tóm tắt công việc:</h6>
                            <div className="list-group">
                              {report.tasks && report.tasks.map((task, taskIndex) => (
                                <div key={taskIndex} className="list-group-item">
                                  <div className="d-flex flex-column flex-sm-row justify-content-between align-items-sm-start">
                                    <div className="task-info flex-grow-1 mb-2 mb-sm-0">
                                      <div className="d-flex justify-content-between align-items-start mb-2">
                                        <h6 className="mb-1 me-2">Công việc {taskIndex + 1}:</h6>
                                      </div>
                                      <p className="mb-1 text-truncate" style={{ maxWidth: '100%' }}>
                                        <strong>Nội dung:</strong> {task.content}
                                      </p>
                                      <div className="small text-muted">
                                        <span className="d-block d-sm-inline me-sm-3">Thời gian: {formatDate(task.startTime)} - {formatDate(task.endTime)}</span>
                                      </div>
                                    </div>
                                    <div className="d-none d-sm-block text-end"> {/* Show badges on the side for sm and up */}
                                      <Badge className={`status-badge status-${task.status} me-2`}>
                                        {task.status === 'ongoing' ? 'Tiếp tục' : 
                                         task.status === 'paused' ? 'Tạm dừng' : 'Hoàn thành'}
                                      </Badge>
                                      {task.project && <Badge bg="info" className="project-badge">{task.project}</Badge>}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </Card.Body>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-5">
                    <div className="mb-3"><i className="fas fa-file-alt fa-3x text-muted"></i></div>
                    <p>Chưa có Lưu nháp nháp nào.</p>
                    <Button variant="primary" onClick={() => setActiveTab('submit')}>
                      <i className="fas fa-plus me-2"></i> Tạo báo cáo mới
                    </Button>
                  </div>
                )}
              </Card.Body>
            </Card>
            
            {draftTotalPages > 1 && (
              <CustomPagination 
                currentPage={draftCurrentPage}
                totalPages={draftTotalPages}
                onPageChange={handleDraftPageChange}
              />
            )}
          </Tab>
        )}
        
        {/* Tab Quản lý công việc của tất cả - hiển thị cho ADMIN và SUPER ADMIN */}
        {(isAdminOrSuper || isReportManager) && (
          <Tab eventKey="all-tasks" title="Quản lý công việc của tất cả">
            <Card className="mb-3">
              <Card.Body>
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div className="filter-title">Lọc công việc</div>
                  {isReportManager && !isAdminOrSuper && (
                    <div className="filter-note alert alert-info py-1 px-3 mb-0 d-inline-block">
                      <small><i className="fas fa-info-circle me-1"></i> Bạn chỉ thấy công việc của nhân viên đã chọn bạn làm người quản lý</small>
                    </div>
                  )}
                  <div className="export-buttons">
                    <Button variant="success" onClick={() => handleOpenExportModal('all-tasks')}>
                      <i className="fas fa-file-export me-2"></i> Xuất báo cáo
                    </Button>
                  </div>
                </div>
                <Form className="horizontal-filter-section">
                  <div className="horizontal-filter-container">
                    <div className="horizontal-filter-group">
                      <Form.Label>Tên nhân viên</Form.Label>
                      <Form.Select 
                        name="userName" 
                        value={taskSearchData.userName} 
                        onChange={handleTaskSearchChange}
                      >
                        <option value="">Tất cả nhân viên</option>
                        {uniqueUsers.map(user => (
                          <option key={user._id} value={user._id}>{user.fullName}</option>
                        ))}
                      </Form.Select>
                    </div>
                    
                    <div className="horizontal-filter-group">
                      <Form.Label>Dự án</Form.Label>
                      <Form.Select 
                        name="project" 
                        value={taskSearchData.project || ''} 
                        onChange={handleTaskSearchChange}
                      >
                        <option value="">Tất cả dự án</option>
                        {filteredTaskProjectsByUser.map(project => (
                          <option key={project} value={project}>{project}</option>
                        ))}
                      </Form.Select>
                    </div>
                    
                    <div className="horizontal-filter-group">
                      <Form.Label>Trạng thái</Form.Label>
                      <Form.Select name="status" value={taskSearchData.status} onChange={handleTaskSearchChange}>
                        <option value="">Tất cả trạng thái</option>
                        {uniqueStatuses.map(status => (
                          <option key={status} value={status}>
                            {status === 'ongoing' ? 'Tiếp tục' : 
                             status === 'paused' ? 'Tạm dừng' : 
                             status === 'completed' ? 'Hoàn thành' : 
                             status === 'pending' ? 'Chờ xác nhận' :
                             status === 'accepted' ? 'Đã nhận' :
                             status}
                          </option>
                        ))}
                      </Form.Select>
                    </div>
                    
                    <div className="horizontal-filter-group">
                      <Form.Label>Lọc theo thời gian</Form.Label>
                      <Form.Select 
                        name="filterType"
                        value={taskSearchData.filterType} 
                        onChange={(e) => {
                          const newFilterType = e.target.value;
                          setTaskSearchData(prev => ({ 
                            ...prev, 
                            filterType: newFilterType,
                            dateFrom: '',
                            dateTo: '',
                            month: newFilterType === 'date' ? '' : prev.month,
                            year: prev.year
                          }));
                          handleTaskSearchChange(e);
                        }}>
                        <option value="date">Theo ngày cụ thể</option>
                        <option value="month">Theo tháng</option>
                        <option value="year">Theo năm</option>
                        <option value="monthYear">Theo tháng và năm</option>
                      </Form.Select>
                    </div>
                  </div>
            
            <div className="horizontal-date-filters">
              {taskSearchData.filterType === 'date' && (
                <>
                  <div className="horizontal-filter-group">
                    <Form.Label>Từ ngày</Form.Label>
                    <Form.Control 
                      type="date" 
                      name="dateFrom" 
                      value={taskSearchData.dateFrom} 
                      onChange={handleTaskSearchChange} 
                    />
                  </div>
                  <div className="horizontal-filter-group">
                    <Form.Label>Đến ngày</Form.Label>
                    <Form.Control 
                      type="date" 
                      name="dateTo" 
                      value={taskSearchData.dateTo} 
                      onChange={handleTaskSearchChange} 
                    />
                  </div>
                </>
              )}
              
              {(taskSearchData.filterType === 'month' || taskSearchData.filterType === 'monthYear') && (
                <div className="horizontal-filter-group">
                  <Form.Label>Tháng</Form.Label>
                  <Form.Select name="month" value={taskSearchData.month} onChange={handleTaskSearchChange}>
                    <option value="">Chọn tháng</option>
                    {Array.from({length: 12}, (_, i) => i + 1).map(month => (
                      <option key={month} value={month}>{month}</option>
                    ))}
                  </Form.Select>
                </div>
              )}
              
              {(taskSearchData.filterType === 'year' || taskSearchData.filterType === 'monthYear') && (
                <div className="horizontal-filter-group">
                  <Form.Label>Năm</Form.Label>
                  <Form.Select name="year" value={taskSearchData.year} onChange={handleTaskSearchChange}>
                    <option value="">Chọn năm</option>
                    {Array.from({length: 5}, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </Form.Select>
                </div>
              )}
              
              <div className="horizontal-filter-buttons">
                <Button 
                  variant="outline-secondary" 
                  onClick={() => {
                    const resetSearch = { 
                      userName: '', 
                      project: '', 
                      status: '', 
                      dateFrom: '', 
                      dateTo: '',
                      month: '',
                      year: new Date().getFullYear().toString(),
                      filterType: 'date'
                    };
                    setTaskSearchData(resetSearch);
                    setEmployeeFilterSearch('');
                    updateFilteredTaskProjectsByUser(''); // Reset danh sách dự án về tất cả
                    applyTaskFilterAndPagination(resetSearch, allTasks, 1);
                  }}
                >
                  <i className="fas fa-undo me-1"></i> Đặt lại bộ lọc
                </Button>
              </div>
            </div>
          </Form>
        </Card.Body>
      </Card>
      
      <div className="task-table-container">
        <div className="reports-header-controls">
          <div className="total-count">
            Tổng số công việc: <strong>{allTasks.length}</strong>
            <span className="ms-3">
              (Hiển thị {filteredTasks.length} công việc)
            </span>
          </div>
          
        </div>
  {/* Desktop Table Layout */}
  <div className="d-none d-md-block">
    {filteredTasks.length > 0 ? (
      <table className="task-table">
        <thead>
          <tr>
            <th>STT</th>
            <th>Người thực hiện</th>
            <th>Trạng thái</th>
            <th>Dự án</th>
            <th>Nội dung</th>
            <th>Thời gian</th>
            <th>Cá nhân phối hợp</th>
            <th>Tiến độ</th>
            <th>Hoàn thành</th>
            {/* <th>Ảnh</th> */}
          </tr>
        </thead>
        <tbody>
          {filteredTasks.map((task, index) => (
            <tr 
              key={`${task.reportId}-${task._id || index}`}
              onClick={() => openTaskModal(task)}
              style={{ cursor: 'pointer' }}
              title="Nhấp để xem chi tiết"
              data-report-id={task.reportId}
              data-task-id={task._id}
            >
              <td className="stt-cell">{index + 1}</td>
              <td className="user-cell">
                <div className="user-name">{task.report?.user?.fullName || 'Không xác định'}</div>
                <div className="update-date">Cập nhật: {formatDate(task.date)}</div>
              </td>
              
              <td className="status-cell">
                <div className={`status-badge status-${task.status}`}>
                  {task.status === 'pending'
                    ? 'Chờ xác nhận'
                    : task.status === 'accepted'
                      ? 'Đã nhận'
                      : task.status === 'ongoing'
                        ? 'Tiếp tục'
                        : task.status === 'paused'
                          ? 'Tạm dừng'
                          : task.status === 'completed'
                            ? 'Hoàn thành'
                            : 'Hoàn thành'}
                </div>
              </td>
              
              <td className="project-cell">
                <div className="preserve-whitespace">{task.project || 'N/A'}</div>
              </td>
              
              <td className="content-cell">
                {task.content && task.content.length > 50 ? (
                  <div className="expandable-content">
                    <div className="content-preview">
                      {task.content.slice(0, 50)}...
                      <button 
                        className="expand-btn"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row click
                          e.currentTarget.parentElement.style.display = 'none';
                          e.currentTarget.parentElement.nextElementSibling.style.display = 'block';
                        }}
                      >
                        Xem thêm
                      </button>
                    </div>
                    <div className="content-full" style={{ display: 'none' }}>
                      <div className="preserve-whitespace">{task.content}</div>
                      <button 
                        className="collapse-btn"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row click
                          e.currentTarget.parentElement.style.display = 'none';
                          e.currentTarget.parentElement.previousElementSibling.style.display = 'block';
                        }}
                      >
                        Thu gọn
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="preserve-whitespace">{task.content}</div>
                )}
              </td>
              
              <td className="time-cell">
                <div className="time-range">
                  <div>Từ: {formatDate(task.startTime)}</div>
                  <div>Đến: {formatDate(task.endTime)}</div>
                </div>
              </td>
              
              <td className="collaborator-cell">
                <div className="preserve-whitespace">
                  {renderCollaborators(task.collaborator, employees)}
                </div>
              </td>
              
              <td className="progress-cell">
                {task.progress && task.progress.length > 50 ? (
                  <div className="expandable-content">
                    <div className="content-preview">
                      {task.progress.slice(0, 50).replace(/\n/g, ' ')}...
                      <button 
                        className="expand-btn"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row click
                          e.currentTarget.parentElement.style.display = 'none';
                          e.currentTarget.parentElement.nextElementSibling.style.display = 'block';
                        }}
                      >
                        Xem thêm
                      </button>
                    </div>
                    <div className="content-full" style={{ display: 'none' }}>
                      <div className="preserve-whitespace">{task.progress}</div>
                      <button 
                        className="collapse-btn"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row click
                          e.currentTarget.parentElement.style.display = 'none';
                          e.currentTarget.parentElement.previousElementSibling.style.display = 'block';
                        }}
                      >
                        Thu gọn
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="horizontal-text">{task.progress ? task.progress.replace(/\n/g, ' ') : '-'}</div>
                )}
              </td>

              <td className="completion-percentage-cell">
                <div className="completion-percentage-display">
                  <div className="completion-badge">
                    <span className="percentage-text">
                      {task.completionPercentage || 0}%
                    </span>
                  </div>
                  <div className="progress-bar-container">
                    <div
                      className="progress-bar-fill"
                      style={{
                        width: `${task.completionPercentage || 0}%`,
                        backgroundColor:
                          (task.completionPercentage || 0) === 100 ? '#28a745' :
                          (task.completionPercentage || 0) >= 80 ? '#20c997' :
                          (task.completionPercentage || 0) >= 60 ? '#ffc107' :
                          (task.completionPercentage || 0) >= 40 ? '#fd7e14' :
                          (task.completionPercentage || 0) >= 20 ? '#dc3545' : '#6c757d'
                      }}
                    ></div>
                  </div>
                </div>
              </td>

              {/* <td className="image-cell">
                {task.images && task.images.length > 0 && (
                  <div className="task-images">
                    {task.images.map((img, idx) => (
                      <img
                        key={idx}
                        src={`${backendUrl}${img}`}
                        alt="Task Image"
                        loading="lazy"
                        onClick={() => openImageModal(`${backendUrl}${img}`)}
                      />
                    ))}
                  </div>
                )}
              </td> */}
            </tr>
          ))}
        </tbody>
      </table>
    ) : (
      <div className="no-tasks">Không có công việc nào để hiển thị.</div>
    )}
  </div>
  
  {/* Mobile Card Layout */}
  <div className="task-cards d-block d-md-none">
    {filteredTasks.length > 0 ? (
      filteredTasks.map((task, index) => (
        <div 
          key={`${task.reportId}-${task._id || index}`} 
          className="task-card"
          onClick={() => openTaskModal(task)}
          style={{ cursor: 'pointer' }}
          title="Nhấp để xem chi tiết"
        >
          <div className="task-card-number">{index + 1}</div>
          <div className="task-card-header">
            <div className="task-card-title">
              <div className="user-name">{task.report?.user?.fullName || 'Không xác định'}</div>
              <div className="update-date">Cập nhật: {formatDate(task.date)}</div>
            </div>
            <div className={`status-badge status-${task.status}`}>
              {task.status === 'pending'
                ? 'Chờ xác nhận'
                : task.status === 'accepted'
                  ? 'Đã nhận'
                  : task.status === 'ongoing'
                    ? 'Tiếp tục'
                    : task.status === 'paused'
                      ? 'Tạm dừng'
                      : task.status === 'completed'
                        ? 'Hoàn thành'
                        : 'Hoàn thành'}
            </div>
          </div>
          
          <div className="task-card-content">
            <div className="task-card-row">
              <strong>Dự án:</strong> {task.project || 'N/A'}
            </div>
            
            <div className="task-card-row">
              <strong>Nội dung:</strong>
              <div className="preserve-whitespace">{task.content}</div>
            </div>
            
            <div className="task-card-row">
              <strong>Thời gian:</strong>
              <div className="time-range">
                <div>Từ: {formatDate(task.startTime)}</div>
                <div>Đến: {formatDate(task.endTime)}</div>
              </div>
            </div>
            
            <div className="task-card-row">
              <strong>Cá nhân phối hợp:</strong>
              <div className="preserve-whitespace">
                {renderCollaborators(task.collaborator, employees)}
              </div>
            </div>
            
            <div className="task-card-row">
              <strong>Tiến độ:</strong>
              <div className="preserve-whitespace">{task.progress || '-'}</div>
            </div>
          </div>
          <div className="task-card-footer">
            <button 
              className="task-card-action"
              onClick={(e) => {
                e.stopPropagation(); // Ngăn sự kiện click của card cha
                openTaskModal(task);
              }}
            >
              <i className="fas fa-eye"></i>
              Xem chi tiết
            </button>
          </div>
        </div>
      ))
    ) : (
      <div className="no-tasks">Không có công việc nào để hiển thị.</div>
    )}
  </div>
        </div>
      

    </Tab>
  )}
        {(canManageReports || user.role === 'ADMIN') && (
      <Tab eventKey="manager-assigned" title="Công việc đã giao cho nhân viên">
        <Card className="mb-3">
          <Card.Header className="d-flex justify-content-between align-items-center">
            <h2>Công việc đã giao cho nhân viên</h2>
            <Button variant="primary" onClick={() => setShowAssignTaskModal(true)}>
              Giao việc mới
            </Button>
          </Card.Header>
          <Card.Body>
            <div className="mb-3">
              <h4>Bộ lọc</h4>
              <Form className="row">
                <Form.Group className="col-md-4 mb-2">
                  <Form.Label>Nhân viên</Form.Label>
                  <Form.Select 
                    name="employeeId" 
                    value={managerTaskFilter.employeeId} 
                    onChange={handleManagerTaskFilterChange}
                  >
                    <option value="">Tất cả nhân viên</option>
                    {getAssignedEmployees(managerAssignedTasks).map(emp => (
                      <option key={emp._id} value={emp._id}>{emp.fullName}</option>
                    ))}
                  </Form.Select>
                </Form.Group>
                
                <Form.Group className="col-md-4 mb-2">
                  <Form.Label>Dự án</Form.Label>
                  <Form.Control 
                    type="text" 
                    name="project" 
                    value={managerTaskFilter.project} 
                    onChange={handleManagerTaskFilterChange}
                    placeholder="Lọc theo dự án"
                  />
                </Form.Group>
                
                <Form.Group className="col-md-4 mb-2">
                  <Form.Label>Trạng thái</Form.Label>
                  <Form.Select 
                    name="status" 
                    value={managerTaskFilter.status} 
                    onChange={handleManagerTaskFilterChange}
                  >
                    <option value="">Tất cả trạng thái</option>
                    <option value="pending">Chờ xác nhận</option>
                    <option value="accepted">Đã nhận</option>
                    <option value="ongoing">Tiếp tục</option>
                    <option value="paused">Tạm dừng</option>
                    <option value="completed">Hoàn thành</option>
                  </Form.Select>
                </Form.Group>
                
                <div className="col-12 mt-2">
                  <Button variant="outline-secondary" onClick={() => {
                    const resetFilter = { employeeId: '', project: '', status: '' };
                    setManagerTaskFilter(resetFilter);
                    filterAndPaginateManagerTasks(resetFilter, 1);
                    setManagerAssignedCurrentPage(1);
                  }}>
                    Đặt lại bộ lọc
                  </Button>
                </div>
              </Form>
            </div>
            
            <div className="reports-header-controls">
              <div className="total-count">
                Tổng số công việc đã giao: <strong>{managerAssignedTasks.length}</strong>
                <span className="ms-3">
                  (Đang xem trang {managerAssignedCurrentPage}/{managerAssignedTotalPages}, hiển thị {paginatedManagerAssignedTasks.length} công việc)
                </span>
              </div>
              {selectedTasks.length > 0 && (
                <Button 
                  variant="danger" 
                  onClick={handleBatchDeleteConfirm}
                  className="mt-2"
                >
                  <i className="fas fa-trash"></i> Xóa ({selectedTasks.length}) công việc đã chọn
                </Button>
              )}
            </div>
            
            {/* Table header with select all checkbox */}
            <div className="task-table-header d-flex align-items-center mb-2 p-3 bg-light rounded">
              <div className="d-flex align-items-center">
                <Form.Check 
                  type="checkbox" 
                  className="me-2"
                  checked={selectedTasks.length === paginatedManagerAssignedTasks.length && paginatedManagerAssignedTasks.length > 0}
                  onChange={handleSelectAllTasks}
                  id="select-all-tasks"
                />
                <label htmlFor="select-all-tasks" className="mb-0 cursor-pointer">
                  {selectedTasks.length === 0 
                    ? "Chọn tất cả" 
                    : selectedTasks.length === paginatedManagerAssignedTasks.length 
                      ? "Bỏ chọn tất cả" 
                      : `Đã chọn ${selectedTasks.length}/${paginatedManagerAssignedTasks.length}`}
                </label>
              </div>
            </div>
            
            {paginatedManagerAssignedTasks.map((task, index) => (
              <Card key={task._id} className="mb-2 task-card manager-assigned-card">
                <Card.Body className="d-flex align-items-center">
                  <div className="task-select me-3">
                    <Form.Check 
                      type="checkbox" 
                      id={`task-select-${task._id}`}
                      checked={selectedTasks.includes(task._id)}
                      onChange={() => handleTaskSelection(task._id)}
                    />
                  </div>
                  <div className="task-content flex-grow-1">
                    <div className="d-flex justify-content-between align-items-start mb-2">
                      <div className="stt-badge">STT: {index + 1 + (managerAssignedCurrentPage - 1) * reportsPerPage}</div>
                      <div className={`status-badge status-${task.status} ms-2`}>
                        {task.status === 'pending'
                          ? 'Chờ xác nhận'
                          : task.status === 'accepted'
                            ? 'Đã nhận'
                            : task.status === 'ongoing'
                              ? 'Tiếp tục'
                              : task.status === 'paused'
                                ? 'Tạm dừng'
                                : 'Hoàn thành'}
                      </div>
                    </div>
                    
                      <div className="task-info mb-3">
                      <h5 className="mb-2">Thông tin công việc</h5>
                      <div className="p-3 bg-light rounded mb-2">
                        <p className="mb-1"><strong>Nội dung:</strong></p>
                        <p style={{ whiteSpace: 'pre-line' }}>{task.content}</p>
                      </div>
                      
                      <div className="p-3 bg-light rounded mb-2">
                        <p className="mb-1"><strong>Dự án:</strong></p>
                        <p style={{ whiteSpace: 'pre-line' }}>{task.project || 'Không có'}</p>
                      </div>
                      
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Hạn chót:</strong> {formatDate(task.dueDate)}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Giao cho:</strong> {task.employeeId?.fullName || 'Không xác định'}</p>
                        </div>
                      </div>
                    </div>
                    
                    {task.images && task.images.length > 0 && (
                      <div className="mt-3">
                        <h5 className="mb-2">Ảnh hoàn thành</h5>
                        <div className="task-images">
                          {task.images.map((img, idx) => (
                            <img
                              key={idx}
                              src={`${backendUrl}${img}`}
                              alt="Task Image"
                              loading="lazy"
                              className="task-image"
                              style={{ width: '100px', height: '100px', objectFit: 'cover', marginRight: '10px', cursor: 'pointer', borderRadius: '4px' }}
                              onClick={() => openImageModal(`${backendUrl}${img}`)}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="task-actions ms-3">
                    <Button 
                      variant="outline-primary" 
                      size="sm" 
                      className="mb-2 w-100"
                      onClick={() => handleEditTask(task)}
                    >
                      <i className="fas fa-edit"></i> Sửa
                    </Button>
                    <Button 
                      variant="outline-danger" 
                      size="sm"
                      className="w-100"
                      onClick={() => handleDeleteTaskConfirm(task)}
                    >
                      <i className="fas fa-trash"></i> Xóa
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            ))}
            
            {paginatedManagerAssignedTasks.length === 0 && (
              <div className="text-center p-4">
                <p className="text-muted">Không có công việc nào được tìm thấy</p>
              </div>
            )}
          </Card.Body>
        </Card>
        
        {managerAssignedTotalPages > 1 && (
          <CustomPagination 
            currentPage={managerAssignedCurrentPage}
            totalPages={managerAssignedTotalPages}
            onPageChange={handleManagerAssignedPageChange}
          />
        )}
      </Tab>

        )}
      </Tabs>

      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Xác nhận xóa báo cáo</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Bạn có chắc chắn muốn xóa báo cáo này?</p>
          {reportToDelete && (
            <div>
              <p><strong>Nhân viên:</strong> {reportToDelete.user?.fullName || 'N/A'}</p>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>Hủy</Button>
          <Button variant="danger" onClick={handleDelete}>Xóa</Button>
        </Modal.Footer>
      </Modal>

      <Modal show={showAssignTaskModal} onHide={() => setShowAssignTaskModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Giao việc cho nhân viên/quản lý</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleAssignTaskSubmit}>
          <Form.Group className="mb-3 position-relative">
          <Form.Label>Chọn người nhận việc</Form.Label>
          <Form.Control
            type="text"
            placeholder="Tìm kiếm người nhận..."
            value={employeeSearch}
            onChange={(e) => {
              setEmployeeSearch(e.target.value);
              setShowDropdown(true);
            }}
            onFocus={() => setShowDropdown(true)}
            autoComplete="off"
          />
          {showDropdown && employeeSearch && (
            <div className="dropdown-menu show suggestion-dropdown" style={{ width: '100%' }}>
              <div className="dropdown-header">Nhân viên</div>
              {employees
                .filter(e => e.role === 'EMPLOYEE' && e.fullName.toLowerCase().includes(employeeSearch.toLowerCase()))
                .map(employee => (
                  <button
                    key={employee._id}
                    type="button"
                    className="dropdown-item"
                    onMouseDown={(e) => e.preventDefault()}  // Prevents input from losing focus
                    onClick={() => {
                      setAssignTaskForm(prev => ({ ...prev, employeeId: employee._id }));
                      setEmployeeSearch(employee.fullName);
                      setShowDropdown(false);
                    }}
                  >
                    {employee.fullName}
                  </button>
              ))}
              <div className="dropdown-header">Quản lý báo cáo của nhân viên</div>
              {employees
                .filter(e => e.role === 'REPORT_MANAGER' && e.fullName.toLowerCase().includes(employeeSearch.toLowerCase()))
                .map(employee => (
                  <button
                    key={employee._id}
                    type="button"
                    className="dropdown-item"
                    onMouseDown={(e) => e.preventDefault()}
                    onClick={() => {
                      setAssignTaskForm(prev => ({ ...prev, employeeId: employee._id }));
                      setEmployeeSearch(employee.fullName);
                      setShowDropdown(false);
                    }}
                  >
                    {employee.fullName}
                  </button>
              ))}
              <div className="dropdown-header">Quản lý</div>
              {employees
                .filter(e => e.role === 'LEVEL_II_MANAGER' && e.fullName.toLowerCase().includes(employeeSearch.toLowerCase()))
                .map(employee => (
                  <button
                    key={employee._id}
                    type="button"
                    className="dropdown-item"
                    onMouseDown={(e) => e.preventDefault()}
                    onClick={() => {
                      setAssignTaskForm(prev => ({ ...prev, employeeId: employee._id }));
                      setEmployeeSearch(employee.fullName);
                      setShowDropdown(false);
                    }}
                  >
                    {employee.fullName}
                  </button>
              ))}
              <div className="dropdown-header">Quản trị viên hệ thống</div>
              {employees
                .filter(e => e.role === 'SUPER_ADMIN' && e.fullName.toLowerCase().includes(employeeSearch.toLowerCase()))
                .map(employee => (
                  <button
                    key={employee._id}
                    type="button"
                    className="dropdown-item"
                    onMouseDown={(e) => e.preventDefault()}
                    onClick={() => {
                      setAssignTaskForm(prev => ({ ...prev, employeeId: employee._id }));
                      setEmployeeSearch(employee.fullName);
                      setShowDropdown(false);
                    }}
                  >
                    {employee.fullName}
                  </button>
              ))}
            </div>
          )}
        </Form.Group>
        <Form.Group className="mb-3">
                <Form.Label>Nội dung công việc</Form.Label>
                <Form.Control 
                  as="textarea"
                  rows={3}
                  name="content"
                  value={assignTaskForm.content}
                  onChange={handleAssignTaskChange}
                  placeholder="Nhập nội dung công việc"
                  required
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Dự án</Form.Label>
                <Form.Control 
                  as="textarea"
                  rows={3}
                  name="project"
                  value={assignTaskForm.project}
                  onChange={handleAssignTaskChange}
                  placeholder="Nhập tên dự án"
                  
                />
              </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Phần trăm hoàn thành</Form.Label>
              <Form.Select
                name="completionPercentage"
                value={assignTaskForm.completionPercentage || 0}
                onChange={handleAssignTaskChange}
                className="completion-percentage-select"
              >
                <option value={0}>0% - Chưa bắt đầu</option>
                <option value={10}>10% - Bắt đầu</option>
                <option value={20}>20% - Tiến triển nhẹ</option>
                <option value={30}>30% - Tiến triển</option>
                <option value={40}>40% - Gần nửa chừng</option>
                <option value={50}>50% - Nửa chừng</option>
                <option value={60}>60% - Hơn nửa</option>
                <option value={70}>70% - Tiến triển tốt</option>
                <option value={80}>80% - Gần hoàn thành</option>
                <option value={90}>90% - Sắp hoàn thành</option>
                <option value={100}>100% - Hoàn thành</option>
              </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Ngày hết hạn</Form.Label>
              <Form.Control type="date" name="dueDate" value={assignTaskForm.dueDate} onChange={handleAssignTaskChange} required />
            </Form.Group>
            <Button variant="primary" type="submit" disabled={buttonDisabled}>
                {buttonDisabled ? 'Đang giao việc...' : 'Giao việc'}
              </Button>
            <Button variant="secondary" className="ms-2" onClick={() => setShowAssignTaskModal(false)}>Hủy</Button>
          </Form>
        </Modal.Body>
      </Modal>

      <Modal show={showImageModal} onHide={() => setShowImageModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Xem ảnh hoàn thành</Modal.Title>
        </Modal.Header>
        <Modal.Body className="text-center">
          {selectedImage && (
            <img
              src={selectedImage}
              alt="Enlarged Task Image"
              style={{ maxWidth: '100%', maxHeight: '70vh', objectFit: 'contain' }}
            />
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowImageModal(false)}>Đóng</Button>
        </Modal.Footer>
      </Modal>
      
      {/* Modal for editing a task */}
      <Modal show={showEditTaskModal} onHide={() => setShowEditTaskModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Cập nhật công việc</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleEditTaskSubmit}>
            <Form.Group className="mb-3 position-relative">
              <Form.Label>Chọn người nhận việc</Form.Label>
              <Form.Control
                type="text"
                placeholder="Tìm kiếm người nhận..."
                value={employeeSearch}
                onChange={(e) => {
                  setEmployeeSearch(e.target.value);
                  setShowDropdown(true);
                }}
                onFocus={() => setShowDropdown(true)}
                autoComplete="off"
              />
              {showDropdown && employeeSearch && (
                <div className="dropdown-menu show suggestion-dropdown" style={{ width: '100%' }}>
                  <div className="dropdown-header">Nhân viên</div>
                  {employees
                    .filter(e => e.role === 'EMPLOYEE' && e.fullName.toLowerCase().includes(employeeSearch.toLowerCase()))
                    .map(employee => (
                      <button
                        key={employee._id}
                        type="button"
                        className="dropdown-item"
                        onMouseDown={(e) => e.preventDefault()}
                        onClick={() => {
                          setEditTaskForm(prev => ({ ...prev, employeeId: employee._id }));
                          setEmployeeSearch(employee.fullName);
                          setShowDropdown(false);
                        }}
                      >
                        {employee.fullName}
                      </button>
                  ))}
                  <div className="dropdown-header">Quản lý báo cáo của nhân viên</div>
                  {employees
                    .filter(e => e.role === 'REPORT_MANAGER' && e.fullName.toLowerCase().includes(employeeSearch.toLowerCase()))
                    .map(employee => (
                      <button
                        key={employee._id}
                        type="button"
                        className="dropdown-item"
                        onMouseDown={(e) => e.preventDefault()}
                        onClick={() => {
                          setEditTaskForm(prev => ({ ...prev, employeeId: employee._id }));
                          setEmployeeSearch(employee.fullName);
                          setShowDropdown(false);
                        }}
                      >
                        {employee.fullName}
                      </button>
                  ))}
                  <div className="dropdown-header">Quản lý</div>
                  {employees
                    .filter(e => e.role === 'LEVEL_II_MANAGER' && e.fullName.toLowerCase().includes(employeeSearch.toLowerCase()))
                    .map(employee => (
                      <button
                        key={employee._id}
                        type="button"
                        className="dropdown-item"
                        onMouseDown={(e) => e.preventDefault()}
                        onClick={() => {
                          setEditTaskForm(prev => ({ ...prev, employeeId: employee._id }));
                          setEmployeeSearch(employee.fullName);
                          setShowDropdown(false);
                        }}
                      >
                        {employee.fullName}
                      </button>
                  ))}
                  <div className="dropdown-header">Quản trị viên hệ thống</div>
                  {employees
                    .filter(e => e.role === 'SUPER_ADMIN' && e.fullName.toLowerCase().includes(employeeSearch.toLowerCase()))
                    .map(employee => (
                      <button
                        key={employee._id}
                        type="button"
                        className="dropdown-item"
                        onMouseDown={(e) => e.preventDefault()}
                        onClick={() => {
                          setEditTaskForm(prev => ({ ...prev, employeeId: employee._id }));
                          setEmployeeSearch(employee.fullName);
                          setShowDropdown(false);
                        }}
                      >
                        {employee.fullName}
                      </button>
                  ))}
                </div>
              )}
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Nội dung công việc</Form.Label>
              <Form.Control 
                as="textarea"
                rows={3}
                name="content"
                value={editTaskForm.content}
                onChange={handleEditTaskChange}
                placeholder="Nhập nội dung công việc"
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Dự án</Form.Label>
              <Form.Control 
                as="textarea"
                rows={3}
                name="project"
                value={editTaskForm.project}
                onChange={handleEditTaskChange}
                placeholder="Nhập tên dự án"
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Phần trăm hoàn thành</Form.Label>
              <Form.Select
                name="completionPercentage"
                value={editTaskForm.completionPercentage || 0}
                onChange={handleEditTaskChange}
                className="completion-percentage-select"
              >
                <option value={0}>0% - Chưa bắt đầu</option>
                <option value={10}>10% - Bắt đầu</option>
                <option value={20}>20% - Tiến triển nhẹ</option>
                <option value={30}>30% - Tiến triển</option>
                <option value={40}>40% - Gần nửa chừng</option>
                <option value={50}>50% - Nửa chừng</option>
                <option value={60}>60% - Hơn nửa</option>
                <option value={70}>70% - Tiến triển tốt</option>
                <option value={80}>80% - Gần hoàn thành</option>
                <option value={90}>90% - Sắp hoàn thành</option>
                <option value={100}>100% - Hoàn thành</option>
              </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Ngày hết hạn</Form.Label>
              <Form.Control
                type="date"
                name="dueDate"
                value={editTaskForm.dueDate}
                onChange={handleEditTaskChange}
                required
              />
            </Form.Group>
            <Button variant="primary" type="submit" disabled={buttonDisabled}>
              {buttonDisabled ? 'Đang cập nhật...' : 'Cập nhật công việc'}
            </Button>
            <Button variant="secondary" className="ms-2" onClick={() => setShowEditTaskModal(false)}>Hủy</Button>
          </Form>
        </Modal.Body>
      </Modal>
      
      {/* Modal for deleting a task */}
      <Modal show={showDeleteTaskModal} onHide={() => setShowDeleteTaskModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Xác nhận xóa công việc</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Bạn có chắc chắn muốn xóa công việc này?</p>
          {taskToDelete && (
            <div className="task-preview p-3 bg-light rounded">
              <p><strong>Nội dung:</strong> {taskToDelete.content}</p>
              <p><strong>Dự án:</strong> {taskToDelete.project}</p>
              <p><strong>Giao cho:</strong> {taskToDelete.employeeId?.fullName}</p>
            </div>
          )}
          <p className="text-danger mt-3">Lưu ý: Hành động này không thể hoàn tác và nhân viên sẽ nhận được thông báo về việc công việc bị xóa.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteTaskModal(false)}>Hủy</Button>
          <Button variant="danger" onClick={handleDeleteTask} disabled={buttonDisabled}>
            {buttonDisabled ? 'Đang xóa...' : 'Xóa công việc'}
          </Button>
        </Modal.Footer>
      </Modal>
      
      {/* Modal for batch deleting tasks */}
      <Modal show={showBatchDeleteModal} onHide={() => setShowBatchDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Xác nhận xóa nhiều công việc</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Bạn có chắc chắn muốn xóa <strong>{selectedTasks.length}</strong> công việc đã chọn?</p>
          <div className="selected-tasks-preview">
            <h6>Danh sách công việc sẽ bị xóa:</h6>
            <ul className="list-group mt-2 mb-3" style={{ maxHeight: '200px', overflowY: 'auto' }}>
              {paginatedManagerAssignedTasks
                .filter(task => selectedTasks.includes(task._id))
                .map(task => (
                  <li key={task._id} className="list-group-item">
                    <strong>{task.employeeId?.fullName || 'Không xác định'}</strong>: {task.content.substring(0, 50)}{task.content.length > 50 ? '...' : ''}
                  </li>
                ))
              }
            </ul>
          </div>
          <p className="text-danger">Lưu ý: Hành động này không thể hoàn tác và các nhân viên sẽ nhận được thông báo về việc công việc bị xóa.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowBatchDeleteModal(false)}>Hủy</Button>
          <Button variant="danger" onClick={handleBatchDeleteTasks} disabled={buttonDisabled}>
            {buttonDisabled ? 
              <><i className="fas fa-spinner fa-spin me-2"></i>Đang xóa...</> : 
              <><i className="fas fa-trash me-2"></i>Xóa {selectedTasks.length} công việc</>
            }
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Add this modal at the end of your component, before the final closing tag */}
      <Modal 
        show={showTaskModal} 
        onHide={() => {
          console.log('Modal closed');
          setShowTaskModal(false);
        }} 
        size="lg"
        className="task-modal"
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Chi tiết công việc</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedTask && (() => {
            console.log('Modal selectedTask:', selectedTask);
            console.log('Modal selectedTask.report:', selectedTask.report);
            console.log('Modal selectedTask.report.managers:', selectedTask.report?.managers);
            
            return (
              <div className="task-details">
                <div className="row">
                <div className="col-md-8">
                  <h5>Thông tin chung</h5>
                  {selectedTask.report?.user?.fullName && (
                    <div className="d-flex align-items-center mb-2">
                      <i className="fas fa-user-circle me-2" style={{ color: '#2563eb', fontSize: '1.1rem' }}></i>
                      <strong>Người thực hiện:</strong>
                      <span className="ms-2">{selectedTask.report.user.fullName}</span>
                    </div>
                  )}
                  
                  <div className="d-flex align-items-center mb-2">
                    <i className="fas fa-calendar-alt me-2" style={{ color: '#2563eb', fontSize: '1.1rem' }}></i>
                    <strong>Ngày cập nhật:</strong>
                    <span className="ms-2">{formatDate(selectedTask.date)}</span>
                  </div>
                  
                  {selectedTask.report?.managers && selectedTask.report.managers.length > 0 && (
                    <div className="d-flex align-items-center mb-2">
                      <i className="fas fa-user-tie me-2" style={{ color: '#2563eb', fontSize: '1.1rem' }}></i>
                      <strong>Người quản lý:</strong>
                      <span className="ms-2">
                        {selectedTask.report.managers.map(manager => {
                          if (typeof manager === 'string') {
                            // Nếu chỉ là ID string, tìm tên từ danh sách managers
                            const foundManager = managers.find(m => String(m._id) === String(manager));
                            return foundManager ? foundManager.fullName : 'Không xác định';
                          }
                          return manager.fullName || manager.name || 'Không xác định';
                        }).join(', ')}
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="col-md-4">
                  <div className="status-section text-md-end">
                    <strong className="d-block d-md-inline mb-1">Trạng thái:</strong>
                    <div className={`status-badge status-${selectedTask.status} d-inline-block ms-md-2`}>
                      {selectedTask.status === 'pending'
                        ? 'Chờ xác nhận'
                        : selectedTask.status === 'accepted'
                          ? 'Đã nhận'
                          : selectedTask.status === 'ongoing'
                            ? 'Tiếp tục'
                            : selectedTask.status === 'paused'
                              ? 'Tạm dừng'
                              : selectedTask.status === 'completed'
                                ? 'Hoàn thành'
                                : 'Hoàn thành'}
                    </div>
                  </div>
                </div>
              </div>
              
              <h5>
                <i className="fas fa-tasks me-2" style={{ color: '#2563eb' }}></i>
                Thông tin công việc
              </h5>
              
              <div className="row mb-3">
                <div className="col-md-6">
                  <p><strong>Từ ngày:</strong> {formatDate(selectedTask.startTime)}</p>
                </div>
                <div className="col-md-6">
                  <p><strong>Đến ngày:</strong> {formatDate(selectedTask.endTime)}</p>
                </div>
              </div>
              
              <div className="mb-3">
                <p>
                  <i className="fas fa-project-diagram me-2" style={{ color: '#2563eb' }}></i>
                  <strong>Dự án:</strong>
                </p>
                <div className="p-3 bg-light rounded">
                  {selectedTask.project || 'N/A'}
                </div>
              </div>
              
              <div className="mb-3">
                <p>
                  <i className="fas fa-file-alt me-2" style={{ color: '#2563eb' }}></i>
                  <strong>Nội dung công việc:</strong>
                </p>
                <div className="p-3 bg-light rounded">
                  {selectedTask.content}
                </div>
              </div>
              
              <div className="row">
                
                <div className="col-md-6">
                  <p>
                    <i className="fas fa-users me-2" style={{ color: '#2563eb' }}></i>
                    <strong>Cá nhân phối hợp:</strong>
                  </p>
                  <div className="p-3 bg-light rounded">
                    {renderCollaborators(selectedTask.collaborator, employees)}
                  </div>
                </div>
              </div>
              
              <div className="mb-3">
                <p>
                  <i className="fas fa-chart-line me-2" style={{ color: '#2563eb' }}></i>
                  <strong>Tiến độ công việc:</strong>
                </p>
                <div className="p-3 bg-light rounded">
                  {selectedTask.progress || '-'}
                </div>
              </div>

              <div className="mb-3">
                <p>
                  <i className="fas fa-percentage me-2" style={{ color: '#2563eb' }}></i>
                  <strong>Phần trăm hoàn thành:</strong>
                </p>
                <div className="p-3 bg-light rounded">
                  <div className="completion-percentage-display-modal">
                    <div className="d-flex align-items-center justify-content-between">
                      <div className="completion-badge-large">
                        <span className="percentage-text-large">
                          {selectedTask.completionPercentage || 0}%
                        </span>
                      </div>
                      <div className="progress-bar-container-large">
                        <div
                          className="progress-bar-fill-large"
                          style={{
                            width: `${selectedTask.completionPercentage || 0}%`,
                            backgroundColor:
                              (selectedTask.completionPercentage || 0) === 100 ? '#28a745' :
                              (selectedTask.completionPercentage || 0) >= 80 ? '#20c997' :
                              (selectedTask.completionPercentage || 0) >= 60 ? '#ffc107' :
                              (selectedTask.completionPercentage || 0) >= 40 ? '#fd7e14' :
                              (selectedTask.completionPercentage || 0) >= 20 ? '#dc3545' : '#6c757d'
                          }}
                        ></div>
                      </div>
                    </div>
                    <div className="completion-status-text mt-2">
                      <small className="text-muted">
                        {(selectedTask.completionPercentage || 0) === 0 ? 'Chưa bắt đầu' :
                         (selectedTask.completionPercentage || 0) === 10 ? 'Bắt đầu' :
                         (selectedTask.completionPercentage || 0) === 20 ? 'Tiến triển nhẹ' :
                         (selectedTask.completionPercentage || 0) === 30 ? 'Tiến triển' :
                         (selectedTask.completionPercentage || 0) === 40 ? 'Gần nửa chừng' :
                         (selectedTask.completionPercentage || 0) === 50 ? 'Nửa chừng' :
                         (selectedTask.completionPercentage || 0) === 60 ? 'Hơn nửa' :
                         (selectedTask.completionPercentage || 0) === 70 ? 'Tiến triển tốt' :
                         (selectedTask.completionPercentage || 0) === 80 ? 'Gần hoàn thành' :
                         (selectedTask.completionPercentage || 0) === 90 ? 'Sắp hoàn thành' :
                         (selectedTask.completionPercentage || 0) === 100 ? 'Hoàn thành' : 'Đang thực hiện'}
                      </small>
                    </div>
                  </div>
                </div>
              </div>

              {selectedTask.images && selectedTask.images.length > 0 && (
                <div className="mt-3">
                  <p>
                    <i className="fas fa-images me-2" style={{ color: '#2563eb' }}></i>
                    <strong>Ảnh công việc:</strong>
                  </p>
                  <div className="d-flex flex-wrap">
                    {selectedTask.images.map((img, idx) => (
                      <img
                        key={idx}
                        src={img.startsWith('http') ? img : `${backendUrl}${img}`}
                        alt="Task Image"
                        className="me-2 mb-2"
                        style={{ width: '100px', height: '100px', objectFit: 'cover', cursor: 'pointer' }}
                        onClick={() => openImageModal(img.startsWith('http') ? img : `${backendUrl}${img}`)}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Phần bình luận */}
              <div className="mt-4">
                <h5>
                  <i className="fas fa-comments me-2" style={{ color: '#2563eb' }}></i>
                  Bình luận
                </h5>
                
                {/* Thông báo về upload file báo cáo */}
                <div className="alert alert-info mb-3" style={{ fontSize: '14px', padding: '10px 15px' }}>
                  <i className="fas fa-info-circle me-2"></i>
                  <strong>Lưu ý:</strong> Nếu nộp file báo cáo đã hoàn thành, vui lòng upload lên Google Drive và gửi link download vào bình luận.
                </div>
                
                {/* Form thêm bình luận - chỉ hiển thị nếu có quyền */}
                {canCommentOnTask(selectedTask) && (
                  <div className="mb-3">
                    <div className="d-flex">
                      <Form.Control
                        as="textarea"
                        rows={2}
                        placeholder="Nhập bình luận của bạn... (Ctrl+Enter để gửi)"
                        value={newTaskComment}
                        onChange={(e) => setNewTaskComment(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && e.ctrlKey && !submittingTaskComment && newTaskComment.trim()) {
                            e.preventDefault();
                            submitTaskComment();
                          }
                        }}
                        disabled={submittingTaskComment}
                        className="me-2"
                      />
                      <Button
                        variant="primary"
                        onClick={submitTaskComment}
                        disabled={submittingTaskComment || !newTaskComment.trim()}
                        style={{ minWidth: '100px' }}
                      >
                        {submittingTaskComment ? (
                          <><i className="fas fa-spinner fa-spin me-1"></i>Gửi</>
                        ) : (
                          <><i className="fas fa-paper-plane me-1"></i>Gửi</>
                        )}
                      </Button>
                    </div>
                  </div>
                )}

                {/* Danh sách bình luận */}
                <div className="comments-section" style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {loadingTaskComments ? (
                    <div className="text-center py-3">
                      <i className="fas fa-spinner fa-spin me-2"></i>
                      Đang tải bình luận...
                    </div>
                  ) : taskComments.length === 0 ? (
                    <div className="text-muted text-center py-3">
                      <i className="fas fa-comment-slash me-2"></i>
                      Chưa có bình luận nào
                    </div>
                  ) : (
                    [...taskComments].reverse().map((comment) => (
                      <div key={comment._id} className="comment-item mb-3 p-3 border rounded">
                        <div className="d-flex justify-content-between align-items-start">
                          <div className="flex-grow-1">
                            <div className="d-flex align-items-center mb-2">
                              <strong className="me-2">{comment.author?.fullName || 'Người dùng'}</strong>
                              <small className="text-muted">
                                {new Date(comment.createdAt).toLocaleString('vi-VN')}
                              </small>
                            </div>
                            <p className="mb-0">{comment.content}</p>
                          </div>
                          {/* Chỉ cho phép xóa bình luận của chính mình hoặc nếu là quản lý */}
                          {(comment.author?._id === user.id || comment.author?._id === user._id || canManageReports) && (
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => {
                                if (window.confirm('Bạn có chắc chắn muốn xóa bình luận này?')) {
                                  deleteTaskComment(comment._id);
                                }
                              }}
                              className="ms-2"
                            >
                              <i className="fas fa-trash"></i>
                            </Button>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
            );
          })()}
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-between">
          <div className="action-buttons-modal">
            {selectedTask && selectedTask.report && (() => {
              const report = selectedTask.report;
              const isOwner = report.user && (report.user._id === user.id || report.user._id === user._id);
              const canEdit = canManageReports || isOwner;
              const canDelete = isSuperAdmin || isOwner;
              const canShowActionButton = isOwner && selectedTask.status !== 'completed';
              const isPaused = selectedTask.status === 'paused';
              const isOngoing = selectedTask.status === 'ongoing';
              
              return (
                <>
                  {canShowActionButton && isPaused && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => handleResumeTask(selectedTask)}
                      className="me-2"
                    >
                      <i className="fas fa-play me-1"></i>
                      Tiếp tục
                    </Button>
                  )}
                  {canShowActionButton && isOngoing && (
                    <Button
                      variant="success"
                      size="sm"
                      onClick={() => handleCompleteTask(selectedTask)}
                      className="me-2"
                    >
                      <i className="fas fa-check-circle me-1"></i>
                      Hoàn thành
                    </Button>
                  )}
                  {canEdit && (
                    <Button
                      variant="warning"
                      size="sm"
                      onClick={() => {
                        setShowTaskModal(false);
                        handleEdit(selectedTask.report);
                      }}
                      className="me-2"
                    >
                      <i className="fas fa-edit me-1"></i>
                      Sửa báo cáo
                    </Button>
                  )}
                  <Button
                    variant="info"
                    size="sm"
                    onClick={() => {
                      setShowTaskModal(false);
                      handleCopyReport(selectedTask.report);
                    }}
                    className="me-2"
                  >
                    <i className="fas fa-copy me-1"></i>
                    Sao chép báo cáo
                  </Button>
                  {canDelete && (
                    <>
                      <Button
                        variant="danger"
                        size="sm"
                        onClick={() => {
                          setShowTaskModal(false);
                          openDeleteModal(selectedTask.report);
                        }}
                        className="me-2"
                      >
                        <i className="fas fa-trash me-1"></i>
                        Xóa báo cáo
                      </Button>
                    </>
                  )}
                </>
              );
            })()}
          </div>
          <Button variant="secondary" onClick={() => setShowTaskModal(false)}>
            <i className="fas fa-times me-2"></i>
            Đóng
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal xuất báo cáo */}
      <Modal show={showExportModal} onHide={() => setShowExportModal(false)} size="lg">
        <Modal.Header closeButton className="bg-success text-white">
          <Modal.Title>
            <i className="fas fa-file-export me-2"></i> Xuất báo cáo tùy chỉnh
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Định dạng xuất</Form.Label>
                  <Form.Select 
                    name="format" 
                    value={exportOptions.format} 
                    onChange={handleExportOptionChange}
                  >
                    <option value="excel">Excel (.xlsx)</option>
                    <option value="pdf">PDF (.pdf)</option>
                  </Form.Select>
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Loại báo cáo</Form.Label>
                  <Form.Select 
                    name="reportType" 
                    value={exportOptions.reportType} 
                    onChange={handleExportOptionChange}
                    disabled={true} // Đã được thiết lập khi mở modal
                  >
                    <option value="manage">Báo cáo của nhân viên</option>

                    <option value="all-tasks">Danh sách công việc</option>
                    <option value="assigned">Công việc được giao</option>
                    <option value="draft">Báo cáo nháp</option>
                  </Form.Select>
                </Form.Group>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Khoảng thời gian</Form.Label>
                  <Form.Select 
                    name="dateRange" 
                    value={exportOptions.dateRange} 
                    onChange={handleExportOptionChange}
                  >
                    <option value="all">Tất cả thời gian</option>
                    <option value="thisWeek">Tuần này</option>
                    <option value="thisMonth">Tháng này</option>
                    <option value="lastMonth">Tháng trước</option>
                    <option value="custom">Tùy chỉnh</option>
                  </Form.Select>
                </Form.Group>
              </div>
              
              {exportOptions.dateRange === 'custom' && (
                <div className="col-md-6">
                  <div className="row">
                    <div className="col-md-6">
                      <Form.Group className="mb-3">
                        <Form.Label>Từ ngày</Form.Label>
                        <Form.Control 
                          type="date" 
                          name="customDateFrom" 
                          value={exportOptions.customDateFrom} 
                          onChange={handleExportOptionChange}
                        />
                      </Form.Group>
                    </div>
                    <div className="col-md-6">
                      <Form.Group className="mb-3">
                        <Form.Label>Đến ngày</Form.Label>
                        <Form.Control 
                          type="date" 
                          name="customDateTo" 
                          value={exportOptions.customDateTo} 
                          onChange={handleExportOptionChange}
                        />
                      </Form.Group>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Nhân viên</Form.Label>
                  <Form.Select 
                    name="selectedEmployees" 
                    value={exportOptions.selectedEmployees} 
                    onChange={handleExportOptionChange}
                    multiple
                    size={5}
                  >
                    <option value="">Tất cả nhân viên</option>
                    {uniqueUsers.map(user => (
                      <option key={user._id} value={user._id}>{user.fullName}</option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    Giữ Ctrl để chọn nhiều nhân viên
                  </Form.Text>
                </Form.Group>
              </div>
              
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Dự án</Form.Label>
                  <Form.Select 
                    name="selectedProjects" 
                    value={exportOptions.selectedProjects} 
                    onChange={handleExportOptionChange}
                    multiple
                    size={5}
                  >
                    <option value="">Tất cả dự án</option>
                    {uniqueProjects.map(project => (
                      <option key={project} value={project}>{project}</option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    Giữ Ctrl để chọn nhiều dự án
                  </Form.Text>
                </Form.Group>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Trạng thái</Form.Label>
                  <Form.Select 
                    name="selectedStatuses" 
                    value={exportOptions.selectedStatuses} 
                    onChange={handleExportOptionChange}
                    multiple
                    size={3}
                  >
                    <option value="">Tất cả trạng thái</option>
                    {uniqueStatuses.map(status => (
                      <option key={status} value={status}>{status}</option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    Giữ Ctrl để chọn nhiều trạng thái
                  </Form.Text>
                </Form.Group>
              </div>
              
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Tùy chọn bổ sung</Form.Label>
                  <div className="d-flex flex-column">
                    <Form.Check 
                      type="checkbox"
                      id="include-comments"
                      label="Bao gồm bình luận"
                      name="includeComments"
                      checked={exportOptions.includeComments}
                      onChange={handleExportOptionChange}
                      className="mb-2"
                    />
                    <Form.Check 
                      type="checkbox"
                      id="include-images"
                      label="Bao gồm hình ảnh (chỉ áp dụng cho PDF)"
                      name="includeImages"
                      checked={exportOptions.includeImages}
                      onChange={handleExportOptionChange}
                      disabled={exportOptions.format !== 'pdf'}
                    />
                  </div>
                </Form.Group>
              </div>
            </div>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowExportModal(false)}>
            Hủy
          </Button>
          <Button variant="success" onClick={handleExport}>
            <i className="fas fa-file-export me-2"></i> Xuất báo cáo
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal chi tiết báo cáo cho collaborator */}
      <Modal show={showReportDetailModal} onHide={() => setShowReportDetailModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-file-alt me-2"></i>
            Chi tiết báo cáo công việc
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedReport && (
            <div>
              <div className="mb-4">
                <h5 className="text-primary mb-3">
                  <i className="fas fa-info-circle me-2"></i>
                  Thông tin chung
                </h5>
                <div className="row">
                  <div className="col-md-6">
                    <p><strong>Báo cáo của:</strong> {selectedReport.user?.fullName || 'N/A'}</p>
                    <p><strong>Ngày cập nhật:</strong> {formatDate(selectedReport.updatedAt || selectedReport.createdAt)}</p>
                  </div>
                  <div className="col-md-6">
                    <p><strong>Trạng thái:</strong> 
                      <Badge 
                        bg={selectedReport.approvalStatus === 'approved' ? 'success' : 
                            selectedReport.approvalStatus === 'rejected' ? 'danger' : 'warning'}
                        className="ms-2"
                      >
                        {selectedReport.approvalStatus === 'approved' ? 'Đã duyệt' : 
                         selectedReport.approvalStatus === 'rejected' ? 'Từ chối' : 'Chờ duyệt'}
                      </Badge>
                    </p>
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <h5 className="text-primary mb-3">
                  <i className="fas fa-tasks me-2"></i>
                  Thông tin công việc
                </h5>
                {selectedReport.tasks && selectedReport.tasks.map((task, index) => (
                  <div key={index} className="card mb-3">
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Từ ngày:</strong> {formatDate(task.date)}</p>
                          <p><strong>Đến ngày:</strong> {task.endTime ? formatDate(task.endTime) : 'N/A'}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Dự án:</strong></p>
                          <div className="bg-light p-2 rounded mb-2">
                            {task.project || 'Không xác định'}
                          </div>
                        </div>
                      </div>
                      
                      <div className="mb-3">
                        <p><strong>Nội dung công việc:</strong></p>
                        <div className="bg-light p-3 rounded">
                          {task.content}
                        </div>
                      </div>

                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Cá nhân phối hợp:</strong></p>
                          <div className="bg-light p-2 rounded mb-2">
                            {renderCollaborators(task.collaborator, employees)}
                          </div>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Tiến độ công việc:</strong></p>
                          <div className="bg-light p-2 rounded mb-2">
                            {task.progress || 'Chưa cập nhật'}
                          </div>
                        </div>
                      </div>

                      {task.images && task.images.length > 0 && (
                        <div className="mt-3">
                          <p><strong>Hình ảnh đính kèm:</strong></p>
                          <div className="d-flex flex-wrap gap-2">
                            {task.images.map((image, imgIndex) => (
                              <img
                                key={imgIndex}
                                src={`${backendUrl}${image}`}
                                alt={`Task ${index + 1} - Image ${imgIndex + 1}`}
                                className="img-thumbnail"
                                style={{ width: '100px', height: '100px', objectFit: 'cover', cursor: 'pointer' }}
                                onClick={() => {
                                  setSelectedImage(`${backendUrl}${image}`);
                                  setShowImageModal(true);
                                }}
                              />
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mb-3">
                <h5 className="text-primary mb-3">
                  <i className="fas fa-comments me-2"></i>
                  Bình luận
                </h5>
                <p className="text-muted">
                  <i className="fas fa-info-circle me-2"></i>
                  Lưu ý: Nếu nộp file báo cáo đã hoàn thành, vui lòng upload lên Google Drive và gửi link download vào bình luận.
                </p>
                
                {/* Form thêm bình luận */}
                <div className="mb-3">
                  <textarea
                    className="form-control"
                    rows="3"
                    placeholder="Nhập bình luận của bạn..."
                    value={commentData[selectedReport._id] || ''}
                    onChange={(e) => setCommentData({
                      ...commentData,
                      [selectedReport._id]: e.target.value
                    })}
                  />
                  <div className="mt-2">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={(e) => setSelectedImages({
                        ...selectedImages,
                        [selectedReport._id]: Array.from(e.target.files)
                      })}
                      className="form-control mb-2"
                    />
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => handleAddComment(selectedReport._id)}
                      disabled={!commentData[selectedReport._id]?.trim()}
                    >
                      <i className="fas fa-paper-plane me-2"></i>
                      Gửi
                    </Button>
                  </div>
                </div>

                {/* Hiển thị bình luận */}
                <div className="comments-section">
                  <h6 className="mb-3">
                    <i className="fas fa-comment me-2"></i>
                    Bình luận ({selectedReport.comments?.length || 0})
                  </h6>
                  
                  {!selectedReport.comments || selectedReport.comments.length === 0 ? (
                    <div className="text-muted text-center py-3">
                      <i className="fas fa-comment-slash me-2"></i>
                      Chưa có bình luận nào
                    </div>
                  ) : (
                    <div className="comments-list">
                      {[...selectedReport.comments].reverse().map((comment) => (
                        <div key={comment._id} className="comment-item mb-3 p-3 border rounded">
                          <div className="d-flex justify-content-between align-items-start">
                            <div className="flex-grow-1">
                              <div className="d-flex align-items-center mb-2">
                                <strong className="me-2">{comment.user?.fullName || 'Người dùng'}</strong>
                                <small className="text-muted">
                                  {new Date(comment.createdAt).toLocaleString('vi-VN')}
                                </small>
                              </div>
                              <p className="mb-0">{comment.content}</p>
                              {comment.images && comment.images.length > 0 && (
                                <div className="d-flex flex-wrap gap-1 mt-2">
                                  {comment.images.map((image, imgIndex) => (
                                    <img
                                      key={imgIndex}
                                      src={`${backendUrl}${image}`}
                                      alt={`Comment Image ${imgIndex + 1}`}
                                      className="img-thumbnail"
                                      style={{ width: '60px', height: '60px', objectFit: 'cover', cursor: 'pointer' }}
                                      onClick={() => {
                                        setSelectedImage(`${backendUrl}${image}`);
                                        setShowImageModal(true);
                                      }}
                                    />
                                  ))}
                                </div>
                              )}
                            </div>
                            {/* Chỉ cho phép xóa bình luận của chính mình hoặc nếu là quản lý */}
                            {(comment.user?._id === user.id || comment.user?._id === user._id || canManageReports) && (
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => handleDeleteComment(selectedReport._id, comment._id)}
                                title="Xóa bình luận"
                              >
                                <i className="fas fa-trash"></i>
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowReportDetailModal(false)}>
            <i className="fas fa-times me-2"></i>
            Đóng
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal bình luận báo cáo */}
      <Modal show={showCommentsModal} onHide={() => setShowCommentsModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-comments me-2"></i>
            Bình luận {selectedReport && formatDate(selectedReport.weekStart)}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedReport && (
            <>
              {/* Hiển thị thông tin báo cáo */}
              <div className="mb-3 p-3 bg-light rounded">
                <h6>Báo cáo của: {selectedReport.user?.fullName}</h6>
              </div>

              {/* Thông báo về upload file báo cáo */}
              <div className="alert alert-info mb-3" style={{ fontSize: '14px', padding: '10px 15px' }}>
                <i className="fas fa-info-circle me-2"></i>
                <strong>Lưu ý:</strong> Nếu nộp file báo cáo đã hoàn thành, vui lòng upload lên Google Drive và gửi link download vào bình luận.
              </div>

              {/* Form thêm bình luận mới */}
              <Form className="mb-4">
                <Form.Group>
                  <Form.Label>Thêm bình luận mới</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    placeholder="Nhập bình luận của bạn..."
                    value={commentData[selectedReport._id] || ''}
                    onChange={(e) => setCommentData(prev => ({
                      ...prev,
                      [selectedReport._id]: e.target.value
                    }))}
                  />
                </Form.Group>
                <Button 
                  variant="primary" 
                  className="mt-2"
                  onClick={() => handleAddComment(selectedReport._id)}
                  disabled={!commentData[selectedReport._id]?.trim()}
                >
                  <i className="fas fa-paper-plane me-2"></i>
                  Gửi bình luận
                </Button>
              </Form>

              {/* Danh sách bình luận */}
              <div className="comments-section">
                <h6 className="mb-3">
                  <i className="fas fa-comment me-2"></i>
                  Bình luận ({selectedReport.comments?.length || 0})
                </h6>
                
                {!selectedReport.comments || selectedReport.comments.length === 0 ? (
                  <div className="text-muted text-center py-3">
                    <i className="fas fa-comment-slash me-2"></i>
                    Chưa có bình luận nào
                  </div>
                ) : (
                  <div className="comments-list">
                    {[...selectedReport.comments].reverse().map((comment) => (
                      <div key={comment._id} className="comment-item mb-3 p-3 border rounded">
                        <div className="d-flex justify-content-between align-items-start">
                          <div className="flex-grow-1">
                            <div className="d-flex align-items-center mb-2">
                              <strong className="me-2">{comment.user?.fullName || 'Người dùng'}</strong>
                              <small className="text-muted">
                                {new Date(comment.createdAt).toLocaleString('vi-VN')}
                              </small>
                            </div>
                            <p className="mb-0">{comment.content}</p>
                          </div>
                          {/* Chỉ cho phép xóa bình luận của chính mình hoặc nếu là quản lý */}
                          {(comment.user?._id === user.id || comment.user?._id === user._id || canManageReports) && (
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleDeleteComment(selectedReport._id, comment._id)}
                              title="Xóa bình luận"
                            >
                              <i className="fas fa-trash"></i>
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCommentsModal(false)}>
            <i className="fas fa-times me-2"></i>
            Đóng
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default WorkReports;
