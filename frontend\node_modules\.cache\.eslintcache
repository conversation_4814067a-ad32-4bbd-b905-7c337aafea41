[{"E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\index.js": "1", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\App.js": "2", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\UploadsRedirect.js": "3", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\context\\AuthContext.js": "4", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\AssetManagement.js": "5", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\Preloader.js": "6", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\Navbar.js": "7", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\ws.js": "8", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\Register.js": "9", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ContractPage.js": "10", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\IncidentForm.js": "11", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\WelcomePage.js": "12", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\Login.js": "13", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\RequestForm.js": "14", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ReturnForm.js": "15", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ReportPage.js": "16", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\EmployeeManagement.js": "17", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\IncidentReports.js": "18", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ApprovalDashboard.js": "19", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SupportFeedbackForm.js": "20", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\HumanResources.js": "21", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SupportRequestsAdmin.js": "22", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SupportRequestsUser.js": "23", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\VehicleManagement.js": "24", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SuperAdminDashboard.js": "25", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\Reception.js": "26", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\UserProfile.js": "27", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ReturnReports.js": "28", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\WorkReports.js": "29", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\LeaveRequestForm.js": "30", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AdminWorkManagement.js": "31", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\LeaveRequest.js": "32", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\DriverProfiles.js": "33", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\UserGuide.js": "34", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AllNotifications.js": "35", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\api.js": "36", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationBell.js": "37", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\translation.js": "38", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\CompanyDocuments.js": "39", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\CompanyRegulations.js": "40", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\socket.js": "41", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\CreateUser.js": "42", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\constants\\roles.js": "43", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\EmployeeList.js": "44", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AddVehicle.js": "45", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AdminDriverManagementPage.js": "46", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NewNotificationAlert.js": "47", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\notificationService.js": "48", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ActivityLogManagement.js": "49", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\FuelManagement.js": "50", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\formatUtils.js": "51", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\fuel\\FuelRecordForm.js": "52", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\fuel\\FuelStatistics.js": "53", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\config\\config.js": "54", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleRepairHistory.js": "55", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleInsurance.js": "56", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\insuranceService.js": "57", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\PageReloader.js": "58", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\DriverAssignmentModal.js": "59", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\HomeButton.js": "60", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AdminNotificationPage.js": "61", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\Breadcrumbs.js": "62", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\PageHeader.js": "63", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\AssignedDriverInfo.js": "64", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\DriverSchedulePage.js": "65", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AllDriverSchedulesPage.js": "66", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AnnouncementsPage.js": "67", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AdminAnnouncementsPage.js": "68", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\PrivacyPolicy.js": "69", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\DocumentViewer.js": "70", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\SimpleDocumentViewer.js": "71", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\AuthContext.js": "72", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\TodoListPage.js": "73", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\notificationUtils.js": "74", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleDocuments.js": "75", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleImages.js": "76", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\CompanyDocumentsPage.js": "77", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleIssues.js": "78", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\ApiTest.js": "79", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\EmployeeEvaluationPage.js": "80", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\EvaluationForm.js": "81", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\EvaluationList.js": "82", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\employeeEvaluationService.js": "83", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\EvaluationStats.js": "84", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\ManagerSelector.js": "85", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\Notifications.js": "86", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\MultiManagerSelector.js": "87", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\ManagerValidation.js": "88", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\context\\PageVisibilityContext.js": "89", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\pageVisibilityService.js": "90", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\PageVisibilityManagement.js": "91", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\context\\SystemStatusContext.js": "92", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\MaintenanceMode.js": "93", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\systemStatusService.js": "94", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SystemStatusManagement.js": "95", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\admin\\OnlineUsersPage.js": "96", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\WebSocketTracker.js": "97", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\admin\\OnlineUsersWidget.js": "98", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\OnlineUsersBadge.js": "99", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\api.fixed.js": "100", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\admin\\OnlineUsersMonitor.js": "101", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\formatters.js": "102", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\axiosConfig.js": "103", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\NotificationSettings.js": "104", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\serviceWorkerRegistration.js": "105", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationManager.js": "106", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\PushNotificationManager.js": "107", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\NotificationSettingsPage.js": "108", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationSettings.js": "109", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\debugExports.js": "110", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\pwaDetection.js": "111", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\mobile\\MobileAppContainer.js": "112", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\hooks\\usePWA.js": "113", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\WhitelistStatusChecker.js": "114", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\RecruitmentDashboardPage.js": "115", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\CandidatesPage.js": "116", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\InterviewsPage.js": "117", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\RecruitmentRequestsPage.js": "118", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\recruitmentService.js": "119", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\CandidateKanban.js": "120", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\RecruitmentDashboard.js": "121", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\RecruitmentRequestForm.js": "122", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\CandidateForm.js": "123", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\CandidateDetail.js": "124", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\RecruitmentDashboardSimple.js": "125", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\TestRecruitmentAPI.js": "126", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\RecruitmentRequestDetailModal.js": "127", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\InterviewForm.js": "128", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\RecruitmentNavigation.js": "129", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NavigationHandler.js": "130", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationTester.js": "131", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\PushDebugStatus.js": "132", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationNavigationHandler.js": "133", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\ProfileGuard.js": "134", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\CompleteProfilePage.js": "135", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\ProfileCompletion.js": "136", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\hooks\\useProfileCompletion.js": "137", "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\hooks\\useCompanyDepartmentOptions.js": "138"}, {"size": 3207, "mtime": 1753233321969, "results": "139", "hashOfConfig": "140"}, {"size": 31405, "mtime": 1753680324567, "results": "141", "hashOfConfig": "140"}, {"size": 530, "mtime": 1739941601782, "results": "142", "hashOfConfig": "140"}, {"size": 2161, "mtime": 1753677966216, "results": "143", "hashOfConfig": "140"}, {"size": 28989, "mtime": 1747652602116, "results": "144", "hashOfConfig": "140"}, {"size": 749, "mtime": 1746104373866, "results": "145", "hashOfConfig": "140"}, {"size": 39112, "mtime": 1753844788172, "results": "146", "hashOfConfig": "140"}, {"size": 18044, "mtime": 1752034869940, "results": "147", "hashOfConfig": "140"}, {"size": 7710, "mtime": 1753231910334, "results": "148", "hashOfConfig": "140"}, {"size": 342, "mtime": 1744983020072, "results": "149", "hashOfConfig": "140"}, {"size": 6796, "mtime": 1740327018597, "results": "150", "hashOfConfig": "140"}, {"size": 99406, "mtime": 1753148007853, "results": "151", "hashOfConfig": "140"}, {"size": 10481, "mtime": 1751851750979, "results": "152", "hashOfConfig": "140"}, {"size": 22805, "mtime": 1749351751675, "results": "153", "hashOfConfig": "140"}, {"size": 18236, "mtime": 1747974125071, "results": "154", "hashOfConfig": "140"}, {"size": 51403, "mtime": 1747619027174, "results": "155", "hashOfConfig": "140"}, {"size": 12912, "mtime": 1741858395470, "results": "156", "hashOfConfig": "140"}, {"size": 4562, "mtime": 1740210491226, "results": "157", "hashOfConfig": "140"}, {"size": 80184, "mtime": 1753417459191, "results": "158", "hashOfConfig": "140"}, {"size": 5490, "mtime": 1741492403311, "results": "159", "hashOfConfig": "140"}, {"size": 168872, "mtime": 1753925085754, "results": "160", "hashOfConfig": "140"}, {"size": 16286, "mtime": 1747619972317, "results": "161", "hashOfConfig": "140"}, {"size": 14397, "mtime": 1741658838533, "results": "162", "hashOfConfig": "140"}, {"size": 108949, "mtime": 1752547768659, "results": "163", "hashOfConfig": "140"}, {"size": 20721, "mtime": 1750728791496, "results": "164", "hashOfConfig": "140"}, {"size": 18267, "mtime": 1752221729941, "results": "165", "hashOfConfig": "140"}, {"size": 32249, "mtime": 1753681001532, "results": "166", "hashOfConfig": "140"}, {"size": 32645, "mtime": 1747561237308, "results": "167", "hashOfConfig": "140"}, {"size": 315796, "mtime": 1753320460027, "results": "168", "hashOfConfig": "140"}, {"size": 57367, "mtime": 1752308690711, "results": "169", "hashOfConfig": "140"}, {"size": 48183, "mtime": 1751518605506, "results": "170", "hashOfConfig": "140"}, {"size": 126260, "mtime": 1753927451506, "results": "171", "hashOfConfig": "140"}, {"size": 18983, "mtime": 1749617537833, "results": "172", "hashOfConfig": "140"}, {"size": 29453, "mtime": 1746586187714, "results": "173", "hashOfConfig": "140"}, {"size": 25739, "mtime": 1753233309476, "results": "174", "hashOfConfig": "140"}, {"size": 7478, "mtime": 1753680252879, "results": "175", "hashOfConfig": "140"}, {"size": 35826, "mtime": 1753158920814, "results": "176", "hashOfConfig": "140"}, {"size": 1364, "mtime": 1753252720100, "results": "177", "hashOfConfig": "140"}, {"size": 37827, "mtime": 1749614569019, "results": "178", "hashOfConfig": "140"}, {"size": 26946, "mtime": 1747014917522, "results": "179", "hashOfConfig": "140"}, {"size": 3014, "mtime": 1752035261312, "results": "180", "hashOfConfig": "140"}, {"size": 8022, "mtime": 1750641354581, "results": "181", "hashOfConfig": "140"}, {"size": 387, "mtime": 1746619076076, "results": "182", "hashOfConfig": "140"}, {"size": 39798, "mtime": 1748676604988, "results": "183", "hashOfConfig": "140"}, {"size": 11847, "mtime": 1747013544779, "results": "184", "hashOfConfig": "140"}, {"size": 21421, "mtime": 1751427277718, "results": "185", "hashOfConfig": "140"}, {"size": 2602, "mtime": 1753233281996, "results": "186", "hashOfConfig": "140"}, {"size": 42220, "mtime": 1753676648400, "results": "187", "hashOfConfig": "140"}, {"size": 22130, "mtime": 1748307291052, "results": "188", "hashOfConfig": "140"}, {"size": 22527, "mtime": 1749430689961, "results": "189", "hashOfConfig": "140"}, {"size": 3902, "mtime": 1749617513629, "results": "190", "hashOfConfig": "140"}, {"size": 13433, "mtime": 1747799071297, "results": "191", "hashOfConfig": "140"}, {"size": 34185, "mtime": 1747798735916, "results": "192", "hashOfConfig": "140"}, {"size": 2827, "mtime": 1751254539311, "results": "193", "hashOfConfig": "140"}, {"size": 24974, "mtime": 1752546857175, "results": "194", "hashOfConfig": "140"}, {"size": 26704, "mtime": 1752546774967, "results": "195", "hashOfConfig": "140"}, {"size": 1599, "mtime": 1747894880424, "results": "196", "hashOfConfig": "140"}, {"size": 1730, "mtime": 1747899447379, "results": "197", "hashOfConfig": "140"}, {"size": 14067, "mtime": 1748656808497, "results": "198", "hashOfConfig": "140"}, {"size": 3682, "mtime": 1751432978248, "results": "199", "hashOfConfig": "140"}, {"size": 31319, "mtime": 1748394335663, "results": "200", "hashOfConfig": "140"}, {"size": 6711, "mtime": 1753678476961, "results": "201", "hashOfConfig": "140"}, {"size": 901, "mtime": 1748401479654, "results": "202", "hashOfConfig": "140"}, {"size": 3817, "mtime": 1748654313141, "results": "203", "hashOfConfig": "140"}, {"size": 9076, "mtime": 1749353633167, "results": "204", "hashOfConfig": "140"}, {"size": 39376, "mtime": 1749351802907, "results": "205", "hashOfConfig": "140"}, {"size": 21530, "mtime": 1749109029193, "results": "206", "hashOfConfig": "140"}, {"size": 29279, "mtime": 1753231896292, "results": "207", "hashOfConfig": "140"}, {"size": 6032, "mtime": 1748829892713, "results": "208", "hashOfConfig": "140"}, {"size": 11759, "mtime": 1749001838654, "results": "209", "hashOfConfig": "140"}, {"size": 29345, "mtime": 1751256418041, "results": "210", "hashOfConfig": "140"}, {"size": 941, "mtime": 1752650988522, "results": "211", "hashOfConfig": "140"}, {"size": 63083, "mtime": 1752802524128, "results": "212", "hashOfConfig": "140"}, {"size": 9835, "mtime": 1749874824232, "results": "213", "hashOfConfig": "140"}, {"size": 12377, "mtime": 1752546791901, "results": "214", "hashOfConfig": "140"}, {"size": 21448, "mtime": 1752546807403, "results": "215", "hashOfConfig": "140"}, {"size": 25700, "mtime": 1749614618716, "results": "216", "hashOfConfig": "140"}, {"size": 17628, "mtime": 1752546827173, "results": "217", "hashOfConfig": "140"}, {"size": 2073, "mtime": 1749779561473, "results": "218", "hashOfConfig": "140"}, {"size": 24878, "mtime": 1753839504509, "results": "219", "hashOfConfig": "140"}, {"size": 71797, "mtime": 1753841620559, "results": "220", "hashOfConfig": "140"}, {"size": 82188, "mtime": 1753842643761, "results": "221", "hashOfConfig": "140"}, {"size": 7366, "mtime": 1753841601517, "results": "222", "hashOfConfig": "140"}, {"size": 110687, "mtime": 1753842687893, "results": "223", "hashOfConfig": "140"}, {"size": 3601, "mtime": 1749868365898, "results": "224", "hashOfConfig": "140"}, {"size": 8588, "mtime": 1751940300469, "results": "225", "hashOfConfig": "140"}, {"size": 11256, "mtime": 1753841668616, "results": "226", "hashOfConfig": "140"}, {"size": 1230, "mtime": 1750394020934, "results": "227", "hashOfConfig": "140"}, {"size": 6604, "mtime": 1750817855272, "results": "228", "hashOfConfig": "140"}, {"size": 5300, "mtime": 1750816455659, "results": "229", "hashOfConfig": "140"}, {"size": 29371, "mtime": 1750817877710, "results": "230", "hashOfConfig": "140"}, {"size": 10208, "mtime": 1752466467623, "results": "231", "hashOfConfig": "140"}, {"size": 9174, "mtime": 1752466831016, "results": "232", "hashOfConfig": "140"}, {"size": 1760, "mtime": 1751253942824, "results": "233", "hashOfConfig": "140"}, {"size": 23622, "mtime": 1752466531513, "results": "234", "hashOfConfig": "140"}, {"size": 2262, "mtime": 1753847795434, "results": "235", "hashOfConfig": "140"}, {"size": 1230, "mtime": 1752033042923, "results": "236", "hashOfConfig": "140"}, {"size": 3971, "mtime": 1750727624859, "results": "237", "hashOfConfig": "140"}, {"size": 1817, "mtime": 1752035253510, "results": "238", "hashOfConfig": "140"}, {"size": 11168, "mtime": 1750735522305, "results": "239", "hashOfConfig": "140"}, {"size": 21756, "mtime": 1753848113332, "results": "240", "hashOfConfig": "140"}, {"size": 2162, "mtime": 1751598273617, "results": "241", "hashOfConfig": "140"}, {"size": 1018, "mtime": 1751860112712, "results": "242", "hashOfConfig": "140"}, {"size": 719, "mtime": 1751939078515, "results": "243", "hashOfConfig": "140"}, {"size": 22558, "mtime": 1753496177480, "results": "244", "hashOfConfig": "140"}, {"size": 7320, "mtime": 1753494579760, "results": "245", "hashOfConfig": "140"}, {"size": 18608, "mtime": 1753494734743, "results": "246", "hashOfConfig": "140"}, {"size": 7985, "mtime": 1752033046398, "results": "247", "hashOfConfig": "140"}, {"size": 32111, "mtime": 1753675321673, "results": "248", "hashOfConfig": "140"}, {"size": 4542, "mtime": 1751989126012, "results": "249", "hashOfConfig": "140"}, {"size": 3455, "mtime": 1752284415187, "results": "250", "hashOfConfig": "140"}, {"size": 1491, "mtime": 1752284148493, "results": "251", "hashOfConfig": "140"}, {"size": 6255, "mtime": 1752284186346, "results": "252", "hashOfConfig": "140"}, {"size": 0, "mtime": 1752466519853, "results": "253", "hashOfConfig": "140"}, {"size": 1247, "mtime": 1753073608057, "results": "254", "hashOfConfig": "140"}, {"size": 5414, "mtime": 1753073524638, "results": "255", "hashOfConfig": "140"}, {"size": 101004, "mtime": 1753419465228, "results": "256", "hashOfConfig": "140"}, {"size": 50318, "mtime": 1753238903009, "results": "257", "hashOfConfig": "140"}, {"size": 17961, "mtime": 1753243947743, "results": "258", "hashOfConfig": "140"}, {"size": 26298, "mtime": 1753233825587, "results": "259", "hashOfConfig": "140"}, {"size": 11417, "mtime": 1753246636198, "results": "260", "hashOfConfig": "140"}, {"size": 22348, "mtime": 1753174094001, "results": "261", "hashOfConfig": "140"}, {"size": 39046, "mtime": 1752915938285, "results": "262", "hashOfConfig": "140"}, {"size": 22681, "mtime": 1752896607276, "results": "263", "hashOfConfig": "140"}, {"size": 6224, "mtime": 1752810039250, "results": "264", "hashOfConfig": "140"}, {"size": 4946, "mtime": 1752810038985, "results": "265", "hashOfConfig": "140"}, {"size": 9831, "mtime": 1753257372495, "results": "266", "hashOfConfig": "140"}, {"size": 16061, "mtime": 1753171564241, "results": "267", "hashOfConfig": "140"}, {"size": 2383, "mtime": 1753147869645, "results": "268", "hashOfConfig": "140"}, {"size": 651, "mtime": 1753233236592, "results": "269", "hashOfConfig": "140"}, {"size": 8360, "mtime": 1753415733921, "results": "270", "hashOfConfig": "140"}, {"size": 22087, "mtime": 1753674437413, "results": "271", "hashOfConfig": "140"}, {"size": 4169, "mtime": 1753676734847, "results": "272", "hashOfConfig": "140"}, {"size": 3638, "mtime": 1753679852342, "results": "273", "hashOfConfig": "140"}, {"size": 5852, "mtime": 1753679705013, "results": "274", "hashOfConfig": "140"}, {"size": 17140, "mtime": 1753839056672, "results": "275", "hashOfConfig": "140"}, {"size": 6588, "mtime": 1753839080427, "results": "276", "hashOfConfig": "140"}, {"size": 2890, "mtime": 1753681001591, "results": "277", "hashOfConfig": "140"}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gd1jiv", {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\index.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\App.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\UploadsRedirect.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\context\\AuthContext.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\AssetManagement.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\Preloader.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\Navbar.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\ws.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\Register.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ContractPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\IncidentForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\WelcomePage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\Login.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\RequestForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ReturnForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ReportPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\EmployeeManagement.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\IncidentReports.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ApprovalDashboard.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SupportFeedbackForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\HumanResources.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SupportRequestsAdmin.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SupportRequestsUser.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\VehicleManagement.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SuperAdminDashboard.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\Reception.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\UserProfile.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ReturnReports.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\WorkReports.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\LeaveRequestForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AdminWorkManagement.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\LeaveRequest.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\DriverProfiles.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\UserGuide.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AllNotifications.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\api.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationBell.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\translation.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\CompanyDocuments.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\CompanyRegulations.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\socket.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\CreateUser.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\constants\\roles.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\EmployeeList.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AddVehicle.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AdminDriverManagementPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NewNotificationAlert.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\notificationService.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\ActivityLogManagement.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\FuelManagement.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\formatUtils.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\fuel\\FuelRecordForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\fuel\\FuelStatistics.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\config\\config.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleRepairHistory.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleInsurance.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\insuranceService.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\PageReloader.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\DriverAssignmentModal.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\HomeButton.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AdminNotificationPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\Breadcrumbs.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\PageHeader.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\AssignedDriverInfo.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\DriverSchedulePage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AllDriverSchedulesPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AnnouncementsPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\AdminAnnouncementsPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\PrivacyPolicy.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\DocumentViewer.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\SimpleDocumentViewer.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\AuthContext.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\TodoListPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\notificationUtils.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleDocuments.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleImages.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\CompanyDocumentsPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\VehicleIssues.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\ApiTest.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\EmployeeEvaluationPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\EvaluationForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\EvaluationList.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\employeeEvaluationService.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\EvaluationStats.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\ManagerSelector.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\Notifications.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\MultiManagerSelector.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\evaluation\\ManagerValidation.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\context\\PageVisibilityContext.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\pageVisibilityService.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\PageVisibilityManagement.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\context\\SystemStatusContext.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\MaintenanceMode.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\systemStatusService.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\SystemStatusManagement.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\admin\\OnlineUsersPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\WebSocketTracker.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\admin\\OnlineUsersWidget.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\OnlineUsersBadge.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\api.fixed.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\admin\\OnlineUsersMonitor.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\formatters.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\axiosConfig.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\NotificationSettings.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\serviceWorkerRegistration.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationManager.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\PushNotificationManager.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\NotificationSettingsPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationSettings.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\debugExports.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\utils\\pwaDetection.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\mobile\\MobileAppContainer.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\hooks\\usePWA.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\WhitelistStatusChecker.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\RecruitmentDashboardPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\CandidatesPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\InterviewsPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\RecruitmentRequestsPage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\services\\recruitmentService.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\CandidateKanban.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\RecruitmentDashboard.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\RecruitmentRequestForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\CandidateForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\CandidateDetail.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\RecruitmentDashboardSimple.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\recruitment\\TestRecruitmentAPI.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\RecruitmentRequestDetailModal.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\recruitment\\InterviewForm.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\RecruitmentNavigation.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NavigationHandler.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationTester.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\PushDebugStatus.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\NotificationNavigationHandler.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\ProfileGuard.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\pages\\CompleteProfilePage.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\components\\ProfileCompletion.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\hooks\\useProfileCompletion.js", [], [], "E:\\NEW ATLANTIC\\QUẢN LÝ XE LOCAL HOST\\frontend\\src\\hooks\\useCompanyDepartmentOptions.js", [], []]